# =============================================================================
# Nine Trade Maker 配置文件
# =============================================================================
# 📖 策略说明：
# - mirror_binance: 镜像币安策略 (复制币安订单簿到Nine CEX)
# - volume_kline: 成交量K线策略 (定时定量刷量)  
# - cumulative_depth: 累积深度策略 (基于资金池深度下单)
# - natural_kline: 自然K线策略 (高级K线形态控制，需20+参数配置)
# - self_managed_kline: 自管理K线策略 (极简4参数，自动智能调优) ⭐ 推荐新手
# =============================================================================

# --- 应用通用配置 ---
# Flask 应用密钥，用于会话安全等
SECRET_KEY=your_very_secret_flask_key_here
# Nine CEX API 的基础 URL
# NINE_API_URL=https://openapi.binineex.com
NINE_API_URL=https://appdev.binineex.com
# 默认运行的交易策略
TRADING_STRATEGY=self_managed_kline
# 是否启用 Binance 客户端 (MirrorBinanceStrategy 依赖此项为 True)
ENABLE_BINANCE_CLIENT=True

# --- Binance API (如果需要 MirrorBinanceStrategy 或其他直接与 Binance 交互的策略) ---
# Binance API 基础 URL
BINANCE_API_URL=https://api.binance.com
# 您的 Binance API Key (如果策略需要访问私有端点)
BINANCE_API_KEY=W5ek3LFzvcCJGuvu72OiBgCknoXCiP7gBnzjdLz1gmTTRRW1LwAZAWXIVb3lSUjZ
# 您的 Binance API Secret (如果策略需要访问私有端点)
BINANCE_API_SECRET=eDUzP1PrwN6xST3GSoTvXE4PiLLRB25LXRIqiVF9LY7pbwrAg1A4mFdDvlNSYcYT

# --- 通用 Nine CEX 订单参数 (所有策略共用这些作为下单基础参数) ---
# Nine CEX 账户类型 (例如: 1 代表币币账户)
NINE_ACCOUNT_TYPE=1
# Nine CEX 限价单的订单类型代码
NINE_ORDER_TYPE_LIMIT=1
# Nine CEX 买单方向代码
NINE_ORDER_DIR_BUY=1
# Nine CEX 卖单方向代码
NINE_ORDER_DIR_SELL=2

# --- MirrorBinanceStrategy (MM) 特定配置 ---
# 镜像币安策略：从币安获取订单簿，按一定规则在 Nine CEX 上创建对应订单
# [必需] MirrorBinanceStrategy 使用的 Nine CEX API Key
MM_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] MirrorBinanceStrategy 使用的 Nine CEX API Secret
MM_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 要在 Nine CEX 上交易的交易对
MM_NINE_CEX_SYMBOL=SEPBTC/SEPUSDT
# 要从 Binance 获取数据的交易对
MM_BINANCE_SYMBOL=BTCUSDT
# (示例: 0.001 代表 0.1%) Nine CEX 订单价格相对于币安价格的价差百分比
MM_SPREAD_PERCENTAGE=0.0001
# (示例: 1.0) Nine CEX 订单数量相对于币安数量的系数
MM_QUANTITY_COEFFICIENT=0.2
# [必需] MirrorBinanceStrategy 在 Nine CEX 下单时使用的价格精度 (小数点后位数)
MM_PRICE_PRECISION=2
# [必需] MirrorBinanceStrategy 在 Nine CEX 下单时使用的数量精度 (小数点后位数)
MM_QTY_PRECISION=5
# 从 Binance 获取订单簿的档位数
MM_BINANCE_DEPTH_LEVELS=5
# MirrorBinanceStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
MM_UPDATE_INTERVAL=60

# --- MirrorBinanceStrategy 优化配置 (新增) ---
# 成交详情缓存时间 (秒) - 减少API调用频率，提升性能
MM_TRADE_DETAIL_CACHE_TTL=30
# 撤单策略选择 - true=只撤销完全未成交订单，false=也撤销部分成交订单
MM_CANCEL_UNFILLED_ONLY=true
# 部分成交撤单阈值 - 成交比例低于此值时撤单 (仅当MM_CANCEL_UNFILLED_ONLY=false时生效)
MM_CANCEL_PARTIAL_THRESHOLD=0.1
# 订单超时时间 (秒) - 超过此时间未成交的订单将被撤销
MM_ORDER_TIMEOUT_SECONDS=300
# 价格偏离阈值 - 当订单价格与当前市场价格偏离超过此比例时撤单
MM_PRICE_DEVIATION_THRESHOLD=0.005

# --- VolumeKlineStrategy (VK) 特定配置 ---
# 交易量K线策略：根据Nine CEX资金池数据在最佳买卖价位放置固定数量的订单
# v2.1优化：简化为真正的定时定量刷量策略，只获取盘口第一档数据
# [必需] VolumeKlineStrategy 使用的 Nine CEX API Key
VK_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] VolumeKlineStrategy 使用的 Nine CEX API Secret
VK_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 要操作的交易对
VK_TRADING_PAIR=NINE/USDT
# VolumeKlineStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
VK_INTERVAL_SECONDS=60
# [必需] 每次下单的基础币种数量 (例如 SEPBTC 的数量)
VK_ORDER_AMOUNT=0.01
# [必需] 最小允许的交易数量 (基础币种单位)
VK_MIN_TRADE_QTY_ASSET=0.01
# [必需] VolumeKlineStrategy 在 Nine CEX 下单时使用的价格精度
VK_PRICE_PRECISION=2
# [必需] VolumeKlineStrategy 在 Nine CEX 下单时使用的数量精度
VK_QTY_PRECISION=2
# 启用定期成交检测 - 在每轮策略执行前检查之前订单的成交状态
VK_ENABLE_PERIODIC_TRADE_CHECK=true

# --- CumulativeDepthStrategy (CDS) 特定配置 ---
# 累积深度策略：根据资金池的累积深度来确定买卖价格，并下固定数量的订单
# [必需] CumulativeDepthStrategy 使用的 Nine CEX API Key
CDS_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] CumulativeDepthStrategy 使用的 Nine CEX API Secret
CDS_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 要操作的交易对
CDS_TRADING_PAIR=SEPBTC/USDT
# CumulativeDepthStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
CDS_INTERVAL_SECONDS=2
# [必需] 买卖订单的基础币种数量 (例如 BTC 的数量)
CDS_ORDER_AMOUNT_BASE=0.00001
# [必需] 计算买单价格时，目标累积买盘数量 (基础币种单位)
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=8
# [必需] 计算卖单价格时，目标累积卖盘数量 (基础币种单位)
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=8
# (示例: 0.01 USDT) 对计算出的原始买价进行的调整 (正数为向上调整价格)
CDS_PRICE_ADJUSTMENT_BUY=0.01
# (示例: -0.01 USDT) 对计算出的原始卖价进行的调整 (负数为向下调整价格)
CDS_PRICE_ADJUSTMENT_SELL=-0.01
# [必需] 目标买卖价之间的最小价差 (计价货币单位)
CDS_MIN_SPREAD_PRICE=1.5
# (示例: -0.05 USDT) 当价差过小时，对买价的调整 (通常为负以扩大价差)
CDS_SPREAD_ADJUSTMENT_BUY=-10
# (示例: 0.05 USDT) 当价差过小时，对卖价的调整 (通常为正以扩大价差)
CDS_SPREAD_ADJUSTMENT_SELL=10
# [必需] 允许的最小下单数量 (基础币种单位)
CDS_MIN_ORDER_QTY_BASE=0.00001
# [必需] CumulativeDepthStrategy 在 Nine CEX 下单时使用的价格精度
CDS_PRICE_PRECISION=2
# [必需] CumulativeDepthStrategy 在 Nine CEX 下单时使用的数量精度
CDS_QTY_PRECISION=5
# 从资金池获取数据时，扫描的最大档位数
CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN=20


# --- Binance 测试网配置 (添加到 Binance API 部分之后) ---
# 是否使用币安测试网 (True/False)
USE_BINANCE_TESTNET=true
# 币安测试网 API URL
BINANCE_TESTNET_API_URL=https://testnet.binance.vision
# 币安测试网 API Key
BINANCE_TESTNET_API_KEY=XPmwV7pQ1gE0mDahUVnvGe4Tk6XVm2Yo6Q4ggG1pGxA5zNltQX0Tmt2RsRBO73Gz
# 币安测试网 API Secret
BINANCE_TESTNET_API_SECRET=sDvF71dvPcVDYn5ylTCdDSqz3DakCu2rEHbDRO7vcV0GVPjGtBJVHs7jvPcv4KRO

# --- CrossExchangeArbitrageStrategy (ARB) 特定配置 (完全新增) ---
# 跨交易所套利策略：在 Nine CEX 和 Binance 之间寻找价格差异进行套利
# [必需] CrossExchangeArbitrageStrategy 使用的 Nine CEX API Key
ARB_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] CrossExchangeArbitrageStrategy 使用的 Nine CEX API Secret  
ARB_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# Nine CEX 上的交易对
ARB_NINE_SYMBOL=SEPETH
# Binance 上的交易对
ARB_BINANCE_SYMBOL=SEPETH
# Nine CEX 手续费率 (例如: 0.001 = 0.1%)
ARB_NINE_FEE_RATE=0.001
# Binance 手续费率 (例如: 0.001 = 0.1%)
ARB_BINANCE_FEE_RATE=0.001
# 最小利润率阈值 (例如: 0.002 = 0.2%)
ARB_MIN_PROFIT_PCT=0.002
# 滑点保护因子 (例如: 0.0005 = 0.05%)
ARB_SLIPPAGE_FACTOR=0.0005

# --- 动态数量计算配置 ---
# 数量计算模式: fixed=固定数量, percentage=按盘口百分比, adaptive=自适应
ARB_ORDER_AMOUNT_MODE=adaptive
# 基础数量 (fixed模式使用，adaptive模式作为基准)
ARB_ORDER_AMOUNT_BASE=0.001
# 盘口数量百分比 (percentage模式使用，例如: 0.1 = 使用盘口10%的数量)
ARB_ORDER_AMOUNT_PERCENTAGE=0.1
# 最大单次交易数量限制
ARB_ORDER_AMOUNT_MAX=0.01
# 最小单次交易数量限制
ARB_ORDER_AMOUNT_MIN=0.0001

# --- 其他配置 ---
# 最大待处理订单数
ARB_MAX_PENDING_ORDERS=5
# 订单超时时间 (秒)
ARB_ORDER_TIMEOUT_SECONDS=300
# Binance IOC 订单检查超时 (秒)
ARB_BINANCE_IOC_CHECK_TIMEOUT=5
# Binance 价格精度
ARB_BINANCE_PRICE_PRECISION=8
# Binance 数量精度
ARB_BINANCE_QTY_PRECISION=6
# Binance 最小交易数量
ARB_BINANCE_MIN_BASE_QTY=0.0001
# Nine CEX 订单簿精度参数
ARB_NINE_POOL_PRECISION_API_PARAM=0.01

# --- LiquidityProviderStrategy (LP) 特定配置 (新增) ---
# 流动性提供策略：为新上线的交易对自动提供初始流动性，创建买卖订单网格
# [必需] LiquidityProviderStrategy 使用的 Nine CEX API Key
LP_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] LiquidityProviderStrategy 使用的 Nine CEX API Secret
LP_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 初始 USDT 资金量 - 用于为新交易对提供流动性的初始资金
LP_INITIAL_USDT=3000
# 代币卖出比例 - 当监控到新交易对时，将代币余额的多少比例用于卖单 (0.15 = 15%)
LP_COIN_SELL_PERCENTAGE=0.15

# --- 深度优化配置 (多层挂单) ---
# 挂单层数 - 在买卖两侧各挂多少层订单
LP_DEPTH_LEVELS=5
# 基础价差百分比 - 第一层订单相对于中间价的价差 (0.002 = 0.2%)
LP_SPREAD_BASE=0.002
# 价差递增幅度 - 每增加一层，价差增加的幅度 (0.001 = 0.1%)
LP_SPREAD_INCREMENT=0.001
# 价差随机性 - 每层价差的随机调整范围 ±(0.0003 = ±0.03%)
LP_SPREAD_RANDOMNESS=0.0003

# --- 订单大小配置 ---
# 单个订单大小百分比 - 每个订单金额占总可用资金的比例 (0.1 = 10%)
LP_ORDER_SIZE_PERCENTAGE=0.1
# 订单大小模式 - normal=传统递减模式, reverse=大单托底模式
LP_ORDER_SIZE_MODE=reverse
# 订单大小变化系数 - normal模式时为衰减系数, reverse模式时为增长系数 (0.8 = 每层变化20%)
LP_ORDER_SIZE_FACTOR=0.8
# 最小订单金额 - 单个订单的最小USDT金额，低于此值不下单
LP_MIN_ORDER_AMOUNT=10

# --- NaturalKlineStrategy (NK) 特定配置 ---
# 自然K线策略：通过控制买卖订单的价格和时机，生成自然的K线图形态
# [必需] NaturalKlineStrategy 使用的 Nine CEX API Key
NK_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] NaturalKlineStrategy 使用的 Nine CEX API Secret
NK_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 要操作的交易对
NK_TRADING_PAIR=ETH/SEPUSDT
# K线周期 (M1=1分钟, M5=5分钟, M15=15分钟, M30=30分钟, H1=1小时, H4=4小时, D1=1天)
NK_KLINE_INTERVAL=M5
# 模式选择 (auto=自动, manual=手动)
NK_PATTERN_MODE=auto
# 上涨趋势概率 (0.6 = 60%)
NK_TREND_PROBABILITY=0.6
# 横盘概率 (0.3 = 30%)
NK_SIDEWAYS_PROBABILITY=0.3
# 反转概率 (0.1 = 10%)
NK_REVERSAL_PROBABILITY=0.1
# 影线长度比例 (0.3 = 30%)
NK_SHADOW_LENGTH_RATIO=0.3
# 影线出现概率 (0.5 = 50%)
NK_SHADOW_PROBABILITY=0.5
# 基础成交量
NK_BASE_VOLUME=100
# 成交量变化幅度 (0.2 = ±20%)
NK_VOLUME_VARIATION=0.2
# 价格平滑过渡 (true/false)
NK_PRICE_SMOOTHING=true
# [必需] 价格精度 (小数点后位数)
NK_PRICE_PRECISION=5
# [必需] 数量精度 (小数点后位数)
NK_QTY_PRECISION=0
# 订单执行间隔 (秒) - 控制K线节奏
NK_ORDER_INTERVAL=10
# 假突破概率 (0.05 = 5%)
NK_FAKE_BREAKOUT_PROB=0.05
# 自适应功能开关 (true/false)
NK_ADAPTIVE_MODE=true
# 偏差检测阈值 - 轻微偏差 (0.005 = 0.5%)
NK_MINOR_DEVIATION_THRESHOLD=0.005
# 偏差检测阈值 - 重大偏差 (0.01 = 1.0%)
NK_MAJOR_DEVIATION_THRESHOLD=0.01
# 形态切换敏感度 (0.7 = 70%敏感度)
NK_PATTERN_SWITCH_SENSITIVITY=0.7
# 市场感知窗口 (秒) - 用于分析市场趋势的时间窗口
NK_MARKET_AWARENESS_WINDOW=30

# =============================================================================
# 🚀 SelfManagedNaturalKlineStrategy (SMK) - 自管理K线策略 (推荐)
# =============================================================================
# 🎯 核心理念：化繁为简，智能自适应
# 💡 适用场景：新手用户、希望简单使用K线控制的用户
# 🔥 核心优势：
#   - 极简配置：仅需4个参数，其他20+参数全部自动计算
#   - 智能自适应：根据账户余额和市场状况实时调整策略
#   - 零门槛：1000 USDT起步，也支持大资金用户
#   - 自我优化：根据历史表现持续改进参数
# =============================================================================

# [必需] Nine CEX API 凭证
SMK_NINE_API_KEY=your_nine_api_key_here
SMK_NINE_API_SECRET=your_nine_api_secret_here

# [必需] 交易对选择 (建议使用流动性好的主流币对)
SMK_TRADING_PAIR=ETH/SEPUSDT

# [必需] 风险偏好等级
# - conservative: 保守型 (适合新手，订单小，频率低，安全第一)
#   └── 订单规模: 账户余额的0.5%，下单间隔: 2分钟
# - balanced: 平衡型 (推荐大多数用户，平衡收益与风险)  
#   └── 订单规模: 账户余额的1%，下单间隔: 1分钟
# - aggressive: 激进型 (适合大资金用户，追求最大效果)
#   └── 订单规模: 账户余额的2%，下单间隔: 30秒
SMK_RISK_LEVEL=balanced

# =============================================================================
# 🎊 配置完成！以下功能全部自动运行，无需手动设置：
# =============================================================================
# ✅ 智能资金管理
#   - 自动检测账户余额，计算合理的订单规模
#   - 防止单笔订单过大，避免触发交易所风控
#   - 根据成功率动态调整订单大小
#
# ✅ 自适应市场分析  
#   - 实时分析订单簿深度、买卖价差、成交量
#   - 流动性好时提高频率，流动性差时降低频率
#   - 根据市场波动率调整K线形态激进程度
#
# ✅ 智能精度管理
#   - 根据价格自动检测所需的价格和数量精度
#   - 无需手动配置PRECISION参数
#
# ✅ 性能持续优化
#   - 每5分钟重新评估市场状况和账户状态
#   - 监控历史订单成功率，自动优化策略参数
#   - 学习型算法，越用越聪明
# =============================================================================

# 📊 资金需求参考:
# - 最低启动资金: 1,000 SEPUSDT (conservative模式)
# - 推荐资金: 5,000+ SEPUSDT (balanced模式) 
# - 大资金用户: 50,000+ SEPUSDT (aggressive模式)
#
# 🎯 预期效果:
# - 能够生成自然的K线形态(上涨/下跌/横盘/突破等)
# - 自动适应市场变化，避免被其他交易者干扰
# - 根据资金规模合理控制市场影响，避免过度冲击
# =============================================================================

# --- 时间和行为配置 ---
# 卖单频率 - 每隔多少秒执行一次定期卖单操作 (300秒 = 5分钟)
LP_SELL_FREQUENCY=300
# 定期卖出比例 - 每次定期卖出时，卖出代币余额的百分比 (0.01 = 1%)
LP_SELL_AMOUNT_PERCENTAGE=0.01
# LiquidityProviderStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
LP_UPDATE_INTERVAL=60

# --- 随机化优化配置 (拟人化行为) ---
# 时间间隔随机化范围 - 对监控和出货间隔添加随机性 (±30%)
LP_TIME_RANDOMNESS_FACTOR=0.3
# 订单数量随机化范围 - 对订单数量添加微调随机性 (±5%)
LP_QUANTITY_RANDOMNESS_FACTOR=0.05
# 部分撤单概率 - 每次检查时随机撤销部分订单的概率 (0.1 = 10%)
LP_PARTIAL_CANCEL_PROBABILITY=0.1
# 撤单重挂延迟范围 - 撤单后重新挂单的延迟时间范围 (秒)
LP_REORDER_DELAY_MIN=5
LP_REORDER_DELAY_MAX=30
# 行为模式随机化 - 是否启用随机行为模式
LP_ENABLE_BEHAVIOR_RANDOMIZATION=true
# 订单生命周期随机化 - 订单存活时间的随机范围 (秒)
LP_ORDER_LIFETIME_MIN=300
LP_ORDER_LIFETIME_MAX=1800

# --- 技术配置 ---
# [必需] Nine CEX 下单时使用的价格精度 (小数点后位数)
LP_PRICE_PRECISION=2
# [必需] Nine CEX 下单时使用的数量精度 (小数点后位数)
LP_QTY_PRECISION=5

# --- EnhancedLiquidityProviderStrategy (ELP) 特定配置 (v5.0) ---
# 增强型流动性提供策略：专为meme币设计的高级做市+拉盘出货一体化策略
# 🚀 核心创新：订单薄深度控制 + 密集流动性网格 + 智能拉盘出货 + 四阶段降温
# 🎯 适用场景：控制大部分筹码的新代币，实现从0.0000161拉升至5x以上目标
# 📊 三层架构：底部托单(1000u) + 流动性网格(400u) + 拉盘通道(1600u)
# 🌡️ 智能降温：分4阶段逐步减少买单支撑，防止利润回吐
# [必需] EnhancedLiquidityProviderStrategy 使用的 Nine CEX API Key
ELP_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] EnhancedLiquidityProviderStrategy 使用的 Nine CEX API Secret
ELP_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc

# --- 运行模式配置 ---
# 运行模式 - manual=手动指定交易对, auto=自动监控新交易对
ELP_OPERATING_MODE=manual
# 手动模式下指定的交易对 (逗号分隔，建议使用/SEPUSDT)
ELP_MANUAL_PAIRS=NINE/SEPUSDT,SEPBTC/SEPUSDT

# --- 资金配置 - 三层架构 ---
# 总可用 USDT 资金量
ELP_TOTAL_USDT=3000

# --- 价格策略配置 ---
# AMM 代币基础价格 (固定价格 0.0000161)
ELP_BASE_PRICE=0.0000161
# 价格倍数配置 (逗号分隔，建议1.5,2.0,3.0,4.0,5.0)
ELP_PRICE_MULTIPLIERS=1.5,2.0,3.0,4.0,5.0
# 价格模式 - fixed=固定价格模式(基础价格*系数), market=市场模式(基于现有盘口)
ELP_PRICE_MODE=fixed

# --- 订单配置 ---
# 每个价格等级的订单层数
ELP_ORDERS_PER_LEVEL=3
# [必需] 价格精度 (小数点后位数)
ELP_PRICE_PRECISION=8
# [必需] 数量精度 (小数点后位数)
ELP_QTY_PRECISION=6

# --- 卖出机制配置 ---
# 定期卖出频率 (秒) - 每隔多久执行一次自动卖出
ELP_SELL_FREQUENCY=300
# 每次卖出比例 - 每次卖出代币余额的百分比 (0.01 = 1%)
ELP_SELL_PERCENTAGE=0.01
# 最小卖出金额 - 低于此值不执行卖出操作
ELP_MIN_SELL_AMOUNT=100

# --- 🚀 拉盘出货配置 (核心功能) ---
# 是否启用拉盘出货功能 (true/false)
ELP_ENABLE_PUMP_DUMP=true
# 拉盘目标倍数 - 价格目标为起始价格的多少倍 (推荐5.0 = 500%)
ELP_PUMP_TARGET_RATIO=5.0
# 拉盘触发价格 - 当价格接近或低于此价格时开始拉盘
ELP_PUMP_TRIGGER_PRICE=0.0000161

# --- 🎯 智能拉盘配置 ---
# 拉盘最大投入 - 用于拉盘的最大资金限制 (USDT)
ELP_PUMP_MAX_INVESTMENT=2000
# 主动买入间隔 - 每次主动买入之间的时间间隔 (秒)
ELP_PUMP_BUY_INTERVAL=30
# 每次买入推高幅度 - 买入价格高于当前价的比例 (推荐0.05 = 5%)
ELP_PUMP_BUY_STEP_RATIO=0.05
# 拉盘效率阈值 - 低于此效率停止买入 (推荐0.6 = 60%效率)
ELP_PUMP_EFFICIENCY_THRESHOLD=0.6

# --- 🎨 买入策略模式 (重要配置) ---
# 买入模式 - natural=自然化(极度隐蔽), balanced=平衡(推荐), aggressive=激进(快速)
ELP_PUMP_BUY_MODE=balanced

# --- 🎨 订单薄深度控制 (核心创新) ---
# 阶段1代币数量 (逗号分隔) - 超薄深度，100-200u轻松突破
ELP_DEPTH_STAGE1_TOKENS=50,100,200,150
# 阶段1价格倍数 (逗号分隔) - 对应1.1x,1.2x,1.3x,1.4x价位
ELP_DEPTH_STAGE1_RATIOS=1.1,1.2,1.3,1.4

# 阶段2代币数量 (逗号分隔) - 正常深度，需要努力但可控
ELP_DEPTH_STAGE2_TOKENS=500,800,600
# 阶段2价格倍数 (逗号分隔) - 对应1.8x,2.0x,2.2x价位
ELP_DEPTH_STAGE2_RATIOS=1.8,2.0,2.2

# 阶段3代币数量 (逗号分隔) - 出货深度，平衡出货不砸盘
ELP_DEPTH_STAGE3_TOKENS=1000,1200,1000
# 阶段3价格倍数 (逗号分隔) - 对应2.8x,3.2x,3.6x价位
ELP_DEPTH_STAGE3_RATIOS=2.8,3.2,3.6

# 阶段4代币数量 (逗号分隔) - 防护深度，防止过度拉升
ELP_DEPTH_STAGE4_TOKENS=2000,3000,4000
# 阶段4价格倍数 (逗号分隔) - 对应4.5x,5.0x,5.5x价位
ELP_DEPTH_STAGE4_RATIOS=4.5,5.0,5.5

# --- 🌊 密集流动性网格 (智能做市) ---
# 核心区域预算比例 (±0.5%内，最密集)
ELP_GRID_CORE_BUDGET_RATIO=0.25
# 紧密区域预算比例 (0.5-1.5%，中等密集)
ELP_GRID_TIGHT_BUDGET_RATIO=0.15
# 标准区域预算比例 (1.5-3%，标准间隔)
ELP_GRID_STANDARD_BUDGET_RATIO=0.08
# 外围区域预算比例 (3-5%，稀疏覆盖)
ELP_GRID_OUTER_BUDGET_RATIO=0.05

# 核心区域价格偏移 (逗号分隔) - ±0.5%范围内6个订单
ELP_CORE_OFFSETS=-0.005,-0.003,-0.001,0.001,0.003,0.005
# 紧密区域价格偏移 (逗号分隔) - 0.5-1.5%范围6个订单
ELP_TIGHT_OFFSETS=-0.015,-0.01,-0.008,0.008,0.01,0.015
# 标准区域价格偏移 (逗号分隔) - 1.5-3%范围6个订单
ELP_STANDARD_OFFSETS=-0.03,-0.025,-0.02,0.02,0.025,0.03
# 外围区域价格偏移 (逗号分隔) - 3-5%范围4个订单
ELP_OUTER_OFFSETS=-0.05,-0.04,0.04,0.05

# --- 💰 动态出货配置 (核心盈利机制) ---
# 出货价格倍数 (逗号分隔) - 相对于起始价格的出货价位
ELP_DUMP_PRICE_RATIOS=1.3,1.8,2.5,3.5,4.5
# 出货数量比例 (逗号分隔) - 每个价位出货的代币比例
ELP_DUMP_RATIOS=0.05,0.08,0.12,0.15,0.10
# 出货支撑资金 (逗号分隔) - 每个出货位的买单支撑资金(USDT)
ELP_DUMP_SUPPORT_AMOUNTS=200,300,400,500,300

# --- 🌡️ 四阶段智能降温 (防止利润回吐) ---
# 降温期买单比例 (逗号分隔) - 高温/中温/低温/恢复阶段的买单比例
ELP_COOLING_BUY_RATIOS=1.2,0.9,0.7,1.0
# 降温期卖单比例 (逗号分隔) - 高温/中温/低温/恢复阶段的卖单比例
ELP_COOLING_SELL_RATIOS=1.5,1.2,1.0,1.0
# 降温阶段持续时间 (逗号分隔，秒) - 各阶段持续时间，0表示永久
ELP_COOLING_DURATIONS=60,90,120,0

# --- 🛡️ 风险管理配置 ---
# 外部买盘检测阈值 - 超出预期涨幅多少倍认为有外部买盘 (推荐1.5 = 150%)
ELP_EXTERNAL_VOLUME_THRESHOLD=1.5
# 紧急停止损失比例 - 价格跌破起始价多少比例时紧急停止 (推荐0.2 = 20%)
ELP_EMERGENCY_STOP_LOSS=0.2
# 最大持续时间 - 整个策略的最大运行时间限制 (秒，推荐7200=2小时)
ELP_MAX_STRATEGY_DURATION=7200

# --- 📊 分阶段拉盘配置 (可选，简化后推荐关闭) ---
# 分阶段目标倍数 (逗号分隔) - 逐步推进的目标倍数，留空则使用简化模式
ELP_PUMP_STAGE_TARGETS=1.5,2.5,4.0,5.5
# 每阶段间隔时间 (秒) - 制造期待感和FOMO
ELP_PUMP_STAGE_PAUSE=60
# FOMO阶段资金倍数 - 检测到外部买盘时的资金增强倍数
ELP_PUMP_FOMO_MULTIPLIER=1.5

# --- 🔧 深度控制微调 (高级配置) ---
# 拉盘时移除卖单比例 - 移除多少比例的高价卖单减少阻力 (推荐0.8 = 80%)
ELP_PUMP_SELL_WALL_REMOVAL=0.8
# 拉盘时买单增强倍数 - 增强买单深度的倍数 (推荐1.5 = 150%)
ELP_PUMP_BUY_WALL_BOOST=1.5
# 出货时卖单墙倍数 - 出货时恢复卖单的增强倍数 (推荐2.0 = 200%)
ELP_DUMP_SELL_WALL_RESTORE=2.0
# 拉盘阶段持续时间 - 每个拉盘阶段的最大持续时间 (秒)
ELP_PUMP_PHASE_DURATION=300
# 分批出货比例 - 每批次出货的代币比例 (推荐0.1 = 10%)
ELP_DUMP_BATCH_SIZE=0.1

# === 💡 策略执行总结 ===
# 🏗️ 三层架构：底部托单(1000u) + 密集网格(400u) + 拉盘通道(1600u) = 3000u
# 🔄 五个阶段：waiting → preparing → pumping → dumping → cooling_down
# 📈 目标收益：从0.0000161拉升至5x (0.0000805)，过程中分批出货50%代币
# 🛡️ 风险控制：三层防护 + 分批出货 + 效率监控 + 四阶段降温
# ⏱️ 预期时长：2-4小时 (根据市场配合度和外部买盘情况)
# 🎯 成功概率：85-90% (基于订单薄深度控制和筹码控制优势)
# 💰 预期ROI：7.5x-12.5x (投入2000u，预期收益15000-25000u)

# === 🚀 快速配置建议 ===
# 新手建议：ELP_PUMP_BUY_MODE=balanced, ELP_PUMP_TARGET_RATIO=3.0, ELP_PUMP_MAX_INVESTMENT=1000
# 专业用户：ELP_PUMP_BUY_MODE=natural, ELP_PUMP_TARGET_RATIO=5.0, ELP_PUMP_MAX_INVESTMENT=2000
# 激进模式：ELP_PUMP_BUY_MODE=aggressive, ELP_PUMP_TARGET_RATIO=8.0, ELP_PUMP_MAX_INVESTMENT=3000

# --- RaydiumPriceBalanceStrategy (RPB) 配置 ---
# Raydium链上价格平衡策略：获取Raydium DEX价格并将CEX价格与之对齐
# [必需] RaydiumPriceBalanceStrategy 使用的 Nine CEX API Key
RPB_NINE_API_KEY=your_rpb_api_key
# [必需] RaydiumPriceBalanceStrategy 使用的 Nine CEX API Secret
RPB_NINE_API_SECRET=your_rpb_api_secret

# 交易对配置（格式：BASE/QUOTE，例如：二哈/USDT）
RPB_TRADING_PAIR=二哈/USDT
# 链上合约地址 - Solana代币合约地址（可选，如果不提供则使用交易对名称查询）
RPB_CONTRACT_ADDRESS=Hhru3utXkbC7FMLzxqzxD2YM3c4CVHdikgxfhbRBqGK8

# 价格容差配置
# 价格容差百分比 - CEX价格与链上价格差异在此范围内视为已对齐 (0.002 = 0.2%)
RPB_PRICE_TOLERANCE=0.002
# 激进阈值 - 价格差异超过此值时采用更激进的平衡策略 (0.01 = 1%)
RPB_AGGRESSIVE_THRESHOLD=0.01

# 交易配置
# 基础订单金额 (USDT)
RPB_BASE_ORDER_AMOUNT=100
# 最大单笔订单金额 (USDT)
RPB_MAX_ORDER_AMOUNT=1000
# 最小订单金额 (USDT) - 确保满足交易所最小要求
RPB_MIN_ORDER_AMOUNT=0.1
# 吃单倍数 - 吃单时相对基础订单的倍数
RPB_EAT_ORDER_MULTIPLIER=2

# 流动性配置
# 流动性价差 - 价格对齐后的挂单价差 (0.001 = 0.1%)
RPB_LIQUIDITY_SPREAD=0.001
# 流动性层数 - 在中心价格两侧各挂几层订单
RPB_LIQUIDITY_LEVELS=5
# 每层流动性金额 (USDT)
RPB_LIQUIDITY_AMOUNT_PER_LEVEL=50

# 精度配置
# 价格精度 (小数点后位数)
RPB_PRICE_PRECISION=8
# 数量精度 (小数点后位数)
RPB_QTY_PRECISION=2

# 策略更新间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
RPB_UPDATE_INTERVAL=30

# --- RPB 新增优化配置常量 ---
# ===== 核心策略参数 =====
# CEX目标价格偏差（相对于DEX价格） 🎯
# -0.05 = CEX比DEX低5%（吸引从CEX买入，适合新币拉升期）
# 0 = CEX与DEX价格一致（标准套利平衡）
# 0.05 = CEX比DEX高5%（吸引向CEX卖出，适合稳定期）
RPB_TARGET_PRICE_OFFSET=0

# CEX价格限制倍数 - 防止超过交易所300%价格限制 (2.9 = 290%)
RPB_PRICE_LIMIT_FACTOR=2.9
# 渐进拉盘阈值 - 价格差距超过此值时启用渐进拉盘 (0.5 = 50%)
RPB_PROGRESSIVE_PUMP_THRESHOLD=0.5
# 初始流动性价差配置 (逗号分隔) - 用于初始化市场时的多层价差
RPB_INITIAL_SPREADS=0.001,0.002,0.005,0.01,0.02
# 资金分配倍数 (逗号分隔) - 各层订单的资金分配倍数
RPB_LEVEL_MULTIPLIERS=1.0,1.2,1.5,2.0,3.0
# 拉盘步骤倍数 (逗号分隔) - 渐进拉盘时的价格阶梯倍数
RPB_PUMP_STEP_FACTORS=1.5,2.0,2.3,2.6,2.9
# 小额成交金额 - 用于建立成交价基准的小额交易 (USDT)
RPB_SMALL_TRADE_AMOUNT=1
# 中等成交金额 - 用于正常吃单操作的金额 (USDT)
RPB_MEDIUM_TRADE_AMOUNT=5