#!/usr/bin/env python3
"""
Nine CEX 多账户大规模并发交易测试 - 主入口点

基于现有的Nine CEX并发测试架构，实现多账户大规模并发交易测试功能：

**数据源和账户配置：**
- 使用 data/accounts.json 中的300个账户作为测试数据源
- 使用 MM_NINE_API_KEY 作为市商账户，300个账户作为买方账户

**交易逻辑设计：**
- MM账户：创建流动性卖单，数量为 0.11 USDT * 测试规模
- 买方账户：每个账户创建 0.11 USDT 的买单
- 价格匹配：确保买单价格 >= 卖单价格以保证成功执行

**并发规模支持：**
- 小规模：100个账户并发
- 中规模：200个账户并发  
- 大规模：300个账户并发（使用全部可用账户）

使用方法:
    python tests/run_multi_account_test.py --scale multi_small
    python tests/run_multi_account_test.py --scale multi_medium
    python tests/run_multi_account_test.py --scale multi_large
    python tests/run_multi_account_test.py --scale multi_all
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv(project_root / '.env')

from app.services.nine_client import NineClient
from app.services.trading_pair_manager import TradingPairManager
from tests.performance.test_multi_account_concurrent import MultiAccountConcurrentTest
from tests.utils.account_manager import AccountManager
from tests.config.test_scales import (
    TestScale, TestScaleConfig, get_test_config, get_all_multi_account_configs,
    parse_scale_from_string, get_scale_summary
)


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,  # 恢复INFO级别
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('tests/logs/multi_account_test.log')
        ]
    )

    # 减少第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Nine CEX 多账户大规模并发交易测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
多账户测试规模说明:
{get_scale_summary()}

使用示例:
  python tests/run_multi_account_test.py                           # 运行大规模多账户测试（默认）
  python tests/run_multi_account_test.py --scale multi_small      # 运行100账户测试
  python tests/run_multi_account_test.py --scale multi_medium     # 运行200账户测试
  python tests/run_multi_account_test.py --scale multi_large      # 运行300账户测试
  python tests/run_multi_account_test.py --scale multi_all        # 运行所有多账户规模测试
  python tests/run_multi_account_test.py --validate-accounts      # 验证账户数据
        """
    )

    parser.add_argument(
        '--scale', '-s',
        type=str,
        default='multi_large',
        help='多账户测试规模 (multi_small/multi_medium/multi_large/multi_all，默认: multi_large)'
    )

    parser.add_argument(
        '--validate-accounts', '-v',
        action='store_true',
        help='验证账户数据文件并显示统计信息'
    )

    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='仅显示测试配置，不执行实际测试'
    )

    parser.add_argument(
        '--accounts-file', '-f',
        type=str,
        default='data/accounts.json',
        help='账户数据文件路径（默认: data/accounts.json）'
    )

    return parser.parse_args()


def validate_environment():
    """验证环境变量"""
    required_vars = [
        'MM_NINE_API_KEY',
        'MM_NINE_API_SECRET', 
        'NINE_API_URL'
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境变量验证通过")
    return True


def validate_accounts_data(accounts_file: str):
    """验证账户数据"""
    try:
        account_manager = AccountManager(accounts_file)
        
        print(f"📊 账户数据验证结果:")
        print(f"   📁 文件路径: {accounts_file}")
        print(f"   📈 总账户数: {account_manager.get_total_accounts()}")
        print(f"   ✅ 可用账户: {account_manager.get_available_accounts()}")
        
        # 验证前100个账户
        if account_manager.get_total_accounts() >= 100:
            test_accounts = account_manager.allocate_accounts(100, strategy="sequential")
            validation_result = account_manager.validate_accounts(test_accounts)
            
            print(f"   🔍 验证结果:")
            print(f"      ✅ 有效账户: {validation_result['valid_accounts']}/100")
            print(f"      ❌ 无效账户: {len(validation_result['invalid_accounts'])}")
            print(f"      🔄 重复密钥: {len(validation_result['duplicate_keys'])}")
            print(f"      📊 验证通过: {'✅' if validation_result['validation_passed'] else '❌'}")
            
            if validation_result['invalid_accounts']:
                print(f"   ⚠️ 无效账户详情:")
                for invalid in validation_result['invalid_accounts'][:5]:  # 显示前5个
                    print(f"      - 账户{invalid['index']}: {invalid['reason']}")
                if len(validation_result['invalid_accounts']) > 5:
                    print(f"      ... 还有 {len(validation_result['invalid_accounts']) - 5} 个无效账户")
            
            # 释放测试账户
            account_manager.release_accounts(test_accounts)
        
        return True
        
    except Exception as e:
        print(f"❌ 账户数据验证失败: {e}")
        return False


def create_clients():
    """创建客户端实例"""
    try:
        api_key = os.getenv('MM_NINE_API_KEY')
        api_secret = os.getenv('MM_NINE_API_SECRET')
        api_url = os.getenv('NINE_API_URL')

        nine_client = NineClient(api_key=api_key, secret=api_secret, api_url=api_url)
        trading_pair_manager = TradingPairManager(nine_client)
        print("✅ MM账户客户端创建完成")
        return nine_client, trading_pair_manager
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return None, None


async def run_single_multi_account_test(config: TestScaleConfig) -> Dict[str, Any]:
    """运行单个多账户测试"""
    logger = logging.getLogger(__name__)

    logger.info(f"🎯 开始{config.display_name}...")
    logger.info(f"📊 测试配置: {config.num_accounts}账户, 全量并发, 预期{config.expected_duration}秒")
    logger.info(f"🎯 测试目的: {config.purpose}")
    logger.info(f"💰 每单价值: {config.order_value_usdt} USDT")
    logger.info(f"💧 流动性倍数: {config.liquidity_multiplier}x")

    # 1. 验证环境
    logger.info("✅ 环境变量验证通过")

    # 2. 创建MM账户客户端
    api_key = os.getenv('MM_NINE_API_KEY')
    api_secret = os.getenv('MM_NINE_API_SECRET')
    api_url = os.getenv('NINE_API_URL')

    nine_client = NineClient(api_key=api_key, secret=api_secret, api_url=api_url)
    trading_pair_manager = TradingPairManager(nine_client)
    test_instance = MultiAccountConcurrentTest(nine_client, trading_pair_manager)
    logger.info("✅ 多账户测试实例创建完成")

    # 3. 验证订单价值
    logger.info("✅ 订单价值验证:")
    logger.info(f"   💰 每单价值: {config.order_value_usdt} USDT")
    logger.info(f"   📊 总交易价值: {config.num_accounts * config.order_value_usdt} USDT")
    logger.info(f"   ✅ 最小要求: 0.1 USDT")

    try:
        # 4. 运行多账户并发测试
        logger.info(f"🚀 开始{config.display_name}...")
        logger.info(f"📊 运行{config.num_accounts}账户测试: {config.num_accounts}个买方账户 + 1个MM流动性账户")
        logger.info(f"🎯 目标: {config.description}")

        result = await test_instance.run_multi_account_concurrent_test(config)

        # 5. 生成多账户专用报告
        logger.info(f"\n📄 生成{config.display_name}性能分析报告...")

        try:
            # 分析性能数据
            performance_summary = test_instance.performance_analyzer.analyze_performance()

            # 生成多账户专用CSV报告
            order_details_file, summary_file = test_instance.performance_analyzer.generate_multi_account_csv_reports(
                performance_summary, config
            )

            if order_details_file and summary_file:
                logger.info(f"✅ 多账户订单详情报告: {order_details_file}")
                logger.info(f"✅ 多账户性能摘要报告: {summary_file}")

        except Exception as e:
            logger.error(f"❌ {config.display_name}报告生成失败: {e}")

        logger.info(f"✅ {config.display_name}完成!")

        return {
            'config': config,
            'result': result,
            'performance_summary': performance_summary if 'performance_summary' in locals() else None,
            'reports': {
                'order_details': order_details_file if 'order_details_file' in locals() else None,
                'summary': summary_file if 'summary_file' in locals() else None
            }
        }

    except Exception as e:
        logger.error(f"❌ {config.display_name}执行失败: {e}")
        raise


async def run_multi_account_all_tests() -> bool:
    """运行所有多账户测试并生成对比分析"""
    logger = logging.getLogger(__name__)

    logger.info("🎯 开始所有多账户并发交易测试...")
    logger.info("📊 将依次运行100、200、300账户规模测试")

    all_results = []

    try:
        # 运行所有多账户测试
        for config in get_all_multi_account_configs():
            logger.info(f"\n{'='*80}")
            logger.info(f"🚀 开始 {config.display_name} ({config.name})")
            logger.info(f"{'='*80}")

            result = await run_single_multi_account_test(config)
            all_results.append(result)

            logger.info(f"✅ {config.display_name} 完成")

        # 生成对比分析报告
        logger.info(f"\n{'='*80}")
        logger.info("📊 生成多账户规模对比分析报告...")
        logger.info(f"{'='*80}")

        generate_multi_account_comparison_report(all_results)

        logger.info("🎉 所有多账户测试全部完成!")
        return True

    except Exception as e:
        logger.error(f"❌ 多账户测试失败: {e}")
        return False


def generate_multi_account_comparison_report(all_results: List[Dict[str, Any]]):
    """生成多账户规模对比分析报告"""
    logger = logging.getLogger(__name__)

    try:
        from datetime import datetime
        import csv

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_file = f"tests/reports/multi_account_comparison_{timestamp}.csv"

        os.makedirs("tests/reports", exist_ok=True)

        with open(comparison_file, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入报告头部
            csvfile.write("# Nine CEX 多账户规模对比分析报告\n")
            csvfile.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"# 测试规模: {len(all_results)} 种配置\n")
            csvfile.write("#\n")

            writer = csv.writer(csvfile)

            # 写入对比数据头
            headers = [
                '测试规模', '账户数量', '总交易价值(USDT)', '成功率(%)',
                '平均响应时间(ms)', '吞吐量(订单/秒)', '测试时长(秒)',
                '并发模式', '状态评估'
            ]
            writer.writerow(headers)

            # 写入每个测试的结果
            for test_result in all_results:
                config = test_result['config']
                result = test_result['result']
                performance = test_result.get('performance_summary')

                # 计算状态评估
                success_rate = result.get('success_rate', 0)
                avg_response = result.get('avg_response_time', 0)

                if success_rate >= 0.95 and avg_response < 2.0:
                    status = '🟢 优秀'
                elif success_rate >= 0.8 and avg_response < 3.0:
                    status = '🟡 良好'
                else:
                    status = '🔴 需优化'

                writer.writerow([
                    config.display_name,
                    config.num_accounts,
                    f"{config.num_accounts * config.order_value_usdt:.2f}",
                    f"{success_rate * 100:.1f}",
                    f"{avg_response * 1000:.1f}",
                    f"{result.get('throughput_orders_per_second', 0):.2f}",
                    f"{result.get('test_duration', 0):.1f}",
                    "全量并发",
                    status
                ])

            # 写入分析建议
            csvfile.write("\n# 📊 规模对比分析\n")

            # 找出最佳性能配置
            best_throughput = max(all_results, key=lambda x: x['result'].get('throughput_orders_per_second', 0))
            best_success_rate = max(all_results, key=lambda x: x['result'].get('success_rate', 0))

            csvfile.write(f"# 🏆 最佳吞吐量: {best_throughput['config'].display_name} ({best_throughput['result'].get('throughput_orders_per_second', 0):.2f} 订单/秒)\n")
            csvfile.write(f"# 🎯 最佳成功率: {best_success_rate['config'].display_name} ({best_success_rate['result'].get('success_rate', 0):.1%})\n")

            # 规模建议
            if len(all_results) >= 3:
                csvfile.write(f"# 💡 建议生产规模: 基于测试结果，推荐使用 {best_throughput['config'].num_accounts} 账户配置\n")

        logger.info(f"✅ 多账户规模对比报告: {comparison_file}")

    except Exception as e:
        logger.error(f"❌ 对比报告生成失败: {e}")


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    # 解析参数
    args = parse_arguments()

    print("🚀 Nine CEX 多账户大规模并发交易测试工具")
    print("=" * 60)

    # 验证账户数据
    if args.validate_accounts:
        print("🔍 验证账户数据...")
        if validate_accounts_data(args.accounts_file):
            print("✅ 账户数据验证完成")
        else:
            print("❌ 账户数据验证失败")
        return

    # 验证环境
    if not validate_environment():
        print("❌ 环境验证失败，请检查环境变量配置")
        return

    # 解析测试规模
    try:
        if args.scale == 'multi_all':
            # 运行所有多账户测试
            if args.dry_run:
                print("🔍 多账户测试配置预览:")
                for config in get_all_multi_account_configs():
                    print(f"   📊 {config.display_name}: {config.num_accounts}账户, 全量并发")
                print("✅ 配置预览完成（--dry-run模式）")
                return

            success = await run_multi_account_all_tests()
            if success:
                print("🎉 所有多账户测试完成!")
            else:
                print("❌ 多账户测试失败")
                sys.exit(1)
        else:
            # 运行单个测试
            scale_enum = parse_scale_from_string(args.scale)
            config = get_test_config(scale_enum)

            if args.dry_run:
                print("🔍 测试配置预览:")
                print(f"   📊 {config.display_name}")
                print(f"   👥 账户数量: {config.num_accounts}")
                print(f"   💰 每单价值: {config.order_value_usdt} USDT")
                print(f"   📦 并发模式: 全量并发")
                print(f"   ⏱️ 预期时长: {config.expected_duration}秒")
                print(f"   🎯 测试目的: {config.purpose}")
                print("✅ 配置预览完成（--dry-run模式）")
                return

            result = await run_single_multi_account_test(config)

            if result:
                print(f"🎉 {config.display_name}完成!")
                print(f"📊 成功率: {result['result'].get('success_rate', 0):.1%}")
                print(f"⚡ 吞吐量: {result['result'].get('throughput_orders_per_second', 0):.2f} 订单/秒")
            else:
                print(f"❌ {config.display_name}失败")
                sys.exit(1)

    except ValueError as e:
        print(f"❌ 参数错误: {e}")
        print("💡 使用 --help 查看可用的测试规模选项")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs("tests/logs", exist_ok=True)
    os.makedirs("tests/reports", exist_ok=True)

    # 运行主函数
    asyncio.run(main())
