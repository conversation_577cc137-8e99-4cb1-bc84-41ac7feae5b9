"""
Nine Trade Maker 测试框架

这个测试框架专门为Nine CEX交易所设计，提供全面的功能和性能测试。

测试模块结构：
- unit/: 单元测试，测试单个组件的功能
- integration/: 集成测试，测试组件间的交互
- performance/: 性能测试，测试系统的性能和并发能力
- fixtures/: 测试数据和配置
- utils/: 测试工具类和辅助函数

使用方法：
    # 运行所有测试
    python -m pytest tests/
    
    # 运行特定模块测试
    python -m pytest tests/unit/
    python -m pytest tests/integration/
    python -m pytest tests/performance/
    
    # 运行特定测试文件
    python -m pytest tests/performance/test_batch_orders.py
    
    # 运行性能测试并生成报告
    python tests/run_performance_tests.py
"""

__version__ = "1.0.0"
__author__ = "Nine Trade Maker Team"

# 测试框架配置
TEST_CONFIG = {
    "default_timeout": 30,
    "max_retries": 3,
    "performance_test_duration": 60,
    "max_orders_limit": 100,  # Nine CEX平台限制
    "concurrent_limit": 50
}
