[tool:pytest]
# pytest 配置文件

# 测试发现
testpaths = .
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试（运行时间超过5秒）
    batch: 批量操作测试
    concurrent: 并发测试
    stress: 压力测试
    smoke: 冒烟测试

# 最小版本要求
minversion = 6.0

# 测试目录
norecursedirs = .git .tox dist build *.egg

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
