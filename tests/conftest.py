"""
pytest 配置文件

提供全局的测试配置、fixtures和钩子函数
"""

import pytest
import os
import sys
import logging
from typing import Dict, Any, Optional
from decimal import Decimal
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

from app import create_app
from app.services.nine_client import NineClient
from app.services.binance_client import BinanceClient
from app.services.trading_pair_manager import TradingPairManager


def pytest_configure(config):
    """pytest 配置钩子"""
    # 设置测试日志级别
    logging.getLogger().setLevel(logging.WARNING)
    
    # 添加自定义标记
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "batch: 批量操作测试")
    config.addinivalue_line("markers", "concurrent: 并发测试")


@pytest.fixture(scope="session")
def test_app():
    """创建测试应用实例"""
    app = create_app()
    app.config['TESTING'] = True
    
    with app.app_context():
        yield app


@pytest.fixture(scope="session")
def test_config(test_app):
    """获取测试配置"""
    return test_app.config


@pytest.fixture(scope="function")
def nine_client(test_config):
    """创建Nine CEX客户端实例"""
    return NineClient(
        api_key=test_config.get('MM_NINE_API_KEY'),
        secret=test_config.get('MM_NINE_API_SECRET'),
        api_url=test_config.get('NINE_API_URL')
    )


@pytest.fixture(scope="function")
def binance_client(test_config):
    """创建Binance客户端实例"""
    return BinanceClient(
        api_key=test_config.get('BINANCE_API_KEY'),
        api_secret=test_config.get('BINANCE_API_SECRET')
    )


@pytest.fixture(scope="function")
def trading_pair_manager(nine_client):
    """创建交易对管理器实例"""
    logger = logging.getLogger('test')
    return TradingPairManager(nine_client, logger)


@pytest.fixture(scope="function")
def test_logger():
    """创建测试日志记录器"""
    logger = logging.getLogger('test')
    logger.setLevel(logging.INFO)
    
    # 如果没有处理器，添加一个
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


@pytest.fixture(scope="function")
def sample_trading_pair():
    """提供测试用的交易对（从环境变量读取）"""
    from tests.fixtures.test_config import TestConfig
    trading_pair = TestConfig.get_test_trading_pair()
    return trading_pair["name"]


@pytest.fixture(scope="function")
def sample_order_data():
    """提供测试用的订单数据（从环境变量读取）"""
    from tests.fixtures.test_config import TestConfig
    trading_pair = TestConfig.get_test_trading_pair()
    return {
        "accountType": 1,
        "tradePairName": trading_pair["name"],
        "tradePairId": trading_pair["id"],
        "orderDirection": 1,  # 买单
        "orderType": 1,  # 限价单
        "orderQuantity": "1",
        "orderPrice": "2500.00000"
    }


@pytest.fixture(scope="function")
def performance_config():
    """性能测试配置（从环境变量读取）"""
    from tests.fixtures.test_config import TestConfig
    timeout_config = TestConfig.get_timeout_config()
    batch_config = TestConfig.get_batch_config()
    concurrent_config = TestConfig.get_concurrent_config()
    performance_config = TestConfig.get_performance_config()
    retry_config = TestConfig.get_retry_config()

    return {
        "batch_sizes": [1, 5, 10, 20, 50, batch_config["max_size"]],
        "concurrent_levels": [1, 2, 5, 10, 20, concurrent_config["max_requests"]],
        "test_duration": performance_config["duration"],
        "timeout": timeout_config["default"],
        "max_retries": retry_config["max_retries"],
        "warmup_requests": performance_config["warmup_requests"]
    }


# 测试环境检查
def pytest_runtest_setup(item):
    """测试运行前的设置检查"""
    # 检查是否有必要的环境变量
    required_env_vars = ['MM_NINE_API_KEY', 'MM_NINE_API_SECRET']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars and 'integration' in [mark.name for mark in item.iter_markers()]:
        pytest.skip(f"缺少必要的环境变量: {missing_vars}")
