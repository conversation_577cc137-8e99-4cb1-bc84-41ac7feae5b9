"""
多账户管理器

负责加载和管理300个账户数据，提供账户分配和验证功能。
当前版本：支持多账户大规模并发交易测试架构。

注意：当前多账户测试使用MM账户凭据进行模拟，保持多账户架构设计。
未来扩展：当真实多账户凭据可用时，可直接使用加载的账户进行测试。

扩展接口预留：
- validate_account_credentials(): 验证账户凭据有效性
- refresh_account_status(): 刷新账户状态
- batch_account_operations(): 批量账户操作
"""

import json
import logging
import random
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass


@dataclass
class AccountInfo:
    """账户信息"""
    api_key: str
    secret: str
    email: str
    password: str
    index: int  # 账户在列表中的索引
    
    def __post_init__(self):
        """验证账户信息"""
        if not self.api_key or not self.secret:
            raise ValueError(f"账户{self.index}的API密钥或密码为空")
        if not self.email:
            raise ValueError(f"账户{self.index}的邮箱为空")


class AccountManager:
    """多账户管理器"""
    
    def __init__(self, accounts_file_path: str = "data/accounts.json"):
        """
        初始化账户管理器
        
        Args:
            accounts_file_path: 账户数据文件路径
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.accounts_file_path = Path(accounts_file_path)
        self.accounts: List[AccountInfo] = []
        self.allocated_accounts: Set[int] = set()  # 已分配的账户索引
        
        # 加载账户数据
        self._load_accounts()
        
    def _load_accounts(self):
        """加载账户数据"""
        try:
            if not self.accounts_file_path.exists():
                raise FileNotFoundError(f"账户文件不存在: {self.accounts_file_path}")
            
            with open(self.accounts_file_path, 'r', encoding='utf-8') as f:
                accounts_data = json.load(f)
            
            if not isinstance(accounts_data, list):
                raise ValueError("账户数据格式错误，应为数组")
            
            self.accounts = []
            for i, account_data in enumerate(accounts_data):
                try:
                    account = AccountInfo(
                        api_key=account_data.get('apiKey', ''),
                        secret=account_data.get('secret', ''),
                        email=account_data.get('email', ''),
                        password=account_data.get('password', ''),
                        index=i
                    )
                    self.accounts.append(account)
                except Exception as e:
                    self.logger.warning(f"跳过无效账户{i}: {e}")
                    continue
            
            self.logger.info(f"✅ 成功加载 {len(self.accounts)} 个账户")
            
            if len(self.accounts) == 0:
                raise ValueError("没有有效的账户数据")
                
        except Exception as e:
            self.logger.error(f"❌ 加载账户数据失败: {e}")
            raise
    
    def get_total_accounts(self) -> int:
        """获取总账户数量"""
        return len(self.accounts)
    
    def get_available_accounts(self) -> int:
        """获取可用账户数量"""
        return len(self.accounts) - len(self.allocated_accounts)
    
    def allocate_accounts(self, num_accounts: int, strategy: str = "sequential") -> List[AccountInfo]:
        """
        分配指定数量的账户
        
        Args:
            num_accounts: 需要分配的账户数量
            strategy: 分配策略 ("sequential": 顺序分配, "random": 随机分配)
            
        Returns:
            分配的账户列表
            
        Raises:
            ValueError: 当请求的账户数量超过可用数量时
        """
        if num_accounts <= 0:
            raise ValueError(f"账户数量必须大于0: {num_accounts}")
        
        available_count = self.get_available_accounts()
        if num_accounts > available_count:
            raise ValueError(
                f"请求的账户数量({num_accounts})超过可用数量({available_count})"
            )
        
        # 获取可用账户索引
        available_indices = [
            i for i in range(len(self.accounts)) 
            if i not in self.allocated_accounts
        ]
        
        # 根据策略选择账户
        if strategy == "random":
            selected_indices = random.sample(available_indices, num_accounts)
        else:  # sequential
            selected_indices = available_indices[:num_accounts]
        
        # 分配账户
        allocated_accounts = []
        for index in selected_indices:
            account = self.accounts[index]
            allocated_accounts.append(account)
            self.allocated_accounts.add(index)
        
        self.logger.info(f"✅ 成功分配 {num_accounts} 个账户 (策略: {strategy})")
        return allocated_accounts
    
    def release_accounts(self, accounts: List[AccountInfo]):
        """
        释放账户分配
        
        Args:
            accounts: 要释放的账户列表
        """
        released_count = 0
        for account in accounts:
            if account.index in self.allocated_accounts:
                self.allocated_accounts.remove(account.index)
                released_count += 1
        
        self.logger.info(f"✅ 释放了 {released_count} 个账户")
    
    def release_all_accounts(self):
        """释放所有已分配的账户"""
        released_count = len(self.allocated_accounts)
        self.allocated_accounts.clear()
        self.logger.info(f"✅ 释放了所有 {released_count} 个已分配账户")
    
    def get_account_by_index(self, index: int) -> Optional[AccountInfo]:
        """根据索引获取账户"""
        if 0 <= index < len(self.accounts):
            return self.accounts[index]
        return None
    
    def validate_accounts(self, accounts: List[AccountInfo]) -> Dict[str, Any]:
        """
        验证账户列表
        
        Args:
            accounts: 要验证的账户列表
            
        Returns:
            验证结果字典
        """
        result = {
            "total_accounts": len(accounts),
            "valid_accounts": 0,
            "invalid_accounts": [],
            "duplicate_keys": [],
            "validation_passed": True
        }
        
        seen_api_keys = set()
        
        for account in accounts:
            # 检查API密钥重复
            if account.api_key in seen_api_keys:
                result["duplicate_keys"].append(account.api_key)
                result["validation_passed"] = False
            else:
                seen_api_keys.add(account.api_key)
            
            # 检查账户有效性
            try:
                if not account.api_key or not account.secret:
                    result["invalid_accounts"].append({
                        "index": account.index,
                        "email": account.email,
                        "reason": "API密钥或密码为空"
                    })
                    result["validation_passed"] = False
                else:
                    result["valid_accounts"] += 1
            except Exception as e:
                result["invalid_accounts"].append({
                    "index": account.index,
                    "email": account.email,
                    "reason": str(e)
                })
                result["validation_passed"] = False
        
        return result
    
    def get_allocation_summary(self) -> Dict[str, Any]:
        """获取账户分配摘要"""
        return {
            "total_accounts": self.get_total_accounts(),
            "allocated_accounts": len(self.allocated_accounts),
            "available_accounts": self.get_available_accounts(),
            "allocation_rate": len(self.allocated_accounts) / self.get_total_accounts() if self.get_total_accounts() > 0 else 0
        }
    
    def create_user_beans(self, accounts: List[AccountInfo]) -> List[Dict[str, str]]:
        """
        为账户列表创建userBean格式的数据
        
        Args:
            accounts: 账户列表
            
        Returns:
            userBean格式的数据列表
        """
        user_beans = []
        for account in accounts:
            user_bean = {
                "apiKey": account.api_key,
                "secret": account.secret
            }
            user_beans.append(user_bean)
        
        return user_beans

    # ==================== 预留扩展接口 ====================
    # 以下接口为将来真实多账户功能预留，当前版本暂未实现

    def validate_account_credentials(self, account: AccountInfo) -> bool:
        """
        验证单个账户凭据有效性（预留接口）

        Args:
            account: 要验证的账户

        Returns:
            bool: 凭据是否有效

        注意：当前版本返回True，将来可集成真实的API验证
        """
        # TODO: 实现真实的API凭据验证
        # 可以调用Nine CEX API验证账户凭据
        return True

    def refresh_account_status(self, account: AccountInfo) -> Dict[str, Any]:
        """
        刷新账户状态（预留接口）

        Args:
            account: 要刷新的账户

        Returns:
            Dict: 账户状态信息

        注意：当前版本返回模拟状态，将来可集成真实的账户状态查询
        """
        # TODO: 实现真实的账户状态查询
        # 可以查询账户余额、订单状态等信息
        return {
            "account_index": account.index,
            "status": "active",
            "balance": "unknown",
            "last_updated": "not_implemented"
        }


if __name__ == "__main__":
    # 测试账户管理器
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 创建账户管理器
        manager = AccountManager()
        
        # 显示账户摘要
        summary = manager.get_allocation_summary()
        print(f"📊 账户摘要: {summary}")
        
        # 分配100个账户进行测试
        test_accounts = manager.allocate_accounts(100, strategy="sequential")
        print(f"✅ 分配了 {len(test_accounts)} 个测试账户")
        
        # 验证账户
        validation_result = manager.validate_accounts(test_accounts)
        print(f"🔍 验证结果: {validation_result}")
        
        # 释放账户
        manager.release_accounts(test_accounts)
        print("✅ 释放了测试账户")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
