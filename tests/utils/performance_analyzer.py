"""
Nine CEX 性能分析器 - 企业级版本
提供详细的订单级别跟踪、性能指标收集和专业级CSV报告生成功能
支持多维度性能分析、趋势预测和可视化数据准备
"""

import time
import csv
import os
import statistics
import locale
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from decimal import Decimal
import logging


@dataclass
class OrderMetrics:
    """订单性能指标数据类（支持批量订单和多账户）"""
    order_id: str
    order_index: int
    submit_time: float
    response_time: Optional[float] = None
    status_check_time: Optional[float] = None
    first_fill_time: Optional[float] = None
    final_status: str = "UNKNOWN"
    filled_quantity: str = "0"
    error_message: Optional[str] = None
    api_response_time: Optional[float] = None
    submit_to_response_delay: Optional[float] = None
    submit_to_fill_delay: Optional[float] = None
    status_transitions: List[Tuple[str, float]] = None
    # 批量订单支持字段
    batch_order_ids: Optional[List[str]] = None
    batch_size: Optional[int] = None
    # 多账户支持字段
    account_index: Optional[int] = None
    account_email: Optional[str] = None
    order_value_usdt: Optional[float] = None
    is_multi_account: bool = False
    
    def __post_init__(self):
        if self.status_transitions is None:
            self.status_transitions = []

        # 计算延迟时间
        # 注意：submit_to_response_delay 现在直接在 record_submission_response 中设置
        # 这里只计算填充延迟
        if self.first_fill_time and self.submit_time:
            self.submit_to_fill_delay = self.first_fill_time - self.submit_time


@dataclass
class EnhancedPerformanceMetrics:
    """增强的性能指标数据类"""
    # 基础统计
    total_orders: int
    successful_orders: int
    failed_orders: int
    executed_orders: int

    # 响应时间统计 (毫秒)
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p90_response_time: float
    p95_response_time: float
    p99_response_time: float

    # 执行延迟统计 (毫秒)
    avg_execution_delay: float
    min_execution_delay: float
    max_execution_delay: float
    p50_execution_delay: float
    p90_execution_delay: float
    p95_execution_delay: float
    p99_execution_delay: float

    # 成功率指标
    submission_success_rate: float
    execution_success_rate: float
    overall_success_rate: float

    # 吞吐量指标 - 多维度分析
    orders_per_second: float  # 主要吞吐量（API吞吐量优先）
    end_to_end_throughput: float  # 端到端吞吐量
    api_throughput: float  # API处理吞吐量
    trade_throughput: float  # 交易执行吞吐量
    peak_concurrent_orders: int
    system_load_percentage: float

    # 时间分析
    total_test_duration: float  # 总测试时长
    api_duration: float  # API处理时长
    trade_duration: float  # 交易执行时长

    # USD价值统计
    total_usd_value: float
    executed_usd_value: float
    avg_order_usd_value: float

    # 性能等级分布
    grade_a_plus_count: int = 0
    grade_a_count: int = 0
    grade_b_count: int = 0
    grade_c_count: int = 0
    grade_d_count: int = 0


@dataclass
class PerformanceSummary:
    """性能汇总数据类"""
    test_start_time: str
    test_duration: float
    total_orders: int
    successful_submissions: int
    failed_submissions: int
    executed_orders: int
    submission_success_rate: float
    execution_rate: float
    avg_api_response_time: float
    min_api_response_time: float
    max_api_response_time: float
    p95_api_response_time: float
    p99_api_response_time: float
    avg_execution_delay: float
    throughput_orders_per_second: float
    concurrent_level: int
    system_load_percentage: float


class PerformanceAnalyzer:
    """Nine CEX 性能分析器"""
    
    def __init__(self, test_name: str = "concurrent_trading_test"):
        self.test_name = test_name
        self.test_start_time = time.time()
        self.test_start_datetime = datetime.now()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 订单跟踪数据
        self.order_metrics: Dict[str, OrderMetrics] = {}
        self.order_index_map: Dict[int, str] = {}
        
        # 性能统计
        self.api_response_times: List[float] = []
        self.execution_delays: List[float] = []
        self.error_counts: Dict[str, int] = {}

        # 请求时间窗口记录
        self.first_request_time: Optional[float] = None
        self.last_request_time: Optional[float] = None
        
        # 实时监控
        self.progress_callback = None
        self.last_progress_update = 0
        
        # 确保报告目录存在
        self.reports_dir = "tests/reports"
        os.makedirs(self.reports_dir, exist_ok=True)

        # 测试场景跟踪
        self.scenario_data = {}  # 存储不同测试场景的数据

    def merge_analyzer_data(self, other_analyzer: 'PerformanceAnalyzer', scenario_prefix: str = ""):
        """合并另一个性能分析器的数据到当前分析器中"""
        # 合并订单指标数据
        for tracking_id, metrics in other_analyzer.order_metrics.items():
            # 为避免ID冲突，添加场景前缀
            new_tracking_id = f"{scenario_prefix}{tracking_id}"

            # 创建新的metrics副本，更新order_index以包含场景信息
            new_metrics = OrderMetrics(
                order_id=metrics.order_id,
                order_index=f"{scenario_prefix}{metrics.order_index}",
                submit_time=metrics.submit_time,
                response_time=metrics.response_time,
                status_check_time=metrics.status_check_time,
                first_fill_time=metrics.first_fill_time,
                final_status=metrics.final_status,
                filled_quantity=metrics.filled_quantity,
                error_message=metrics.error_message,
                api_response_time=metrics.api_response_time,
                submit_to_response_delay=metrics.submit_to_response_delay,
                submit_to_fill_delay=metrics.submit_to_fill_delay,
                status_transitions=metrics.status_transitions.copy() if metrics.status_transitions else None,
                batch_order_ids=metrics.batch_order_ids.copy() if metrics.batch_order_ids else None,
                batch_size=metrics.batch_size
            )

            self.order_metrics[new_tracking_id] = new_metrics

        # 合并order_index_map
        for order_index, tracking_id in other_analyzer.order_index_map.items():
            new_order_index = f"{scenario_prefix}{order_index}"
            new_tracking_id = f"{scenario_prefix}{tracking_id}"
            self.order_index_map[new_order_index] = new_tracking_id

        # 合并性能统计数据
        self.api_response_times.extend(other_analyzer.api_response_times)
        self.execution_delays.extend(other_analyzer.execution_delays)

        # 合并错误计数
        for error_key, count in other_analyzer.error_counts.items():
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + count

        # 更新时间窗口
        if other_analyzer.first_request_time:
            if self.first_request_time is None or other_analyzer.first_request_time < self.first_request_time:
                self.first_request_time = other_analyzer.first_request_time

        if other_analyzer.last_request_time:
            if self.last_request_time is None or other_analyzer.last_request_time > self.last_request_time:
                self.last_request_time = other_analyzer.last_request_time

        # 存储场景数据
        self.scenario_data[scenario_prefix.rstrip('_')] = {
            'total_orders': len(other_analyzer.order_metrics),
            'test_name': other_analyzer.test_name,
            'start_time': other_analyzer.test_start_time
        }

    def _extract_scenario_name(self, order_index) -> str:
        """从order_index中提取测试场景名称"""
        order_index_str = str(order_index)

        # 检查是否包含场景前缀
        if '小规模测试_' in order_index_str:
            return "小规模测试(20订单)"
        elif '中等规模测试_' in order_index_str:
            return "中等规模测试(50订单)"
        elif '大规模测试_' in order_index_str:
            return "大规模测试(99订单)"
        else:
            return "单一测试"

    # ==================== 格式化工具函数 ====================

    def _format_number(self, value: float, decimal_places: int = 2) -> str:
        """格式化数字，添加千分位分隔符"""
        if value is None:
            return "N/A"
        try:
            if decimal_places == 0:
                return f"{int(value):,}"
            else:
                return f"{value:,.{decimal_places}f}"
        except (ValueError, TypeError):
            return str(value)

    def _format_percentage(self, value: float, decimal_places: int = 1) -> str:
        """格式化百分比"""
        if value is None:
            return "N/A"
        try:
            return f"{value:.{decimal_places}f}%"
        except (ValueError, TypeError):
            return str(value)

    def _format_duration(self, seconds: float) -> str:
        """格式化时间长度"""
        if seconds is None:
            return "N/A"
        try:
            if seconds < 1:
                return f"{seconds*1000:.1f}ms"
            elif seconds < 60:
                return f"{seconds:.2f}s"
            else:
                minutes = int(seconds // 60)
                secs = seconds % 60
                return f"{minutes}m{secs:.1f}s"
        except (ValueError, TypeError):
            return str(seconds)

    def _get_performance_emoji(self, grade: str) -> str:
        """获取性能等级对应的emoji"""
        emoji_map = {
            "A+": "🏆",  # 优秀+
            "A": "🥇",   # 优秀
            "B": "🥈",   # 良好
            "C": "🥉",   # 及格
            "D": "❌",   # 不及格
            "F": "💥"    # 失败
        }
        return emoji_map.get(grade, "❓")

    def _get_status_emoji(self, status: str) -> str:
        """获取订单状态对应的emoji"""
        status_map = {
            "FILLED": "✅",
            "PARTIALLY_FILLED": "🔄",
            "NEW": "🆕",
            "CANCELLED": "❌",
            "FAILED": "💥",
            "UNKNOWN": "❓"
        }
        return status_map.get(status, "❓")

    def _create_visual_separator(self, title: str, width: int = 80, char: str = "=") -> str:
        """创建视觉分隔符"""
        if len(title) >= width - 4:
            return char * width

        padding = (width - len(title) - 2) // 2
        return f"{char * padding} {title} {char * (width - padding - len(title) - 2)}"
    
    def start_order_tracking(self, order_index, account_index: Optional[int] = None,
                           account_email: Optional[str] = None,
                           order_value_usdt: Optional[float] = None) -> str:
        """开始跟踪订单（支持批量订单和多账户）"""
        # 支持字符串类型的order_index（如"batch_0"）
        tracking_id = f"order_{order_index}_{int(time.time() * 1000)}"

        metrics = OrderMetrics(
            order_id="",  # 将在提交成功后更新
            order_index=order_index if isinstance(order_index, int) else str(order_index),
            submit_time=time.time(),
            account_index=account_index,
            account_email=account_email,
            order_value_usdt=order_value_usdt,
            is_multi_account=account_index is not None
        )

        self.order_metrics[tracking_id] = metrics
        self.order_index_map[order_index] = tracking_id

        return tracking_id
    
    def record_submission_response(self, tracking_id: str, success: bool,
                                 order_id = None,
                                 error_message: Optional[str] = None,
                                 api_response_time: Optional[float] = None):
        """记录订单提交响应（支持单个订单ID或订单ID列表）"""
        if tracking_id not in self.order_metrics:
            return

        metrics = self.order_metrics[tracking_id]
        metrics.response_time = time.time()  # 响应时间戳
        metrics.api_response_time = api_response_time  # API响应耗时（秒）

        # 计算提交到响应的延迟时间
        if api_response_time is not None:
            metrics.submit_to_response_delay = api_response_time

        if success and order_id:
            # 支持单个订单ID或订单ID列表
            if isinstance(order_id, list):
                # 对于批量订单，使用第一个订单ID作为主要ID，其余存储在额外字段中
                metrics.order_id = order_id[0] if order_id else ""
                # 可以在这里添加批量订单的特殊处理逻辑
                if len(order_id) > 1:
                    # 为批量订单记录额外信息
                    metrics.batch_order_ids = order_id
                    metrics.batch_size = len(order_id)
            else:
                metrics.order_id = str(order_id)

            metrics.final_status = "SUBMITTED"
        else:
            metrics.final_status = "FAILED"
            metrics.error_message = error_message

            # 统计错误类型
            error_key = error_message or "UNKNOWN_ERROR"
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1

        # 记录API响应时间
        if api_response_time:
            self.api_response_times.append(api_response_time)

        # 更新请求时间窗口
        current_time = time.time()
        if self.first_request_time is None:
            self.first_request_time = current_time
        self.last_request_time = current_time
    
    def record_status_check(self, order_id: str, status: str, filled_quantity: str = "0"):
        """记录订单状态检查"""
        # 查找对应的跟踪ID
        tracking_id = None
        for tid, metrics in self.order_metrics.items():
            if metrics.order_id == order_id:
                tracking_id = tid
                break

        if not tracking_id:
            return
        
        metrics = self.order_metrics[tracking_id]
        current_time = time.time()
        
        if not metrics.status_check_time:
            metrics.status_check_time = current_time
        
        # 记录状态变化
        if not metrics.status_transitions or metrics.status_transitions[-1][0] != status:
            metrics.status_transitions.append((status, current_time))
        
        # 更新最终状态和成交量
        metrics.final_status = status
        metrics.filled_quantity = filled_quantity
        
        # 记录首次成交时间
        if float(filled_quantity) > 0 and not metrics.first_fill_time:
            metrics.first_fill_time = current_time
            
            # 计算执行延迟
            if metrics.submit_time:
                execution_delay = current_time - metrics.submit_time
                self.execution_delays.append(execution_delay)
    
    def update_progress(self, completed: int, total: int, stage: str = ""):
        """更新测试进度"""
        current_time = time.time()
        
        # 限制进度更新频率
        if current_time - self.last_progress_update < 1.0:  # 每秒最多更新一次
            return
        
        self.last_progress_update = current_time
        progress_percentage = (completed / total) * 100 if total > 0 else 0
        
        # 估算剩余时间
        elapsed_time = current_time - self.test_start_time
        if completed > 0:
            estimated_total_time = elapsed_time * total / completed
            remaining_time = max(0, estimated_total_time - elapsed_time)
            remaining_str = f", 预计剩余: {remaining_time:.1f}秒"
        else:
            remaining_str = ""
        
        stage_str = f" ({stage})" if stage else ""
        self.logger.info(f"📊 测试进度: {completed}/{total} ({progress_percentage:.1f}%){stage_str}{remaining_str}")

    def _calculate_enhanced_metrics(self) -> EnhancedPerformanceMetrics:
        """计算增强的性能指标"""
        total_orders = len(self.order_metrics)

        # 基础统计
        successful_orders = sum(1 for m in self.order_metrics.values()
                              if m.final_status not in ["FAILED", "UNKNOWN"])
        failed_orders = total_orders - successful_orders
        executed_orders = sum(1 for m in self.order_metrics.values()
                            if float(m.filled_quantity) > 0)

        # 响应时间统计 (转换为毫秒)
        response_times_ms = [t * 1000 for t in self.api_response_times if t is not None]

        if response_times_ms:
            avg_response_time = statistics.mean(response_times_ms)
            min_response_time = min(response_times_ms)
            max_response_time = max(response_times_ms)
            p50_response_time = statistics.median(response_times_ms)
            p90_response_time = self._calculate_percentile(response_times_ms, 90)
            p95_response_time = self._calculate_percentile(response_times_ms, 95)
            p99_response_time = self._calculate_percentile(response_times_ms, 99)
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p50_response_time = p90_response_time = p95_response_time = p99_response_time = 0

        # 执行延迟统计 (转换为毫秒)
        execution_delays_ms = []
        for metrics in self.order_metrics.values():
            if metrics.submit_to_fill_delay:
                execution_delays_ms.append(metrics.submit_to_fill_delay * 1000)

        if execution_delays_ms:
            avg_execution_delay = statistics.mean(execution_delays_ms)
            min_execution_delay = min(execution_delays_ms)
            max_execution_delay = max(execution_delays_ms)
            p50_execution_delay = statistics.median(execution_delays_ms)
            p90_execution_delay = self._calculate_percentile(execution_delays_ms, 90)
            p95_execution_delay = self._calculate_percentile(execution_delays_ms, 95)
            p99_execution_delay = self._calculate_percentile(execution_delays_ms, 99)
        else:
            avg_execution_delay = min_execution_delay = max_execution_delay = 0
            p50_execution_delay = p90_execution_delay = p95_execution_delay = p99_execution_delay = 0

        # 成功率指标
        submission_success_rate = (successful_orders / total_orders * 100) if total_orders > 0 else 0
        execution_success_rate = (executed_orders / successful_orders * 100) if successful_orders > 0 else 0
        overall_success_rate = (executed_orders / total_orders * 100) if total_orders > 0 else 0

        # 吞吐量指标 - 多维度时间分析
        test_duration = time.time() - self.test_start_time

        # 方法A：端到端吞吐量（总测试时长）
        end_to_end_throughput = total_orders / test_duration if test_duration > 0 else 0

        # 方法B：API吞吐量（订单提交时间窗口）
        api_throughput = 0
        api_duration = 0
        if self.first_request_time and self.last_request_time:
            api_duration = self.last_request_time - self.first_request_time
            api_throughput = total_orders / api_duration if api_duration > 0 else 0

        # 方法C：交易吞吐量（基于实际执行时间窗口）
        trade_throughput = 0
        trade_duration = 0
        execution_times = []
        for metrics in self.order_metrics.values():
            if metrics.first_fill_time:
                execution_times.append(metrics.first_fill_time)

        if len(execution_times) >= 2:
            trade_duration = max(execution_times) - min(execution_times)
            trade_throughput = len(execution_times) / trade_duration if trade_duration > 0 else 0

        # 使用端到端吞吐量作为主要指标（更准确反映实际性能）
        orders_per_second = end_to_end_throughput

        peak_concurrent_orders = total_orders  # 假设所有订单都是并发的

        # 系统负载百分比计算 - 暂时注释，因为无法获取服务器端负载
        # import psutil
        # try:
        #     cpu_percent = psutil.cpu_percent(interval=0.1)
        #     memory_percent = psutil.virtual_memory().percent
        #     system_load_percentage = max(cpu_percent, memory_percent)
        # except ImportError:
        #     # 如果没有psutil，使用基于并发数的估算
        #     max_concurrent_capacity = 100  # Nine CEX的最大订单限制
        #     system_load_percentage = (total_orders / max_concurrent_capacity) * 100

        # 暂时不计算系统负载，避免误导性数据
        system_load_percentage = None

        # USD价值统计
        buy_price = 0.0000105  # 从配置获取
        total_usd_value = 0
        executed_usd_value = 0

        for metrics in self.order_metrics.values():
            order_usd = float(metrics.filled_quantity) * buy_price if metrics.filled_quantity else 0
            total_usd_value += order_usd
            if float(metrics.filled_quantity) > 0:
                executed_usd_value += order_usd

        avg_order_usd_value = total_usd_value / total_orders if total_orders > 0 else 0

        # 性能等级分布 - 修复分类逻辑
        grade_counts = {"A+": 0, "A": 0, "B": 0, "C": 0, "D": 0}
        for metrics in self.order_metrics.values():
            grade = self._calculate_performance_grade(metrics)
            # 修复等级提取逻辑，处理"A+ 优秀"、"B+ 中等"等格式
            if grade.startswith("A+"):
                grade_counts["A+"] += 1
            elif grade.startswith("A "):
                grade_counts["A"] += 1
            elif grade.startswith("B"):  # 包括"B+ 中等"和"B 及格"
                grade_counts["B"] += 1
            elif grade.startswith("C"):
                grade_counts["C"] += 1
            elif grade.startswith("D"):
                grade_counts["D"] += 1

        return EnhancedPerformanceMetrics(
            total_orders=total_orders,
            successful_orders=successful_orders,
            failed_orders=failed_orders,
            executed_orders=executed_orders,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p50_response_time=p50_response_time,
            p90_response_time=p90_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            avg_execution_delay=avg_execution_delay,
            min_execution_delay=min_execution_delay,
            max_execution_delay=max_execution_delay,
            p50_execution_delay=p50_execution_delay,
            p90_execution_delay=p90_execution_delay,
            p95_execution_delay=p95_execution_delay,
            p99_execution_delay=p99_execution_delay,
            submission_success_rate=submission_success_rate,
            execution_success_rate=execution_success_rate,
            overall_success_rate=overall_success_rate,
            orders_per_second=orders_per_second,
            end_to_end_throughput=end_to_end_throughput,
            api_throughput=api_throughput,
            trade_throughput=trade_throughput,
            peak_concurrent_orders=peak_concurrent_orders,
            system_load_percentage=system_load_percentage,
            total_test_duration=test_duration,
            api_duration=api_duration if self.first_request_time and self.last_request_time else 0,
            trade_duration=trade_duration if len(execution_times) >= 2 else 0,
            total_usd_value=total_usd_value,
            executed_usd_value=executed_usd_value,
            avg_order_usd_value=avg_order_usd_value,
            grade_a_plus_count=grade_counts["A+"],
            grade_a_count=grade_counts["A"],
            grade_b_count=grade_counts["B"],
            grade_c_count=grade_counts["C"],
            grade_d_count=grade_counts["D"]
        )

    def _calculate_percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))

    def analyze_performance(self) -> PerformanceSummary:
        """分析性能数据并生成汇总"""
        test_duration = time.time() - self.test_start_time
        total_orders = len(self.order_metrics)
        
        # 统计提交成功/失败 - 修复成功率计算逻辑
        # 成功提交：有订单ID且状态为SUBMITTED的订单
        successful_submissions = sum(1 for m in self.order_metrics.values()
                                   if m.final_status == "SUBMITTED" and m.order_id)
        failed_submissions = total_orders - successful_submissions

        # 统计执行订单（实际成交的订单）
        executed_orders = sum(1 for m in self.order_metrics.values()
                            if float(m.filled_quantity) > 0)

        # 计算成功率
        submission_success_rate = (successful_submissions / total_orders) * 100 if total_orders > 0 else 0
        # execution_rate 表示在成功提交的订单中，实际执行的比例
        execution_rate = (executed_orders / successful_submissions) * 100 if successful_submissions > 0 else 0
        
        # API响应时间统计 - 修复百分位数计算
        if self.api_response_times:
            avg_api_response = statistics.mean(self.api_response_times)
            min_api_response = min(self.api_response_times)
            max_api_response = max(self.api_response_times)

            sorted_times = sorted(self.api_response_times)
            n = len(sorted_times)
            # 使用标准百分位数计算方法
            def safe_percentile(data, p):
                """安全的百分位数计算，避免索引越界"""
                if not data:
                    return 0
                n = len(data)
                index = (p / 100) * (n - 1)
                if index.is_integer():
                    return data[int(index)]
                else:
                    lower_idx = int(index)
                    upper_idx = min(lower_idx + 1, n - 1)
                    if lower_idx == upper_idx:
                        return data[lower_idx]
                    lower = data[lower_idx]
                    upper = data[upper_idx]
                    return lower + (upper - lower) * (index - lower_idx)

            p50_api_response = safe_percentile(sorted_times, 50)
            p90_api_response = safe_percentile(sorted_times, 90)
            p95_api_response = safe_percentile(sorted_times, 95)
            p99_api_response = safe_percentile(sorted_times, 99)
        else:
            avg_api_response = min_api_response = max_api_response = 0
            p50_api_response = p90_api_response = p95_api_response = p99_api_response = 0
        
        # 执行延迟统计
        avg_execution_delay = statistics.mean(self.execution_delays) if self.execution_delays else 0
        
        # 吞吐量计算 - 确保测试时间合理，避免异常高的吞吐量
        # 如果测试时间过短（小于0.1秒），使用API调用时间窗口
        effective_duration = test_duration
        if test_duration < 0.1 and self.first_request_time and self.last_request_time:
            api_duration = self.last_request_time - self.first_request_time
            if api_duration > 0:
                effective_duration = api_duration
                self.logger.debug(f"使用API时间窗口计算吞吐量: {api_duration:.3f}秒 (测试时间过短: {test_duration:.3f}秒)")

        throughput = successful_submissions / effective_duration if effective_duration > 0 else 0
        
        return PerformanceSummary(
            test_start_time=self.test_start_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            test_duration=test_duration,
            total_orders=total_orders,
            successful_submissions=successful_submissions,
            failed_submissions=failed_submissions,
            executed_orders=executed_orders,
            submission_success_rate=submission_success_rate,
            execution_rate=execution_rate,
            avg_api_response_time=avg_api_response,
            min_api_response_time=min_api_response,
            max_api_response_time=max_api_response,
            p95_api_response_time=p95_api_response,
            p99_api_response_time=p99_api_response,
            avg_execution_delay=avg_execution_delay,
            throughput_orders_per_second=throughput,
            concurrent_level=total_orders,
            system_load_percentage=(total_orders + 1) / 100 * 100  # 假设100为系统上限
        )

    def generate_enhanced_csv_reports(self, summary: PerformanceSummary) -> Tuple[str, str]:
        """生成增强的专业级CSV报告文件"""
        timestamp = self.test_start_datetime.strftime("%Y%m%d_%H%M%S")

        # 计算增强指标
        enhanced_metrics = self._calculate_enhanced_metrics()

        # 订单详细报告文件名
        order_details_file = os.path.join(
            self.reports_dir,
            f"enhanced_order_details_{timestamp}.csv"
        )

        # 性能汇总报告文件名
        summary_file = os.path.join(
            self.reports_dir,
            f"enhanced_performance_summary_{timestamp}.csv"
        )

        # 生成增强的订单详细报告
        self._generate_enhanced_order_details_csv(order_details_file, enhanced_metrics)

        # 生成增强的性能汇总报告
        self._generate_enhanced_summary_csv(summary_file, enhanced_metrics)

        return order_details_file, summary_file

    # 保持向后兼容的旧方法
    def generate_csv_reports(self, summary: PerformanceSummary) -> Tuple[str, str]:
        """生成CSV报告文件（向后兼容）"""
        return self.generate_enhanced_csv_reports(summary)

    def _generate_enhanced_order_details_csv(self, filename: str, enhanced_metrics: EnhancedPerformanceMetrics):
        """生成增强的订单详细CSV报告"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入专业级报告头部
            test_duration = time.time() - self.test_start_time
            csvfile.write(f"# {self._create_visual_separator('Nine CEX 订单详细性能报告', 100, '=')}\n")
            csvfile.write(f"# 📊 测试概览\n")
            csvfile.write(f"# ├─ 测试名称: {self.test_name}\n")
            csvfile.write(f"# ├─ 测试时间: {self.test_start_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"# ├─ 测试时长: {self._format_duration(test_duration)}\n")
            csvfile.write(f"# ├─ 并发级别: {self._format_number(enhanced_metrics.peak_concurrent_orders, 0)} 个订单\n")
            # csvfile.write(f"# ├─ 系统负载: {self._format_percentage(enhanced_metrics.system_load_percentage)}\n")  # 暂时注释
            csvfile.write(f"# └─ 总体成功率: {self._format_percentage(enhanced_metrics.overall_success_rate)}\n")
            csvfile.write(f"#\n")
            csvfile.write(f"# 🎯 关键指标快览\n")
            csvfile.write(f"# ├─ 提交成功率: {self._format_percentage(enhanced_metrics.submission_success_rate)}\n")
            csvfile.write(f"# ├─ 执行成功率: {self._format_percentage(enhanced_metrics.execution_success_rate)}\n")
            csvfile.write(f"# ├─ 平均响应时间: {self._format_number(enhanced_metrics.avg_response_time, 1)}ms\n")
            csvfile.write(f"# ├─ P95响应时间: {self._format_number(enhanced_metrics.p95_response_time, 1)}ms\n")
            csvfile.write(f"# ├─ 系统吞吐量: {self._format_number(enhanced_metrics.orders_per_second, 2)} 订单/秒\n")
            csvfile.write(f"# │  ├─ 端到端吞吐量: {self._format_number(enhanced_metrics.end_to_end_throughput, 2)} 订单/秒 (总测试时长)\n")
            csvfile.write(f"# │  ├─ API处理吞吐量: {self._format_number(enhanced_metrics.api_throughput, 2)} 订单/秒 (订单提交窗口)\n")
            csvfile.write(f"# │  └─ 交易执行吞吐量: {self._format_number(enhanced_metrics.trade_throughput, 2)} 订单/秒 (实际执行窗口)\n")
            csvfile.write(f"# └─ 总交易价值: {self._format_number(enhanced_metrics.executed_usd_value, 6)} USDT\n")
            csvfile.write(f"#\n")
            csvfile.write(f"# {self._create_visual_separator('详细订单数据', 100, '-')}\n")

            # 优化的CSV字段定义 - 精简且无冗余，添加测试场景字段
            fieldnames = [
                '序号', '测试场景', '订单ID', '提交时间', '执行状态', '成交数量',
                'USD价值', '响应时间(ms)', '性能等级', '关键指标', '备注'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # 写入优化的订单数据
            for metrics in sorted(self.order_metrics.values(), key=lambda x: x.order_index):
                # 精简时间显示 - 只保留提交时间
                submit_time_str = datetime.fromtimestamp(metrics.submit_time).strftime('%H:%M:%S')

                # 统一执行状态 - 消除重复表达
                execution_status = self._get_unified_execution_status(metrics)

                # 计算USD价值
                buy_price = 0.0000105
                usd_value = float(metrics.filled_quantity) * buy_price if metrics.filled_quantity else 0

                # 优化性能等级显示 - 修复格式不一致问题
                performance_grade = self._get_optimized_performance_grade(metrics)

                # 精简关键指标 - 合并重要信息
                key_indicators = self._get_key_indicators(metrics)

                # 简化备注信息 - 只保留关键问题
                remarks = self._get_simplified_remarks(metrics)

                # 提取测试场景信息
                scenario_name = self._extract_scenario_name(metrics.order_index)

                # 写入优化的订单数据行
                # 处理order_index可能是字符串的情况
                try:
                    order_num = int(str(metrics.order_index).split('_')[-1]) + 1 if '_' in str(metrics.order_index) else (int(metrics.order_index) + 1 if isinstance(metrics.order_index, int) else hash(str(metrics.order_index)) % 1000)
                except:
                    order_num = 1

                writer.writerow({
                    '序号': f"{order_num:03d}",
                    '测试场景': scenario_name,
                    '订单ID': metrics.order_id if metrics.order_id else "N/A",  # 显示完整订单ID
                    '提交时间': submit_time_str,
                    '执行状态': execution_status,
                    '成交数量': self._format_number(float(metrics.filled_quantity), 0) if metrics.filled_quantity else "0",
                    'USD价值': f"${usd_value:.6f}",
                    '响应时间(ms)': self._format_number(metrics.api_response_time * 1000, 0) if metrics.api_response_time else "N/A",
                    '性能等级': performance_grade,
                    '关键指标': key_indicators,
                    '备注': remarks
                })

            # 写入报告尾部统计
            csvfile.write(f"\n# {self._create_visual_separator('报告统计摘要', 100, '=')}\n")
            csvfile.write(f"# 📈 执行统计: 成功 {enhanced_metrics.executed_orders}/{enhanced_metrics.total_orders} 订单\n")
            csvfile.write(f"# 💰 价值统计: 执行 ${self._format_number(enhanced_metrics.executed_usd_value, 6)} / 总计 ${self._format_number(enhanced_metrics.total_usd_value, 6)} USDT\n")
            csvfile.write(f"# ⚡ 性能统计: 平均响应 {self._format_number(enhanced_metrics.avg_response_time, 1)}ms, P95 {self._format_number(enhanced_metrics.p95_response_time, 1)}ms\n")
            csvfile.write(f"# 🎯 等级分布: A+({enhanced_metrics.grade_a_plus_count}) A({enhanced_metrics.grade_a_count}) B({enhanced_metrics.grade_b_count}) C({enhanced_metrics.grade_c_count}) D({enhanced_metrics.grade_d_count})\n")
            csvfile.write(f"# {self._create_visual_separator('报告结束', 100, '=')}\n")

    def _calculate_performance_score(self, metrics: OrderMetrics) -> int:
        """计算性能评分 (0-100)"""
        score = 100

        # 基于API响应时间扣分
        if metrics.api_response_time:
            if metrics.api_response_time > 10:  # 超过10秒
                score -= 40
            elif metrics.api_response_time > 5:  # 超过5秒
                score -= 20
            elif metrics.api_response_time > 2:  # 超过2秒
                score -= 10

        # 基于执行状态扣分
        if metrics.final_status == "FAILED":
            score -= 50
        elif metrics.final_status not in ["FILLED", "PARTIALLY_FILLED"]:
            score -= 30

        # 基于错误信息扣分
        if metrics.error_message:
            score -= 20

        return max(0, score)

    def _generate_smart_analysis(self, metrics: OrderMetrics) -> str:
        """生成智能分析"""
        analysis_parts = []

        # 响应时间分析
        if metrics.api_response_time:
            if metrics.api_response_time > 5:
                analysis_parts.append("🐌 响应缓慢")
            elif metrics.api_response_time < 1:
                analysis_parts.append("⚡ 响应迅速")
            else:
                analysis_parts.append("✅ 响应正常")

        # 执行状态分析
        if metrics.final_status == "FILLED":
            analysis_parts.append("🎯 完全成交")
        elif metrics.final_status == "PARTIALLY_FILLED":
            analysis_parts.append("🔄 部分成交")
        elif metrics.final_status == "FAILED":
            analysis_parts.append("💥 执行失败")

        # 成交量分析
        if metrics.filled_quantity and float(metrics.filled_quantity) > 0:
            analysis_parts.append("💰 有成交")
        else:
            analysis_parts.append("❌ 无成交")

        return "; ".join(analysis_parts) if analysis_parts else "无特殊情况"

    def _generate_optimization_advice(self, metrics: OrderMetrics) -> str:
        """生成优化建议"""
        advice_parts = []

        # 基于响应时间的建议
        if metrics.api_response_time and metrics.api_response_time > 5:
            advice_parts.append("优化网络连接")

        # 基于执行状态的建议
        if metrics.final_status == "FAILED":
            advice_parts.append("检查订单参数")
        elif metrics.final_status not in ["FILLED", "PARTIALLY_FILLED"]:
            advice_parts.append("增加流动性")

        # 基于错误信息的建议
        if metrics.error_message:
            if "timeout" in metrics.error_message.lower():
                advice_parts.append("增加超时时间")
            elif "limit" in metrics.error_message.lower():
                advice_parts.append("调整订单限制")

        return "; ".join(advice_parts) if advice_parts else "无需优化"

    def _calculate_performance_grade(self, metrics: 'OrderMetrics') -> str:
        """计算订单性能等级"""
        score = 0

        # 执行成功 (40分)
        if metrics.final_status in ["FILLED", "PARTIALLY_FILLED"]:
            score += 40
        elif metrics.final_status == "NEW":
            score += 10  # 至少提交成功

        # API响应时间 (30分)
        if metrics.api_response_time:
            if metrics.api_response_time < 1.0:
                score += 30
            elif metrics.api_response_time < 3.0:
                score += 20
            elif metrics.api_response_time < 5.0:
                score += 10

        # 执行延迟 (20分)
        if metrics.submit_to_fill_delay:
            if metrics.submit_to_fill_delay < 30:
                score += 20
            elif metrics.submit_to_fill_delay < 60:
                score += 15
            elif metrics.submit_to_fill_delay < 120:
                score += 10

        # 无错误 (10分)
        if not metrics.error_message:
            score += 10

        # 等级评定
        if score >= 90:
            return "A+ 优秀"
        elif score >= 80:
            return "A 良好"
        elif score >= 70:
            return "B+ 中等"
        elif score >= 60:
            return "B 及格"
        elif score >= 40:
            return "C 较差"
        else:
            return "D 失败"

    def _generate_remarks(self, metrics: 'OrderMetrics') -> str:
        """生成订单备注信息"""
        remarks = []

        # 性能相关备注
        if metrics.api_response_time and metrics.api_response_time > 5.0:
            remarks.append("API高延迟")
        elif metrics.api_response_time and metrics.api_response_time < 1.0:
            remarks.append("API响应快")

        # 执行相关备注
        if float(metrics.filled_quantity) > 0:
            remarks.append("已执行")
        if metrics.final_status == "PARTIALLY_FILLED":
            remarks.append("部分成交")
        elif metrics.final_status == "FILLED":
            remarks.append("完全成交")

        # 错误相关备注
        if metrics.error_message:
            if "timeout" in metrics.error_message.lower():
                remarks.append("超时错误")
            elif "limit" in metrics.error_message.lower():
                remarks.append("限制错误")
            else:
                remarks.append("系统错误")

        # 时间相关备注
        if metrics.submit_to_fill_delay and metrics.submit_to_fill_delay > 120:
            remarks.append("执行缓慢")
        elif metrics.submit_to_fill_delay and metrics.submit_to_fill_delay < 10:
            remarks.append("执行迅速")

        return "; ".join(remarks) if remarks else "正常"

    def _generate_enhanced_summary_csv(self, filename: str, enhanced_metrics: EnhancedPerformanceMetrics):
        """生成增强的性能汇总CSV报告"""
        test_duration = time.time() - self.test_start_time

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入企业级报告头部
            csvfile.write(f"# {self._create_visual_separator('Nine CEX 性能汇总报告', 120, '=')}\n")
            csvfile.write(f"#\n")
            csvfile.write(f"# 🏢 测试环境信息\n")
            csvfile.write(f"# ├─ 测试名称: {self.test_name}\n")
            csvfile.write(f"# ├─ 测试时间: {self.test_start_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"# ├─ 报告生成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"# ├─ 测试时长: {self._format_duration(test_duration)}\n")
            csvfile.write(f"# └─ 并发级别: {self._format_number(enhanced_metrics.peak_concurrent_orders, 0)} 个订单\n")
            csvfile.write(f"#\n")

            # 执行摘要
            csvfile.write(f"# 📊 执行摘要 (Executive Summary)\n")
            csvfile.write(f"# ┌─ 总体成功率: {self._format_percentage(enhanced_metrics.overall_success_rate)} 🎯\n")
            csvfile.write(f"# ├─ 提交成功率: {self._format_percentage(enhanced_metrics.submission_success_rate)} 📤\n")
            csvfile.write(f"# ├─ 执行成功率: {self._format_percentage(enhanced_metrics.execution_success_rate)} ⚡\n")
            csvfile.write(f"# ├─ 系统吞吐量: {self._format_number(enhanced_metrics.orders_per_second, 2)} 订单/秒 🚀\n")
            csvfile.write(f"# ├─ 平均响应时间: {self._format_number(enhanced_metrics.avg_response_time, 1)}ms ⏱️\n")
            csvfile.write(f"# └─ 总交易价值: ${self._format_number(enhanced_metrics.executed_usd_value, 6)} USDT 💰\n")
            csvfile.write(f"#\n")

            # 性能等级评估
            total_orders = enhanced_metrics.total_orders
            grade_distribution = [
                ("🏆 A+ (优秀+)", enhanced_metrics.grade_a_plus_count, enhanced_metrics.grade_a_plus_count/total_orders*100 if total_orders > 0 else 0),
                ("🥇 A (优秀)", enhanced_metrics.grade_a_count, enhanced_metrics.grade_a_count/total_orders*100 if total_orders > 0 else 0),
                ("🥈 B (良好)", enhanced_metrics.grade_b_count, enhanced_metrics.grade_b_count/total_orders*100 if total_orders > 0 else 0),
                ("🥉 C (及格)", enhanced_metrics.grade_c_count, enhanced_metrics.grade_c_count/total_orders*100 if total_orders > 0 else 0),
                ("❌ D (不及格)", enhanced_metrics.grade_d_count, enhanced_metrics.grade_d_count/total_orders*100 if total_orders > 0 else 0)
            ]

            csvfile.write(f"# 🎖️ 性能等级分布\n")
            for grade_name, count, percentage in grade_distribution:
                csvfile.write(f"# ├─ {grade_name}: {count} 个订单 ({self._format_percentage(percentage)})\n")
            csvfile.write(f"#\n")

            # 详细性能指标表格
            csvfile.write(f"# {self._create_visual_separator('详细性能指标', 120, '-')}\n")
            csvfile.write(f"#\n")

            # 创建性能指标表格
            performance_data = [
                ["指标类别", "指标名称", "数值", "单位", "评级", "行业基准", "状态"],
                ["", "", "", "", "", "", ""],
                ["📊 基础统计", "总订单数", f"{enhanced_metrics.total_orders:,}", "个", self._get_grade_by_count(enhanced_metrics.total_orders), "≥50", self._get_status_by_comparison(enhanced_metrics.total_orders, 50)],
                ["", "成功订单数", f"{enhanced_metrics.successful_orders:,}", "个", self._get_grade_by_percentage(enhanced_metrics.submission_success_rate), "≥95%", self._get_status_by_comparison(enhanced_metrics.submission_success_rate, 95)],
                ["", "执行订单数", f"{enhanced_metrics.executed_orders:,}", "个", self._get_grade_by_percentage(enhanced_metrics.execution_success_rate), "≥90%", self._get_status_by_comparison(enhanced_metrics.execution_success_rate, 90)],
                ["", "失败订单数", f"{enhanced_metrics.failed_orders:,}", "个", self._get_grade_by_failure_rate(enhanced_metrics.failed_orders/enhanced_metrics.total_orders*100 if enhanced_metrics.total_orders > 0 else 0), "≤5%", ""],
                ["", "", "", "", "", "", ""],
                ["⚡ 响应时间", "平均响应时间", f"{enhanced_metrics.avg_response_time:.1f}", "ms", self._get_grade_by_response_time(enhanced_metrics.avg_response_time), "≤2000ms", self._get_status_by_comparison(2000, enhanced_metrics.avg_response_time)],
                ["", "最小响应时间", f"{enhanced_metrics.min_response_time:.1f}", "ms", "📈", "≤500ms", self._get_status_by_comparison(500, enhanced_metrics.min_response_time)],
                ["", "最大响应时间", f"{enhanced_metrics.max_response_time:.1f}", "ms", "📊", "≤10000ms", self._get_status_by_comparison(10000, enhanced_metrics.max_response_time)],
                ["", "P50响应时间", f"{enhanced_metrics.p50_response_time:.1f}", "ms", self._get_grade_by_response_time(enhanced_metrics.p50_response_time), "≤1500ms", self._get_status_by_comparison(1500, enhanced_metrics.p50_response_time)],
                ["", "P90响应时间", f"{enhanced_metrics.p90_response_time:.1f}", "ms", self._get_grade_by_response_time(enhanced_metrics.p90_response_time), "≤3000ms", self._get_status_by_comparison(3000, enhanced_metrics.p90_response_time)],
                ["", "P95响应时间", f"{enhanced_metrics.p95_response_time:.1f}", "ms", self._get_grade_by_response_time(enhanced_metrics.p95_response_time), "≤5000ms", self._get_status_by_comparison(5000, enhanced_metrics.p95_response_time)],
                ["", "P99响应时间", f"{enhanced_metrics.p99_response_time:.1f}", "ms", self._get_grade_by_response_time(enhanced_metrics.p99_response_time), "≤8000ms", self._get_status_by_comparison(8000, enhanced_metrics.p99_response_time)],
                ["", "", "", "", "", "", ""],
                ["🎯 成功率指标", "提交成功率", f"{enhanced_metrics.submission_success_rate:.1f}", "%", self._get_grade_by_percentage(enhanced_metrics.submission_success_rate), "≥95%", self._get_status_by_comparison(enhanced_metrics.submission_success_rate, 95)],
                ["", "执行成功率", f"{enhanced_metrics.execution_success_rate:.1f}", "%", self._get_grade_by_percentage(enhanced_metrics.execution_success_rate), "≥90%", self._get_status_by_comparison(enhanced_metrics.execution_success_rate, 90)],
                ["", "总体成功率", f"{enhanced_metrics.overall_success_rate:.1f}", "%", self._get_grade_by_percentage(enhanced_metrics.overall_success_rate), "≥85%", self._get_status_by_comparison(enhanced_metrics.overall_success_rate, 85)],
                ["", "", "", "", "", "", ""],
                ["🚀 吞吐量指标", "订单处理速度", f"{enhanced_metrics.orders_per_second:.2f}", "订单/秒", self._get_grade_by_throughput(enhanced_metrics.orders_per_second), "≥10", self._get_status_by_comparison(enhanced_metrics.orders_per_second, 10)],
                ["", "端到端吞吐量", f"{enhanced_metrics.end_to_end_throughput:.2f}", "订单/秒", self._get_grade_by_throughput(enhanced_metrics.end_to_end_throughput), "≥3", self._get_status_by_comparison(enhanced_metrics.end_to_end_throughput, 3)],
                ["", "API处理吞吐量", f"{enhanced_metrics.api_throughput:.2f}", "订单/秒", self._get_grade_by_throughput(enhanced_metrics.api_throughput), "≥10", self._get_status_by_comparison(enhanced_metrics.api_throughput, 10)],
                ["", "交易执行吞吐量", f"{enhanced_metrics.trade_throughput:.2f}", "订单/秒", self._get_grade_by_throughput(enhanced_metrics.trade_throughput), "≥15", self._get_status_by_comparison(enhanced_metrics.trade_throughput, 15)],
                ["", "峰值并发数", f"{enhanced_metrics.peak_concurrent_orders:,}", "个", self._get_grade_by_count(enhanced_metrics.peak_concurrent_orders), "≥50", self._get_status_by_comparison(enhanced_metrics.peak_concurrent_orders, 50)],
                # ["", "系统负载", f"{enhanced_metrics.system_load_percentage:.1f}", "%", self._get_grade_by_load(enhanced_metrics.system_load_percentage), "≤80%", self._get_status_by_comparison(80, enhanced_metrics.system_load_percentage)],  # 暂时注释
                ["", "", "", "", "", "", ""],
                ["💰 价值指标", "总交易价值", f"${enhanced_metrics.total_usd_value:.6f}", "USDT", "💰", "≥$1.00", self._get_status_by_comparison(enhanced_metrics.total_usd_value, 1.0)],
                ["", "执行交易价值", f"${enhanced_metrics.executed_usd_value:.6f}", "USDT", "💎", "≥$0.90", self._get_status_by_comparison(enhanced_metrics.executed_usd_value, 0.9)],
                ["", "平均订单价值", f"${enhanced_metrics.avg_order_usd_value:.6f}", "USDT", "📊", "≥$0.10", self._get_status_by_comparison(enhanced_metrics.avg_order_usd_value, 0.1)]
            ]

            # 写入性能指标表格
            for row in performance_data:
                csvfile.write(f"# {','.join(row)}\n")

            csvfile.write(f"#\n")
            csvfile.write(f"# {self._create_visual_separator('性能分析结论', 120, '-')}\n")

            # 生成性能分析结论
            overall_grade = self._calculate_overall_grade(enhanced_metrics)
            performance_conclusion = self._generate_performance_conclusion(enhanced_metrics)
            optimization_recommendations = self._generate_optimization_recommendations(enhanced_metrics)

            csvfile.write(f"# 🏆 总体性能等级: {overall_grade}\n")
            csvfile.write(f"# 📋 性能分析结论:\n")
            for conclusion in performance_conclusion:
                csvfile.write(f"#    • {conclusion}\n")
            csvfile.write(f"# 🔧 优化建议:\n")
            for recommendation in optimization_recommendations:
                csvfile.write(f"#    • {recommendation}\n")

            csvfile.write(f"#\n")
            csvfile.write(f"# {self._create_visual_separator('报告结束', 120, '=')}\n")

    # ==================== 增强报告辅助函数 ====================

    def _get_grade_by_count(self, count: int) -> str:
        """根据数量获取等级"""
        if count >= 100:
            return "🏆 A+"
        elif count >= 50:
            return "🥇 A"
        elif count >= 20:
            return "🥈 B"
        elif count >= 10:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_grade_by_percentage(self, percentage: float) -> str:
        """根据百分比获取等级"""
        if percentage >= 98:
            return "🏆 A+"
        elif percentage >= 95:
            return "🥇 A"
        elif percentage >= 90:
            return "🥈 B"
        elif percentage >= 80:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_grade_by_response_time(self, time_ms: float) -> str:
        """根据响应时间获取等级"""
        if time_ms <= 1000:
            return "🏆 A+"
        elif time_ms <= 2000:
            return "🥇 A"
        elif time_ms <= 5000:
            return "🥈 B"
        elif time_ms <= 10000:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_grade_by_failure_rate(self, failure_rate: float) -> str:
        """根据失败率获取等级"""
        if failure_rate <= 1:
            return "🏆 A+"
        elif failure_rate <= 3:
            return "🥇 A"
        elif failure_rate <= 5:
            return "🥈 B"
        elif failure_rate <= 10:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_grade_by_throughput(self, throughput: float) -> str:
        """根据吞吐量获取等级"""
        if throughput >= 50:
            return "🏆 A+"
        elif throughput >= 20:
            return "🥇 A"
        elif throughput >= 10:
            return "🥈 B"
        elif throughput >= 5:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_grade_by_load(self, load_percentage: float) -> str:
        """根据系统负载获取等级"""
        if load_percentage <= 60:
            return "🏆 A+"
        elif load_percentage <= 70:
            return "🥇 A"
        elif load_percentage <= 80:
            return "🥈 B"
        elif load_percentage <= 90:
            return "🥉 C"
        else:
            return "❌ D"

    def _get_status_by_comparison(self, value: float, benchmark: float) -> str:
        """通过与基准比较获取状态"""
        if value >= benchmark:
            return "✅ 达标"
        elif value >= benchmark * 0.8:
            return "⚠️ 接近"
        else:
            return "❌ 不达标"

    def _calculate_overall_grade(self, enhanced_metrics: EnhancedPerformanceMetrics) -> str:
        """计算总体性能等级"""
        scores = []

        # 成功率评分 (40%权重)
        success_score = enhanced_metrics.overall_success_rate
        scores.append(success_score * 0.4)

        # 响应时间评分 (30%权重)
        if enhanced_metrics.avg_response_time <= 1000:
            response_score = 100
        elif enhanced_metrics.avg_response_time <= 2000:
            response_score = 90
        elif enhanced_metrics.avg_response_time <= 5000:
            response_score = 80
        else:
            response_score = 60
        scores.append(response_score * 0.3)

        # 吞吐量评分 (20%权重)
        if enhanced_metrics.orders_per_second >= 20:
            throughput_score = 100
        elif enhanced_metrics.orders_per_second >= 10:
            throughput_score = 90
        elif enhanced_metrics.orders_per_second >= 5:
            throughput_score = 80
        else:
            throughput_score = 60
        scores.append(throughput_score * 0.2)

        # 稳定性评分 (10%权重)
        stability_score = 100 - (enhanced_metrics.failed_orders / enhanced_metrics.total_orders * 100 if enhanced_metrics.total_orders > 0 else 0)
        scores.append(stability_score * 0.1)

        total_score = sum(scores)

        if total_score >= 95:
            return "🏆 A+ (卓越)"
        elif total_score >= 90:
            return "🥇 A (优秀)"
        elif total_score >= 80:
            return "🥈 B (良好)"
        elif total_score >= 70:
            return "🥉 C (及格)"
        else:
            return "❌ D (需改进)"

    def _generate_performance_conclusion(self, enhanced_metrics: EnhancedPerformanceMetrics) -> List[str]:
        """生成性能分析结论"""
        conclusions = []

        # 成功率分析
        if enhanced_metrics.overall_success_rate >= 95:
            conclusions.append(f"系统表现卓越，总体成功率达到 {enhanced_metrics.overall_success_rate:.1f}%")
        elif enhanced_metrics.overall_success_rate >= 85:
            conclusions.append(f"系统表现良好，总体成功率为 {enhanced_metrics.overall_success_rate:.1f}%")
        else:
            conclusions.append(f"系统成功率偏低 ({enhanced_metrics.overall_success_rate:.1f}%)，需要优化")

        # 响应时间分析
        if enhanced_metrics.avg_response_time <= 2000:
            conclusions.append(f"响应时间优秀，平均 {enhanced_metrics.avg_response_time:.1f}ms")
        elif enhanced_metrics.avg_response_time <= 5000:
            conclusions.append(f"响应时间可接受，平均 {enhanced_metrics.avg_response_time:.1f}ms")
        else:
            conclusions.append(f"响应时间较慢，平均 {enhanced_metrics.avg_response_time:.1f}ms，需要优化")

        # 吞吐量分析
        if enhanced_metrics.orders_per_second >= 20:
            conclusions.append(f"系统吞吐量优秀，达到 {enhanced_metrics.orders_per_second:.2f} 订单/秒")
        elif enhanced_metrics.orders_per_second >= 10:
            conclusions.append(f"系统吞吐量良好，为 {enhanced_metrics.orders_per_second:.2f} 订单/秒")
        else:
            conclusions.append(f"系统吞吐量偏低，仅 {enhanced_metrics.orders_per_second:.2f} 订单/秒")

        # 稳定性分析
        failure_rate = enhanced_metrics.failed_orders / enhanced_metrics.total_orders * 100 if enhanced_metrics.total_orders > 0 else 0
        if failure_rate <= 2:
            conclusions.append(f"系统稳定性优秀，失败率仅 {failure_rate:.1f}%")
        elif failure_rate <= 5:
            conclusions.append(f"系统稳定性良好，失败率为 {failure_rate:.1f}%")
        else:
            conclusions.append(f"系统稳定性需要改进，失败率达到 {failure_rate:.1f}%")

        return conclusions

    def _generate_optimization_recommendations(self, enhanced_metrics: EnhancedPerformanceMetrics) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 基于成功率的建议
        if enhanced_metrics.overall_success_rate < 90:
            recommendations.append("提高订单参数验证，减少提交失败")
            recommendations.append("优化网络连接稳定性")

        # 基于响应时间的建议
        if enhanced_metrics.avg_response_time > 3000:
            recommendations.append("优化API调用逻辑，减少网络延迟")
            recommendations.append("考虑使用连接池或异步处理")

        if enhanced_metrics.p95_response_time > 8000:
            recommendations.append("增加超时重试机制")
            recommendations.append("监控和优化慢查询")

        # 基于吞吐量的建议
        if enhanced_metrics.orders_per_second < 10:
            recommendations.append("增加并发处理能力")
            recommendations.append("优化批量处理逻辑")

        # 基于失败率的建议
        failure_rate = enhanced_metrics.failed_orders / enhanced_metrics.total_orders * 100 if enhanced_metrics.total_orders > 0 else 0
        if failure_rate > 5:
            recommendations.append("分析失败原因，完善错误处理")
            recommendations.append("增加订单状态监控和告警")

        # 基于价值的建议
        if enhanced_metrics.executed_usd_value < enhanced_metrics.total_usd_value * 0.9:
            recommendations.append("优化订单执行策略，提高成交率")
            recommendations.append("调整订单价格和数量参数")

        if not recommendations:
            recommendations.append("系统表现优秀，继续保持当前配置")
            recommendations.append("定期监控性能指标，确保稳定运行")

        return recommendations

    # ==================== 优化的字段生成函数 ====================

    def _get_unified_execution_status(self, metrics: OrderMetrics) -> str:
        """获取统一的执行状态，消除重复表达"""
        if not metrics.order_id:
            return "❌ 提交失败"

        # 首先检查final_status（来自批量验证的状态）
        if hasattr(metrics, 'final_status') and metrics.final_status:
            if metrics.final_status == "FILLED":
                return "✅ 完全成交"
            elif metrics.final_status == "PARTIALLY_FILLED":
                filled_qty = float(metrics.filled_quantity) if metrics.filled_quantity else 0
                if filled_qty > 0:
                    target_quantity = 10000
                    fill_rate = (filled_qty / target_quantity) * 100
                    return f"🔄 部分成交({fill_rate:.1f}%)"
                else:
                    return "🔄 部分成交"
            elif metrics.final_status == "CANCELLED":
                return "❌ 已撤销"
            elif metrics.final_status == "NEW":
                return "⏳ 未成交"

        # 备用逻辑：基于成交数量判断
        if not metrics.filled_quantity or float(metrics.filled_quantity) == 0:
            return "⏳ 未成交"

        # 检查是否完全成交（假设目标数量为10000）
        target_quantity = 10000
        filled_qty = float(metrics.filled_quantity)

        if filled_qty >= target_quantity:
            return "✅ 完全成交"
        elif filled_qty > 0:
            fill_rate = (filled_qty / target_quantity) * 100
            return f"🔄 部分成交({fill_rate:.1f}%)"
        else:
            return "⏳ 未成交"

    def _get_optimized_performance_grade(self, metrics: OrderMetrics) -> str:
        """获取优化的性能等级，修复格式不一致问题"""
        if not metrics.api_response_time:
            return "❌ 无响应"

        response_time_ms = metrics.api_response_time * 1000

        # 优先使用final_status判断成交情况
        is_filled = False
        if hasattr(metrics, 'final_status') and metrics.final_status:
            is_filled = metrics.final_status in ["FILLED", "PARTIALLY_FILLED"]
        else:
            # 备用逻辑：基于成交数量判断
            filled_qty = float(metrics.filled_quantity) if metrics.filled_quantity else 0
            is_filled = filled_qty > 0

        # 基于响应时间和成交情况的综合评级
        if response_time_ms <= 1000 and is_filled:
            return "🏆 优秀"
        elif response_time_ms <= 2000 and is_filled:
            return "🥇 良好"
        elif response_time_ms <= 5000 and is_filled:
            return "🥈 一般"
        elif is_filled:
            return "🥉 较慢"
        else:
            return "❌ 失败"

    def _get_key_indicators(self, metrics: OrderMetrics) -> str:
        """获取关键指标，合并重要信息"""
        indicators = []

        # 响应速度指标
        if metrics.api_response_time:
            response_time_ms = metrics.api_response_time * 1000
            if response_time_ms <= 1000:
                indicators.append("⚡快速")
            elif response_time_ms <= 3000:
                indicators.append("✅正常")
            else:
                indicators.append("🐌较慢")

        # 成交指标 - 优先使用final_status判断
        if hasattr(metrics, 'final_status') and metrics.final_status:
            if metrics.final_status == "FILLED":
                indicators.append("✅已成交")
            elif metrics.final_status == "PARTIALLY_FILLED":
                indicators.append("🔄部分成交")
            elif metrics.final_status == "CANCELLED":
                indicators.append("❌已撤销")
            else:
                indicators.append("⏳未成交")
        else:
            # 备用逻辑：基于成交数量判断
            filled_qty = float(metrics.filled_quantity) if metrics.filled_quantity else 0
            if filled_qty >= 10000:
                indicators.append("✅已成交")
            elif filled_qty > 0:
                indicators.append("🔄部分成交")
            else:
                indicators.append("⏳未成交")

        # 稳定性指标
        if not metrics.error_message:
            indicators.append("🔒稳定")

        return " ".join(indicators) if indicators else "⚠️异常"

    def _get_simplified_remarks(self, metrics: OrderMetrics) -> str:
        """获取简化的备注信息，只保留关键问题"""
        if metrics.error_message:
            # 简化错误信息
            error_msg = metrics.error_message
            if "timeout" in error_msg.lower() or "超时" in error_msg:
                return "⏰ 超时"
            elif "invalid" in error_msg.lower() or "无效" in error_msg:
                return "❌ 参数错误"
            elif "network" in error_msg.lower() or "网络" in error_msg:
                return "🌐 网络问题"
            else:
                return "❌ 其他错误"

        # 检查是否有异常的响应时间
        if metrics.api_response_time and metrics.api_response_time * 1000 > 5000:
            return "🐌 响应慢"

        # 检查成交情况 - 优先使用final_status
        if hasattr(metrics, 'final_status') and metrics.final_status:
            if metrics.final_status == "FILLED":
                return "✅ 成交完成"
            elif metrics.final_status == "PARTIALLY_FILLED":
                return "🔄 部分成交"
            elif metrics.final_status == "CANCELLED":
                return "❌ 已撤销"
            elif metrics.final_status == "NEW":
                return "⏳ 待成交"
        else:
            # 备用逻辑：基于成交数量判断
            filled_qty = float(metrics.filled_quantity) if metrics.filled_quantity else 0
            if filled_qty == 0 and metrics.order_id:
                return "⏳ 待成交"

        return "✅ 正常"

    def _generate_summary_csv(self, filename: str, summary: PerformanceSummary):
        """生成性能汇总CSV报告"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入报告头部信息
            csvfile.write(f"# Nine CEX 性能汇总报告\n")
            csvfile.write(f"# 测试名称: {self.test_name}\n")
            csvfile.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"#\n")

            # 基本统计信息
            csvfile.write("## 基本统计\n")
            writer = csv.writer(csvfile)
            writer.writerow(["指标", "数值", "单位"])
            writer.writerow(["测试开始时间", summary.test_start_time, ""])
            writer.writerow(["测试持续时间", f"{summary.test_duration:.2f}", "秒"])
            writer.writerow(["总订单数", summary.total_orders, "个"])
            writer.writerow(["成功提交订单", summary.successful_submissions, "个"])
            writer.writerow(["失败提交订单", summary.failed_submissions, "个"])
            writer.writerow(["实际执行订单", summary.executed_orders, "个"])
            writer.writerow(["并发级别", summary.concurrent_level, "个"])
            # writer.writerow(["系统负载", f"{summary.system_load_percentage:.1f}", "%"])  # 暂时注释

            csvfile.write("\n## 成功率统计\n")
            writer.writerow(["指标", "数值", "单位"])
            writer.writerow(["提交成功率", f"{summary.submission_success_rate:.2f}", "%"])
            writer.writerow(["执行成功率", f"{summary.execution_rate:.2f}", "%"])
            writer.writerow(["吞吐量", f"{summary.throughput_orders_per_second:.2f}", "订单/秒"])

            csvfile.write("\n## API响应时间统计\n")
            writer.writerow(["指标", "数值", "单位"])
            writer.writerow(["平均响应时间", f"{summary.avg_api_response_time * 1000:.1f}", "毫秒"])
            writer.writerow(["最小响应时间", f"{summary.min_api_response_time * 1000:.1f}", "毫秒"])
            writer.writerow(["最大响应时间", f"{summary.max_api_response_time * 1000:.1f}", "毫秒"])
            writer.writerow(["P95响应时间", f"{summary.p95_api_response_time * 1000:.1f}", "毫秒"])
            writer.writerow(["P99响应时间", f"{summary.p99_api_response_time * 1000:.1f}", "毫秒"])

            csvfile.write("\n## 执行延迟统计\n")
            writer.writerow(["指标", "数值", "单位"])
            writer.writerow(["平均执行延迟", f"{summary.avg_execution_delay * 1000:.1f}", "毫秒"])

            # 增强的性能分析
            self._write_enhanced_performance_analysis(csvfile, writer, summary)

            # 错误统计
            if self.error_counts:
                csvfile.write("\n## 错误统计\n")
                writer.writerow(["错误类型", "出现次数", "占比"])
                total_errors = sum(self.error_counts.values())
                for error_type, count in sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True):
                    percentage = (count / total_errors) * 100
                    writer.writerow([error_type, count, f"{percentage:.1f}%"])

    def _write_enhanced_performance_analysis(self, csvfile, writer, summary: PerformanceSummary):
        """写入增强的性能分析"""

        # USD价值统计
        csvfile.write("\n## USD价值统计\n")
        writer.writerow(["指标", "数值", "单位"])

        total_usd_value = 0
        executed_usd_value = 0
        buy_price = 0.0000105

        for metrics in self.order_metrics.values():
            order_usd = float(metrics.filled_quantity) * buy_price if metrics.filled_quantity else 0
            total_usd_value += order_usd
            if float(metrics.filled_quantity) > 0:
                executed_usd_value += order_usd

        writer.writerow(["总交易USD价值", f"{total_usd_value:.6f}", "USDT"])
        writer.writerow(["已执行USD价值", f"{executed_usd_value:.6f}", "USDT"])
        writer.writerow(["平均订单USD价值", f"{executed_usd_value/summary.executed_orders:.6f}" if summary.executed_orders > 0 else "0.000000", "USDT"])

        # 性能等级分布
        csvfile.write("\n## 性能等级分布\n")
        writer.writerow(["等级", "订单数", "占比"])

        grade_counts = {}
        for metrics in self.order_metrics.values():
            grade = self._calculate_performance_grade(metrics)
            grade_counts[grade] = grade_counts.get(grade, 0) + 1

        total_orders = len(self.order_metrics)
        for grade, count in sorted(grade_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_orders) * 100
            writer.writerow([grade, count, f"{percentage:.1f}%"])

        # 时间分布分析
        csvfile.write("\n## 时间分布分析\n")
        writer.writerow(["时间段", "响应时间范围", "订单数", "占比"])

        # API响应时间分布
        response_time_ranges = [
            ("极快", 0, 1),
            ("快速", 1, 3),
            ("正常", 3, 5),
            ("缓慢", 5, 10),
            ("极慢", 10, float('inf'))
        ]

        for range_name, min_time, max_time in response_time_ranges:
            count = sum(1 for metrics in self.order_metrics.values()
                       if metrics.api_response_time and min_time <= metrics.api_response_time < max_time)
            percentage = (count / total_orders) * 100
            range_desc = f"{min_time}-{max_time}秒" if max_time != float('inf') else f">{min_time}秒"
            writer.writerow([range_name, range_desc, count, f"{percentage:.1f}%"])

        # 趋势分析
        csvfile.write("\n## 趋势分析建议\n")
        writer.writerow(["分析项", "当前状态", "建议"])

        # 提交成功率趋势
        if summary.submission_success_rate >= 95:
            writer.writerow(["提交成功率", "优秀", "保持当前配置"])
        elif summary.submission_success_rate >= 80:
            writer.writerow(["提交成功率", "良好", "可适当增加并发数"])
        else:
            writer.writerow(["提交成功率", "需改进", "检查网络和API配置"])

        # 执行成功率趋势
        if summary.execution_rate >= 90:
            writer.writerow(["执行成功率", "优秀", "流动性配置合理"])
        elif summary.execution_rate >= 70:
            writer.writerow(["执行成功率", "良好", "可优化价格策略"])
        else:
            writer.writerow(["执行成功率", "需改进", "增加流动性或调整价格"])

        # API性能趋势
        if summary.avg_api_response_time < 2.0:
            writer.writerow(["API响应时间", "优秀", "性能表现良好"])
        elif summary.avg_api_response_time < 5.0:
            writer.writerow(["API响应时间", "可接受", "监控峰值时段"])
        else:
            writer.writerow(["API响应时间", "需优化", "联系技术支持或优化网络"])

    def analyze_bottlenecks(self, summary: PerformanceSummary) -> List[str]:
        """分析性能瓶颈并提供建议"""
        issues = []
        recommendations = []

        # 分析提交成功率
        if summary.submission_success_rate < 95:
            issues.append(f"订单提交成功率较低 ({summary.submission_success_rate:.1f}%)")
            recommendations.append("检查网络连接稳定性和API服务状态")

        # 分析执行率
        if summary.execution_rate < 50:
            issues.append(f"订单执行率较低 ({summary.execution_rate:.1f}%)")
            recommendations.append("检查价格匹配策略和市场流动性")

        # 分析API响应时间
        if summary.avg_api_response_time > 2.0:  # 超过2秒
            issues.append(f"API平均响应时间过长 ({summary.avg_api_response_time:.2f}秒)")
            recommendations.append("考虑优化网络配置或联系API服务提供商")

        if summary.p99_api_response_time > 5.0:  # P99超过5秒
            issues.append(f"API P99响应时间过长 ({summary.p99_api_response_time:.2f}秒)")
            recommendations.append("存在严重的API延迟峰值，需要调查网络或服务端问题")

        # 分析系统负载 - 暂时注释
        # if summary.system_load_percentage > 90:
        #     issues.append(f"系统负载接近上限 ({summary.system_load_percentage:.1f}%)")
        #     recommendations.append("当前测试接近系统最大容量，生产环境需要预留安全边际")

        # 分析吞吐量
        expected_throughput = summary.concurrent_level / 10  # 期望10秒内完成
        if summary.throughput_orders_per_second < expected_throughput:
            issues.append(f"系统吞吐量低于预期 ({summary.throughput_orders_per_second:.1f} vs {expected_throughput:.1f} 订单/秒)")
            recommendations.append("考虑优化并发处理策略或增加系统资源")

        # 分析执行延迟
        if summary.avg_execution_delay > 10.0:  # 超过10秒
            issues.append(f"订单执行延迟过长 ({summary.avg_execution_delay:.1f}秒)")
            recommendations.append("检查订单匹配引擎性能和市场深度")

        # 合并分析结果
        analysis_results = []

        if issues:
            analysis_results.append("🔍 发现的性能问题:")
            for i, issue in enumerate(issues, 1):
                analysis_results.append(f"   {i}. {issue}")

        if recommendations:
            analysis_results.append("\n💡 优化建议:")
            for i, rec in enumerate(recommendations, 1):
                analysis_results.append(f"   {i}. {rec}")

        if not issues:
            analysis_results.append("✅ 系统性能表现良好，未发现明显瓶颈")

        return analysis_results

    def print_real_time_metrics(self):
        """打印实时性能指标"""
        current_time = time.time()
        elapsed = current_time - self.test_start_time

        # 统计当前状态
        total_orders = len(self.order_metrics)
        completed_orders = sum(1 for m in self.order_metrics.values()
                             if m.final_status not in ["UNKNOWN"])
        executed_orders = sum(1 for m in self.order_metrics.values()
                            if float(m.filled_quantity) > 0)

        # 计算实时吞吐量
        current_throughput = completed_orders / elapsed if elapsed > 0 else 0

        # 计算平均响应时间
        recent_response_times = self.api_response_times[-10:] if self.api_response_times else []
        avg_recent_response = statistics.mean(recent_response_times) if recent_response_times else 0

        self.logger.info(f"📊 实时性能指标:")
        self.logger.info(f"   ⏱️  运行时间: {elapsed:.1f}秒")
        self.logger.info(f"   📈 当前吞吐量: {current_throughput:.1f} 订单/秒")
        self.logger.info(f"   🎯 完成进度: {completed_orders}/{total_orders} ({completed_orders/total_orders*100:.1f}%)")
        self.logger.info(f"   ⚡ 执行订单: {executed_orders} ({executed_orders/completed_orders*100:.1f}% 执行率)" if completed_orders > 0 else "   ⚡ 执行订单: 0")
        self.logger.info(f"   🔗 近期API响应: {avg_recent_response*1000:.1f}ms")

    def highlight_anomalies(self):
        """识别并高亮显示异常订单"""
        anomalies = []

        for tracking_id, metrics in self.order_metrics.items():
            # 检查高延迟订单
            if metrics.api_response_time and metrics.api_response_time > 3.0:
                anomalies.append(f"⚠️ 订单 {metrics.order_index}: API响应延迟 {metrics.api_response_time*1000:.0f}ms")

            # 检查失败订单
            if metrics.final_status == "FAILED":
                anomalies.append(f"❌ 订单 {metrics.order_index}: 提交失败 - {metrics.error_message}")

            # 检查长时间未执行的订单
            if (metrics.final_status in ["SUBMITTED", "NEW"] and
                metrics.response_time and
                time.time() - metrics.response_time > 30):  # 30秒未执行
                anomalies.append(f"⏳ 订单 {metrics.order_index}: 长时间未执行 ({metrics.final_status})")

            # 检查部分成交订单
            if (metrics.filled_quantity and
                float(metrics.filled_quantity) > 0 and
                metrics.final_status == "PARTIALLY_FILLED"):
                anomalies.append(f"🔄 订单 {metrics.order_index}: 部分成交 {metrics.filled_quantity}")

        if anomalies:
            self.logger.warning("🚨 发现异常订单:")
            for anomaly in anomalies[:10]:  # 最多显示10个异常
                self.logger.warning(f"   {anomaly}")

            if len(anomalies) > 10:
                self.logger.warning(f"   ... 还有 {len(anomalies) - 10} 个异常订单")

        return anomalies

    def generate_multi_account_csv_reports(self, summary: PerformanceSummary,
                                         config: Any) -> Tuple[str, str]:
        """
        生成多账户测试专用的CSV报告

        Args:
            summary: 性能摘要
            config: 测试配置

        Returns:
            (订单详情文件路径, 摘要文件路径)
        """
        timestamp = self.test_start_datetime.strftime("%Y%m%d_%H%M%S")

        # 生成文件名
        order_details_file = os.path.join(
            self.reports_dir,
            f"multi_account_order_details_{config.name}_{timestamp}.csv"
        )
        summary_file = os.path.join(
            self.reports_dir,
            f"multi_account_summary_{config.name}_{timestamp}.csv"
        )

        # 生成多账户订单详情报告
        self._generate_multi_account_order_details_csv(order_details_file, summary, config)

        # 生成多账户摘要报告
        self._generate_multi_account_summary_csv(summary_file, summary, config)

        return order_details_file, summary_file

    def _generate_multi_account_order_details_csv(self, filename: str,
                                                summary: PerformanceSummary, config: Any):
        """生成多账户订单详情CSV报告"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入报告头部
            test_duration = time.time() - self.test_start_time

            # 统一使用全量并发模式
            concurrency_info = f"全量并发: {config.num_accounts}账户"

            # 计算正确的成功率（基于订单提交成功，而不是执行成功）
            total_orders = len(self.order_metrics)
            successful_orders = sum(1 for m in self.order_metrics.values()
                                  if m.final_status == "SUBMITTED" and m.order_id)
            actual_success_rate = (successful_orders / total_orders * 100) if total_orders > 0 else 0

            csvfile.write(f"# {self._create_visual_separator('Nine CEX 多账户订单详细性能报告', 120, '=')}\n")
            csvfile.write(f"# 📊 多账户测试概览\n")
            csvfile.write(f"# ├─ 测试名称: {config.display_name}\n")
            csvfile.write(f"# ├─ 测试规模: {config.num_accounts} 账户\n")
            csvfile.write(f"# ├─ 每单价值: {config.order_value_usdt} USDT\n")
            csvfile.write(f"# ├─ 总交易价值: {config.num_accounts * config.order_value_usdt} USDT\n")
            csvfile.write(f"# ├─ 测试时间: {self.test_start_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n")
            csvfile.write(f"# ├─ 测试时长: {self._format_duration(test_duration)}\n")
            csvfile.write(f"# ├─ 并发模式: {concurrency_info}\n")
            csvfile.write(f"# └─ 流动性倍数: {config.liquidity_multiplier}x\n")
            csvfile.write(f"#\n")
            csvfile.write(f"# 🎯 关键指标\n")
            csvfile.write(f"# ├─ 订单提交成功率: {self._format_percentage(actual_success_rate)}\n")
            csvfile.write(f"# ├─ 平均响应时间: {self._format_duration(summary.avg_api_response_time)}\n")
            csvfile.write(f"# ├─ 吞吐量: {self._format_number(summary.throughput_orders_per_second, 2)} 订单/秒\n")
            csvfile.write(f"# └─ 并发级别: {config.num_accounts} 账户同时交易\n")
            csvfile.write(f"#\n")

            # CSV数据头
            writer = csv.writer(csvfile)
            headers = [
                '订单索引', '账户索引', '账户邮箱', '订单ID', '订单价值(USDT)',
                '提交时间', '响应时间(ms)', '状态', '成交数量', '错误信息',
                '提交到响应延迟(ms)', '是否成功', '批次信息'
            ]
            writer.writerow(headers)

            # 统一使用全量并发模式
            batch_info = f"全量并发: {config.num_accounts}账户"

            # 写入订单数据
            for tracking_id, metrics in self.order_metrics.items():
                if metrics.is_multi_account:
                    writer.writerow([
                        metrics.order_index,
                        metrics.account_index or 'N/A',
                        metrics.account_email or 'N/A',
                        metrics.order_id or 'N/A',
                        f"{metrics.order_value_usdt:.2f}" if metrics.order_value_usdt else 'N/A',
                        datetime.fromtimestamp(metrics.submit_time).strftime('%H:%M:%S.%f')[:-3],
                        f"{metrics.api_response_time * 1000:.1f}" if metrics.api_response_time else 'N/A',
                        metrics.final_status,
                        metrics.filled_quantity,
                        metrics.error_message or '',
                        f"{metrics.submit_to_response_delay * 1000:.1f}" if metrics.submit_to_response_delay else 'N/A',
                        '✅' if metrics.order_id and not metrics.error_message else '❌',
                        batch_info
                    ])

    def _generate_multi_account_summary_csv(self, filename: str,
                                          summary: PerformanceSummary, config: Any):
        """生成多账户摘要CSV报告"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # 写入报告头部
            test_duration = time.time() - self.test_start_time
            csvfile.write(f"# {self._create_visual_separator('Nine CEX 多账户性能摘要报告', 100, '=')}\n")
            csvfile.write(f"# 📈 执行摘要\n")
            csvfile.write(f"# ├─ 测试配置: {config.display_name}\n")
            csvfile.write(f"# ├─ 账户规模: {config.num_accounts} 个账户\n")
            csvfile.write(f"# ├─ 总交易价值: {config.num_accounts * config.order_value_usdt} USDT\n")
            csvfile.write(f"# └─ 测试目的: {config.purpose}\n")
            csvfile.write(f"#\n")

            # 计算多账户特定指标
            multi_account_orders = [m for m in self.order_metrics.values() if m.is_multi_account]
            successful_accounts = len([m for m in multi_account_orders if m.order_id and not m.error_message])
            account_success_rate = successful_accounts / config.num_accounts if config.num_accounts > 0 else 0

            # CSV数据
            writer = csv.writer(csvfile)

            # 基础指标
            writer.writerow(['指标类别', '指标名称', '数值', '单位', '状态'])
            writer.writerow(['📊 规模指标', '测试账户数', config.num_accounts, '个', '✅'])
            writer.writerow(['📊 规模指标', '成功账户数', successful_accounts, '个', '✅' if account_success_rate > 0.9 else '⚠️'])
            writer.writerow(['📊 规模指标', '账户成功率', f"{account_success_rate:.1%}", '%', '✅' if account_success_rate > 0.9 else '⚠️'])
            # 统一显示全量并发模式
            writer.writerow(['📊 规模指标', '并发模式', '全量并发', f'{config.num_accounts}账户', '✅'])

            # 性能指标
            writer.writerow(['⚡ 性能指标', '平均响应时间', f"{summary.avg_api_response_time * 1000:.1f}", 'ms', '✅' if summary.avg_api_response_time < 2.0 else '⚠️'])
            writer.writerow(['⚡ 性能指标', '吞吐量', f"{summary.throughput_orders_per_second:.2f}", '订单/秒', '✅' if summary.throughput_orders_per_second > 3 else '⚠️'])
            writer.writerow(['⚡ 性能指标', '总测试时长', f"{test_duration:.1f}", '秒', '✅'])
            writer.writerow(['⚡ 性能指标', '并发级别', config.num_accounts, '账户', '✅'])

            # 业务指标
            total_value = successful_accounts * config.order_value_usdt
            writer.writerow(['💰 业务指标', '成功交易价值', f"{total_value:.2f}", 'USDT', '✅'])
            writer.writerow(['💰 业务指标', '单笔订单价值', f"{config.order_value_usdt:.2f}", 'USDT', '✅'])
            writer.writerow(['💰 业务指标', '流动性倍数', f"{config.liquidity_multiplier:.2f}", 'x', '✅'])

            # 质量指标 - 使用正确的成功率计算
            # 计算订单提交成功率（基于有订单ID的订单）
            total_orders = len(self.order_metrics)
            successful_orders = sum(1 for m in self.order_metrics.values()
                                  if m.final_status == "SUBMITTED" and m.order_id)
            actual_success_rate = (successful_orders / total_orders) if total_orders > 0 else 0

            writer.writerow(['🎯 质量指标', '订单提交成功率', f"{actual_success_rate:.1%}", '%', '✅' if actual_success_rate > 0.9 else '⚠️'])
            writer.writerow(['🎯 质量指标', '错误订单数', len([m for m in multi_account_orders if m.error_message]), '个', '✅' if len([m for m in multi_account_orders if m.error_message]) == 0 else '⚠️'])

            # 系统指标
            writer.writerow(['🔧 系统指标', '预期测试时长', config.expected_duration, '秒', '✅' if test_duration <= config.expected_duration * 1.2 else '⚠️'])
            writer.writerow(['🔧 系统指标', '实际测试时长', f"{test_duration:.1f}", '秒', '✅'])
            writer.writerow(['🔧 系统指标', '时长偏差', f"{((test_duration / config.expected_duration - 1) * 100):.1f}" if config.expected_duration > 0 else 'N/A', '%', '✅' if test_duration <= config.expected_duration * 1.2 else '⚠️'])

            # 写入分析建议
            csvfile.write(f"\n# 💡 分析建议\n")
            if account_success_rate >= 0.95:
                csvfile.write(f"# ✅ 账户成功率优秀 ({account_success_rate:.1%})，系统稳定性良好\n")
            elif account_success_rate >= 0.8:
                csvfile.write(f"# ⚠️ 账户成功率良好 ({account_success_rate:.1%})，建议优化网络连接\n")
            else:
                csvfile.write(f"# ❌ 账户成功率偏低 ({account_success_rate:.1%})，需要检查系统配置\n")

            if summary.throughput_orders_per_second >= 5:
                csvfile.write(f"# ✅ 吞吐量优秀 ({summary.throughput_orders_per_second:.1f} 订单/秒)\n")
            elif summary.throughput_orders_per_second >= 3:
                csvfile.write(f"# ⚠️ 吞吐量良好 ({summary.throughput_orders_per_second:.1f} 订单/秒)\n")
            else:
                csvfile.write(f"# ❌ 吞吐量偏低 ({summary.throughput_orders_per_second:.1f} 订单/秒)，建议优化并发策略\n")

            csvfile.write(f"# 📊 建议下次测试规模: {min(config.num_accounts + 50, 300)} 账户\n")
