"""
Nine CEX 统一订单验证器
提供高效的批量并发订单验证功能，支持单账户和多账户场景
"""

import time
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor


@dataclass
class OrderVerificationResult:
    """订单验证结果"""
    order_id: str
    account_index: Optional[int] = None
    account_email: Optional[str] = None
    status: int = 0  # 0=新建, 1=活跃/已执行, 2=取消
    filled_quantity: float = 0.0
    total_quantity: float = 0.0
    success: bool = False
    error_message: Optional[str] = None
    response_time: float = 0.0


@dataclass
class BatchVerificationSummary:
    """批量验证摘要"""
    total_orders: int
    executed_orders: int
    pending_orders: int
    failed_orders: int
    execution_rate: float
    avg_response_time: float
    verification_duration: float


class UnifiedOrderVerifier:
    """统一订单验证器 - 支持单账户和多账户场景的高效批量验证"""
    
    def __init__(self, nine_client, logger: Optional[logging.Logger] = None):
        """
        初始化统一订单验证器
        
        Args:
            nine_client: Nine CEX客户端实例
            logger: 日志记录器
        """
        self.nine_client = nine_client
        self.logger = logger or logging.getLogger(__name__)
        
    async def batch_verify_orders(
        self, 
        order_data: List[Dict[str, Any]], 
        max_wait_seconds: int = 60,
        check_interval: int = 5
    ) -> BatchVerificationSummary:
        """
        批量验证订单执行状态 - 统一接口支持单账户和多账户
        
        Args:
            order_data: 订单数据列表，支持两种格式：
                      - 单账户: [{"order_id": "xxx", "api_key": "xxx", "secret": "xxx"}]
                      - 多账户: [{"order_id": "xxx", "account_index": 0, "account_email": "xxx", "api_key": "xxx", "secret": "xxx"}]
            max_wait_seconds: 最大等待时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            BatchVerificationSummary: 批量验证摘要
        """
        self.logger.info(f"🔍 开始批量验证 {len(order_data)} 个订单的执行状态...")
        self.logger.info(f"⏰ 最大等待时间: {max_wait_seconds}秒，每{check_interval}秒检查一次")
        
        start_time = time.time()
        
        executed_orders = []
        pending_orders = []
        failed_orders = []
        
        # 轮询检查订单状态
        while time.time() - start_time < max_wait_seconds:
            elapsed_time = int(time.time() - start_time)
            self.logger.info(f"🔄 [{elapsed_time}s] 第 {elapsed_time // check_interval + 1} 轮状态检查...")
            
            # 并发查询所有订单状态
            verification_results = await self._concurrent_query_orders(order_data)
            
            # 重新分类订单
            executed_orders.clear()
            pending_orders.clear()
            failed_orders.clear()
            
            for result in verification_results:
                if result.success:
                    if result.status == 1 and result.filled_quantity > 0:
                        executed_orders.append(result)
                        self.logger.debug(f"✅ 订单 {result.order_id} 已执行: 状态={result.status}, 成交量={result.filled_quantity}")
                    elif result.status == 0:
                        pending_orders.append(result)
                        self.logger.debug(f"⏳ 订单 {result.order_id} 待执行: 状态={result.status}")
                    else:
                        failed_orders.append(result)
                        self.logger.debug(f"❌ 订单 {result.order_id} 异常状态: 状态={result.status}")
                else:
                    failed_orders.append(result)
                    self.logger.debug(f"❌ 订单 {result.order_id} 查询失败: {result.error_message}")
            
            # 计算当前执行率
            total_verified = len(executed_orders) + len(pending_orders) + len(failed_orders)
            current_execution_rate = len(executed_orders) / total_verified if total_verified > 0 else 0
            
            self.logger.info(f"📊 当前状态: 已执行={len(executed_orders)}, 待执行={len(pending_orders)}, 失败={len(failed_orders)}")
            self.logger.info(f"📈 执行率: {current_execution_rate:.1%}")
            
            # 如果所有订单都已处理完成（执行或失败），提前结束
            if len(pending_orders) == 0:
                self.logger.info("✅ 所有订单已处理完成，提前结束验证")
                break
            
            # 等待下一轮检查
            if time.time() - start_time < max_wait_seconds:
                await asyncio.sleep(check_interval)
        
        verification_duration = time.time() - start_time
        
        # 计算最终统计
        total_orders = len(order_data)
        execution_rate = len(executed_orders) / total_orders if total_orders > 0 else 0
        avg_response_time = sum(r.response_time for r in verification_results) / len(verification_results) if verification_results else 0
        
        # 记录最终结果
        self.logger.info(f"🎯 批量验证完成:")
        self.logger.info(f"   ⏱️  验证耗时: {verification_duration:.1f}秒")
        self.logger.info(f"   ✅ 已执行: {len(executed_orders)}/{total_orders} ({execution_rate:.1%})")
        self.logger.info(f"   ⏳ 待执行: {len(pending_orders)}")
        self.logger.info(f"   ❌ 失败: {len(failed_orders)}")
        self.logger.info(f"   ⚡ 平均响应: {avg_response_time*1000:.1f}ms")
        
        return BatchVerificationSummary(
            total_orders=total_orders,
            executed_orders=len(executed_orders),
            pending_orders=len(pending_orders),
            failed_orders=len(failed_orders),
            execution_rate=execution_rate,
            avg_response_time=avg_response_time,
            verification_duration=verification_duration
        )
    
    async def _concurrent_query_orders(self, order_data: List[Dict[str, Any]]) -> List[OrderVerificationResult]:
        """
        并发查询所有订单状态
        
        Args:
            order_data: 订单数据列表
            
        Returns:
            List[OrderVerificationResult]: 验证结果列表
        """
        async def query_single_order(order_info: Dict[str, Any]) -> OrderVerificationResult:
            """查询单个订单状态"""
            order_id = order_info["order_id"]
            api_key = order_info["api_key"]
            secret = order_info["secret"]
            account_index = order_info.get("account_index")
            account_email = order_info.get("account_email")
            
            start_time = time.perf_counter()
            
            try:
                # 使用线程池执行同步API调用
                loop = asyncio.get_event_loop()
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(
                        self.nine_client.get_order_detail_by_id,
                        order_id, api_key, secret
                    )
                    order_detail = await loop.run_in_executor(None, lambda: future.result())

                response_time = time.perf_counter() - start_time

                # 添加调试日志
                self.logger.debug(f"🔍 订单 {order_id} 查询响应: {order_detail}")

                # 解析订单详情 - 根据Nine CEX API的实际返回格式
                if order_detail and order_detail.get("query_success", False):
                    if order_detail.get("exists", False):
                        # 订单存在，解析订单详情
                        # 注意：Nine CEX API返回的状态字段是 orderStatus，不是 status
                        status = order_detail.get("orderStatus", 0)
                        filled_quantity = float(order_detail.get("filledQuantity", 0))
                        total_quantity = float(order_detail.get("quantity", 0))

                        return OrderVerificationResult(
                            order_id=order_id,
                            account_index=account_index,
                            account_email=account_email,
                            status=status,
                            filled_quantity=filled_quantity,
                            total_quantity=total_quantity,
                            success=True,
                            response_time=response_time
                        )
                    else:
                        # 订单不存在
                        reason = order_detail.get("reason", "unknown")
                        error_msg = f"订单不存在: {reason}"
                        return OrderVerificationResult(
                            order_id=order_id,
                            account_index=account_index,
                            account_email=account_email,
                            success=False,
                            error_message=error_msg,
                            response_time=response_time
                        )

                # 查询失败的情况
                error_msg = f"查询失败: {order_detail}"
                return OrderVerificationResult(
                    order_id=order_id,
                    account_index=account_index,
                    account_email=account_email,
                    success=False,
                    error_message=error_msg,
                    response_time=response_time
                )
                
            except Exception as e:
                response_time = time.perf_counter() - start_time
                return OrderVerificationResult(
                    order_id=order_id,
                    account_index=account_index,
                    account_email=account_email,
                    success=False,
                    error_message=f"查询异常: {e}",
                    response_time=response_time
                )
        
        # 并发查询所有订单
        tasks = [query_single_order(order_info) for order_info in order_data]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        verification_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                order_info = order_data[i]
                verification_results.append(OrderVerificationResult(
                    order_id=order_info["order_id"],
                    account_index=order_info.get("account_index"),
                    account_email=order_info.get("account_email"),
                    success=False,
                    error_message=f"并发查询异常: {result}",
                    response_time=0.0
                ))
            else:
                verification_results.append(result)
        
        return verification_results
    
    def calculate_dynamic_wait_time(self, num_orders: int, base_wait: int = 60) -> int:
        """
        计算动态等待时间
        
        Args:
            num_orders: 订单数量
            base_wait: 基础等待时间（秒）
            
        Returns:
            int: 动态等待时间（秒）
        """
        # 使用与单账户测试相同的计算逻辑
        dynamic_wait = max(base_wait, num_orders * 2)
        self.logger.info(f"📊 动态等待时间计算: max({base_wait}, {num_orders} × 2) = {dynamic_wait}秒")
        return dynamic_wait
