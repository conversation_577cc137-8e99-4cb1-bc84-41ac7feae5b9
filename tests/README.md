# Nine Trade Maker 测试框架

## 概述

Nine Trade Maker 测试框架是一个全面的测试系统，专门为 Nine CEX 交易所设计，提供单元测试、集成测试和性能测试功能。

## 测试架构

```
tests/
├── unit/                   # 单元测试
│   ├── test_nine_client.py    # Nine Client 单元测试
│   └── test_strategies.py     # 策略单元测试
├── integration/            # 集成测试
│   ├── test_batch_orders.py   # 批量订单集成测试
│   └── test_api_integration.py # API 集成测试
├── performance/            # 性能测试
│   ├── test_concurrent_performance.py # 并发性能测试
│   └── test_load_testing.py          # 负载测试
├── fixtures/               # 测试固件
│   ├── test_config.py         # 测试配置管理
│   └── sample_data.py         # 示例数据
├── utils/                  # 测试工具
│   ├── data_generator.py      # 数据生成器
│   ├── performance_monitor.py # 性能监控
│   └── test_helpers.py        # 测试辅助工具
├── conftest.py            # pytest 全局配置
├── pytest.ini            # pytest 配置文件
└── README.md             # 本文档
```

## 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install pytest pytest-cov pytest-html pytest-mock
```

配置环境变量（在 `.env` 文件中）：

```env
# Nine CEX API 配置
MM_NINE_API_KEY=your_api_key
MM_NINE_API_SECRET=your_api_secret
NINE_API_URL=https://api.nine.com

# 测试配置
TEST_ENVIRONMENT=development
TEST_TRADING_PAIR_ID=20250617000029
```

### 2. 运行测试

使用测试运行脚本：

```bash
# 运行所有测试
python run_tests.py all

# 运行单元测试
python run_tests.py unit

# 运行集成测试
python run_tests.py integration

# 运行性能测试
python run_tests.py performance

# 运行批量订单测试
python run_tests.py batch

# 运行快速测试（排除慢速测试）
python run_tests.py quick

# 生成覆盖率报告
python run_tests.py unit --coverage
```

或直接使用 pytest：

```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit
pytest -m integration
pytest -m performance

# 运行特定文件
pytest tests/unit/test_nine_client.py

# 详细输出
pytest -v

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

## 测试类型

### 单元测试

测试单个组件的功能，使用模拟对象避免外部依赖。

**特点：**
- 快速执行
- 隔离测试
- 高覆盖率
- 使用 mock 对象

**示例：**
```python
@pytest.mark.unit
def test_place_single_order_success(self, mock_nine_client):
    order_data = self.order_generator.generate_single_order()
    result = mock_nine_client.place_order(order_data)
    assert result["orderId"] is not None
```

### 集成测试

测试组件间的交互和真实 API 调用。

**特点：**
- 真实 API 调用
- 端到端测试
- 数据一致性验证
- 错误处理测试

**示例：**
```python
@pytest.mark.integration
@pytest.mark.batch
def test_max_batch_limit(self, nine_client):
    orders = self.order_generator.generate_balanced_orders(100)
    responses = nine_client.place_batch_orders(orders)
    assert len(responses) == 100
```

### 性能测试

测试系统在不同负载条件下的性能表现。

**特点：**
- 并发测试
- 负载测试
- 性能指标收集
- 阈值验证

**示例：**
```python
@pytest.mark.performance
@pytest.mark.concurrent
def test_concurrent_batch_orders(self, nine_client, concurrent_level):
    # 并发执行批量订单提交
    with ThreadPoolExecutor(max_workers=concurrent_level) as executor:
        futures = [executor.submit(submit_batch_orders) for _ in range(concurrent_level)]
```

## 测试标记

使用 pytest 标记来分类和选择测试：

- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.performance` - 性能测试
- `@pytest.mark.batch` - 批量订单测试
- `@pytest.mark.concurrent` - 并发测试
- `@pytest.mark.stress` - 压力测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.smoke` - 冒烟测试

## 测试配置

### 环境配置

测试框架支持多种环境配置：

- **开发环境** (`development`): 本地开发测试
- **生产环境** (`production`): 生产环境测试
- **CI环境** (`ci`): 持续集成测试

### 性能测试场景

预定义的性能测试场景：

```python
PERFORMANCE_SCENARIOS = {
    "light_load": {
        "batch_size": 10,
        "concurrent_requests": 5,
        "duration": 30
    },
    "normal_load": {
        "batch_size": 50,
        "concurrent_requests": 20,
        "duration": 60
    },
    "heavy_load": {
        "batch_size": 100,
        "concurrent_requests": 50,
        "duration": 120
    }
}
```

## 测试工具

### 数据生成器

- `OrderDataGenerator`: 生成测试订单数据
- `PerformanceDataGenerator`: 生成性能测试数据
- `MockDataGenerator`: 生成模拟数据

### 性能监控

- `PerformanceMonitor`: 单线程性能监控
- `ConcurrentPerformanceMonitor`: 多线程性能监控
- 支持响应时间、吞吐量、成功率等指标

### 测试辅助工具

- `OrderValidator`: 订单数据验证
- `TestDataCleaner`: 测试数据清理
- `ConcurrentExecutor`: 并发执行器
- `retry_on_failure`: 重试装饰器

## 性能基准

### 批量订单性能基准

| 批量大小 | 预期响应时间 | 最小成功率 |
|---------|-------------|-----------|
| 1       | < 1s        | 95%       |
| 10      | < 2s        | 90%       |
| 50      | < 5s        | 85%       |
| 100     | < 10s       | 80%       |

### 并发性能基准

| 并发级别 | 预期吞吐量 | 最小成功率 |
|---------|-----------|-----------|
| 5       | > 10 req/s | 90%       |
| 10      | > 15 req/s | 85%       |
| 20      | > 20 req/s | 80%       |
| 50      | > 25 req/s | 70%       |

## 测试报告

### 生成测试报告

```bash
# 生成完整测试报告
python run_tests.py report

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

### 报告位置

- **覆盖率报告**: `test_reports/coverage/index.html`
- **测试报告**: `test_reports/test_report.html`
- **性能报告**: `test_reports/performance/`
- **负载测试报告**: `test_reports/load_tests/`

## 最佳实践

### 1. 测试命名

- 使用描述性的测试名称
- 遵循 `test_<功能>_<场景>_<预期结果>` 格式
- 例如：`test_place_order_with_valid_data_should_succeed`

### 2. 测试组织

- 按功能模块组织测试文件
- 使用测试类组织相关测试
- 合理使用 setup 和 teardown 方法

### 3. 测试数据

- 使用数据生成器创建测试数据
- 避免硬编码测试数据
- 确保测试数据的多样性

### 4. 性能测试

- 设置合理的性能阈值
- 监控关键性能指标
- 记录性能基准数据

### 5. 错误处理

- 测试正常和异常情况
- 验证错误消息和状态码
- 使用重试机制处理网络不稳定

## 故障排除

### 常见问题

1. **环境变量未配置**
   ```
   解决方案：检查 .env 文件中的 API 配置
   ```

2. **API 限制错误**
   ```
   解决方案：降低并发级别或增加请求间隔
   ```

3. **网络超时**
   ```
   解决方案：增加超时时间或检查网络连接
   ```

4. **测试数据冲突**
   ```
   解决方案：使用测试数据清理工具
   ```

### 调试技巧

- 使用 `-v` 参数获取详细输出
- 使用 `--tb=short` 简化错误信息
- 使用 `-s` 参数显示 print 输出
- 使用 `--pdb` 进入调试模式

## 贡献指南

1. 添加新测试时，确保包含适当的测试标记
2. 更新性能基准时，记录变更原因
3. 添加新的测试工具时，更新文档
4. 确保所有测试都有清理机制

## 联系信息

如有问题或建议，请联系开发团队。
