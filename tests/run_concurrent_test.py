#!/usr/bin/env python3
"""
Nine CEX 真正并发交易测试 - 主入口点

这是Nine CEX真正并发交易测试的主入口，提供：
1. 真正的客户端并发策略（每个订单独立API调用）
2. 流动性订单匹配逻辑优化
3. 订单执行成功率验证
4. 性能指标收集
5. 清晰简化的测试流程

使用方法:
    python tests/run_concurrent_test.py

注意：已优化为使用本地并发而非Nine CEX原生batch API的串行处理。
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv(project_root / '.env')

from app.services.nine_client import NineClient
from app.services.trading_pair_manager import TradingPairManager
from tests.performance.test_effective_concurrent_trading import EffectiveConcurrentTradingTest
from tests.config.test_scales import (
    TestScale, TestScaleConfig, get_test_config, get_all_test_configs,
    parse_scale_from_string, get_scale_summary, TestScaleValidator
)


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('tests/logs/concurrent_test.log')
        ]
    )

    # 减少第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Nine CEX 并发交易测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
测试规模说明:
{get_scale_summary()}

使用示例:
  python tests/run_concurrent_test.py                    # 运行大规模测试（默认）
  python tests/run_concurrent_test.py --scale small     # 运行小规模测试
  python tests/run_concurrent_test.py --scale medium    # 运行中规模测试
  python tests/run_concurrent_test.py --scale large     # 运行大规模测试
  python tests/run_concurrent_test.py --scale all       # 运行所有规模测试
  python tests/run_concurrent_test.py --list-scales     # 显示所有可用规模
        """
    )

    parser.add_argument(
        '--scale', '-s',
        type=str,
        default='large',
        help='测试规模 (small/medium/large/all，默认: large)'
    )

    parser.add_argument(
        '--list-scales', '-l',
        action='store_true',
        help='显示所有可用的测试规模配置'
    )

    parser.add_argument(
        '--validate-config', '-v',
        action='store_true',
        help='验证测试配置并显示警告信息'
    )

    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='仅显示测试配置，不执行实际测试'
    )

    return parser.parse_args()


def validate_environment():
    """验证环境变量"""
    required_vars = [
        'MM_NINE_API_KEY',
        'MM_NINE_API_SECRET', 
        'NINE_API_URL'
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境变量验证通过")
    return True


def create_clients():
    """创建客户端实例"""
    try:
        api_key = os.getenv('MM_NINE_API_KEY')
        api_secret = os.getenv('MM_NINE_API_SECRET')
        api_url = os.getenv('NINE_API_URL')

        nine_client = NineClient(api_key=api_key, secret=api_secret, api_url=api_url)
        trading_pair_manager = TradingPairManager(nine_client)
        print("✅ 测试客户端创建完成")
        return nine_client, trading_pair_manager
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return None, None


def validate_order_values():
    """验证订单价值满足最小要求"""
    from decimal import Decimal
    
    # 从配置获取订单参数
    buy_price = Decimal("0.0000105")
    sell_price = Decimal("0.0000095") 
    buy_quantity = Decimal("10000")
    sell_quantity = Decimal("11000")
    min_value = Decimal("0.1")
    
    buy_value = buy_price * buy_quantity
    sell_value = sell_price * sell_quantity
    
    print("✅ USD价值验证通过:")
    print(f"   💰 买入订单: {buy_quantity} ETH × {buy_price} = {buy_value:.7f} USDT")
    print(f"   💰 卖出订单: {sell_quantity} ETH × {sell_price} = {sell_value:.7f} USDT")
    print(f"   ✅ 最小要求: {min_value} USDT")
    
    return buy_value >= min_value and sell_value >= min_value


async def run_single_scale_test(config: TestScaleConfig) -> Dict[str, Any]:
    """运行单个规模的测试"""
    logger = logging.getLogger(__name__)

    logger.info(f"🎯 开始{config.display_name}...")
    logger.info(f"📊 测试配置: {config.num_orders}订单, 全量并发, 预期{config.expected_duration}秒")
    logger.info(f"🎯 测试目的: {config.purpose}")

    # 1. 验证环境
    logger.info("✅ 环境变量验证通过")

    # 2. 创建测试客户端
    api_key = os.getenv('MM_NINE_API_KEY')
    api_secret = os.getenv('MM_NINE_API_SECRET')
    api_url = os.getenv('NINE_API_URL')

    nine_client = NineClient(api_key=api_key, secret=api_secret, api_url=api_url)
    trading_pair_manager = TradingPairManager(nine_client)
    test_instance = EffectiveConcurrentTradingTest(nine_client, trading_pair_manager)
    logger.info("✅ 测试客户端创建完成")

    # 3. 验证USD价值
    buy_price = 0.0000105
    sell_price = 0.0000095
    quantity = 10000

    buy_value = quantity * buy_price
    sell_value = quantity * sell_price

    logger.info("✅ USD价值验证通过:")
    logger.info(f"   💰 买入订单: {quantity} ETH × {buy_price} = {buy_value:.7f} USDT")
    logger.info(f"   💰 卖出订单: {quantity} ETH × {sell_price} = {sell_value:.7f} USDT")
    logger.info(f"   ✅ 最小要求: 0.1 USDT")

    # 4. 设置测试环境
    logger.info(f"🔧 设置{config.display_name}环境...")
    test_instance.setup_test_environment()

    # 5. 创建流动性订单
    logger.info("💧 创建流动性订单...")
    # 设置流动性倍数到测试实例的order_params中
    test_instance.order_params["liquidity_multiplier"] = config.liquidity_multiplier

    liquidity_order_id = test_instance.create_liquidity_order(num_buy_orders=config.num_orders)
    if not liquidity_order_id:
        raise Exception("流动性订单创建失败")
    logger.info("✅ 流动性订单已生效，可以开始交易")

    # 6. 运行并发测试
    logger.info(f"🚀 开始{config.display_name}并发交易测试...")
    logger.info(f"📊 运行{config.num_orders}订单测试: {config.num_orders}个主动买单 + 1个流动性卖单")
    logger.info(f"🎯 目标: {config.description}")
    logger.info(f"⚡ 使用全量并发模式 (asyncio.gather)")

    try:
        result = await test_instance.run_full_concurrent_test(
            num_orders=config.num_orders
        )
    except Exception as e:
        logger.error(f"❌ {config.display_name}执行失败: {e}")
        raise

    # 7. 生成报告
    logger.info(f"\n📄 生成{config.display_name}性能分析报告...")

    try:
        # 分析性能数据
        performance_summary = test_instance.performance_analyzer.analyze_performance()

        # 生成CSV报告
        order_details_file, summary_file = test_instance.performance_analyzer.generate_csv_reports(performance_summary)

        if order_details_file and summary_file:
            logger.info(f"✅ 订单详情报告: {order_details_file}")
            logger.info(f"✅ 性能摘要报告: {summary_file}")

    except Exception as e:
        logger.error(f"❌ {config.display_name}报告生成失败: {e}")

    # 8. 清理测试环境
    logger.info(f"🧹 清理{config.display_name}测试环境...")
    test_instance.cleanup_test_environment()

    logger.info(f"✅ {config.display_name}完成!")

    return {
        'config': config,
        'result': result,
        'performance_summary': performance_summary if 'performance_summary' in locals() else None,
        'reports': {
            'order_details': order_details_file if 'order_details_file' in locals() else None,
            'summary': summary_file if 'summary_file' in locals() else None
        }
    }


async def run_concurrent_test():
    """运行并发交易测试（保持向后兼容）"""
    logger = logging.getLogger(__name__)
    
    try:
        # 1. 验证环境
        if not validate_environment():
            return False
        
        # 2. 创建客户端
        nine_client, trading_pair_manager = create_clients()
        if not nine_client or not trading_pair_manager:
            return False
        
        # 3. 验证订单价值
        if not validate_order_values():
            logger.error("❌ 订单价值验证失败")
            return False
        
        # 4. 创建测试实例
        logger.info("🔧 设置测试环境...")
        test_instance = EffectiveConcurrentTradingTest(nine_client, trading_pair_manager)
        
        # 5. 设置测试环境
        test_instance.setup_test_environment()
        
        # 6. 创建流动性订单
        logger.info("💧 创建流动性订单...")
        liquidity_order_id = test_instance.create_liquidity_order()
        if not liquidity_order_id:
            raise Exception("流动性订单创建失败")
        
        # 7. 运行并发测试 (大规模测试)
        logger.info("🚀 开始大规模智能并发交易测试...")
        logger.info("📊 运行99订单大规模测试: 99个主动买单 + 1个流动性卖单")
        logger.info("🎯 目标: 验证Nine CEX平台限制下的最大并发性能")
        logger.info("⚡ 使用全量并发模式 (asyncio.gather)")

        try:
            result = await test_instance.run_full_concurrent_test(
                num_orders=99   # 99个订单大规模测试，全量并发
            )
        except Exception as e:
            logger.error(f"❌ 并发测试执行失败: {e}")
            import traceback
            traceback.print_exc()
            raise

        # 8. 分析结果
        logger.info("📈 性能分析:")
        logger.info("=" * 80)

        if result:
            logger.info(f"   📦 订单配置: 99 订单，全量并发")
            logger.info(f"   ⏱️  总耗时: {result.get('total_time', 0)*1000:.1f}ms")
            logger.info(f"   ⚡ 平均响应: {result.get('avg_response_time', 0)*1000:.1f}ms")
            logger.info(f"   🚀 吞吐量: {result.get('throughput_orders_per_second', 0):.1f} 订单/秒")
            logger.info(f"   ✅ 成功率: {result.get('success_rate', 0)*100:.1f}%")
            logger.info(f"   📈 提交订单: {result.get('successful_orders', 0)}/{result.get('total_orders', 0)}")

            # 大规模测试特殊指标
            total_submitted = result.get('total_orders_submitted', 0)
            if total_submitted >= 99:
                logger.info(f"   🎯 大规模测试: ✅ 成功处理 {total_submitted} 个订单")
            else:
                logger.info(f"   🎯 大规模测试: ⚠️ 仅处理 {total_submitted}/99 个订单")

            # 显示执行验证结果
            if result.get('execution_result'):
                exec_result = result['execution_result']
                logger.info(f"   🎯 执行验证:")
                logger.info(f"      ✅ 已成交: {exec_result['execution_count']}/{exec_result['total_orders']} ({exec_result['execution_rate']:.1%})")
                logger.info(f"      ⏳ 待成交: {exec_result['pending_count']} 个")
                logger.info(f"      ❌ 失败: {exec_result['failed_count']} 个")
                logger.info(f"      ⏱️  验证耗时: {exec_result['elapsed_time']:.1f}秒")

            # 检查响应时间目标
            avg_response_ms = result.get('avg_response_time', 0) * 1000
            if avg_response_ms < 2000:
                logger.info(f"   🎯 响应时间目标: ✅ 达成 ({avg_response_ms:.1f}ms < 2000ms)")
            else:
                logger.info(f"   🎯 响应时间目标: ❌ 未达成 ({avg_response_ms:.1f}ms >= 2000ms)")
        
        # 9. 生成报告
        logger.info("\n📄 生成性能分析报告...")

        try:
            # 分析性能数据
            performance_summary = test_instance.performance_analyzer.analyze_performance()

            # 生成CSV报告
            order_details_file, summary_file = test_instance.performance_analyzer.generate_csv_reports(performance_summary)

            if order_details_file and summary_file:
                logger.info(f"✅ 订单详情报告: {order_details_file}")
                logger.info(f"✅ 性能摘要报告: {summary_file}")
            else:
                logger.warning("⚠️ 报告生成失败或无数据")

            # 分析性能瓶颈
            bottleneck_analysis = test_instance.performance_analyzer.analyze_bottlenecks(performance_summary)
            if bottleneck_analysis:
                logger.info("🔍 性能瓶颈分析:")
                for bottleneck in bottleneck_analysis[:3]:  # 显示前3个瓶颈
                    logger.info(f"   ⚠️ {bottleneck}")

        except Exception as e:
            logger.error(f"❌ 报告生成失败: {e}")

        # 10. 清理测试环境
        logger.info("🧹 清理测试环境...")
        test_instance.cleanup_test_environment()
        
        logger.info("✅ 并发交易测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        return False


async def run_multi_scale_test() -> bool:
    """运行多规模测试并生成对比分析"""
    logger = logging.getLogger(__name__)

    logger.info("🎯 开始多规模并发交易测试...")
    logger.info("📊 将依次运行小规模、中规模、大规模测试")

    all_results = []

    try:
        # 运行所有规模的测试
        for config in get_all_test_configs():
            logger.info(f"\n{'='*80}")
            logger.info(f"🚀 开始 {config.display_name} ({config.name})")
            logger.info(f"{'='*80}")

            result = await run_single_scale_test(config)
            all_results.append(result)

            logger.info(f"✅ {config.display_name} 完成")

        # 生成对比分析报告
        logger.info(f"\n{'='*80}")
        logger.info("📊 生成多规模对比分析报告...")
        logger.info(f"{'='*80}")

        generate_comparison_report(all_results)

        logger.info("🎉 多规模测试全部完成!")
        return True

    except Exception as e:
        logger.error(f"❌ 多规模测试失败: {e}")
        return False


def generate_comparison_report(results: List[Dict[str, Any]]):
    """生成多规模测试对比分析报告"""
    logger = logging.getLogger(__name__)

    logger.info("📈 多规模性能对比分析:")
    logger.info("-" * 100)
    logger.info(f"{'规模':<12} {'订单数':<8} {'吞吐量':<12} {'响应时间':<12} {'成功率':<8} {'报告文件'}")
    logger.info("-" * 100)

    for result in results:
        config = result['config']
        perf = result.get('performance_summary')
        reports = result.get('reports', {})

        if perf:
            throughput = f"{perf.throughput_orders_per_second:.2f}/s"
            response_time = f"{perf.avg_api_response_time:.0f}ms"
            success_rate = f"{perf.execution_rate:.1f}%"
        else:
            throughput = "N/A"
            response_time = "N/A"
            success_rate = "N/A"

        report_file = reports.get('summary', 'N/A')
        if report_file:
            report_file = os.path.basename(report_file)

        logger.info(f"{config.display_name:<12} {config.num_orders:<8} {throughput:<12} {response_time:<12} {success_rate:<8} {report_file}")

    logger.info("-" * 100)
    logger.info("💡 分析建议:")

    # 简单的性能趋势分析
    if len(results) >= 2:
        small_result = next((r for r in results if r['config'].name == 'small'), None)
        large_result = next((r for r in results if r['config'].name == 'large'), None)

        if small_result and large_result:
            small_perf = small_result.get('performance_summary')
            large_perf = large_result.get('performance_summary')

            if small_perf and large_perf:
                throughput_ratio = large_perf.throughput_orders_per_second / small_perf.throughput_orders_per_second
                response_ratio = large_perf.avg_api_response_time / small_perf.avg_api_response_time

                logger.info(f"   📊 大规模vs小规模吞吐量比: {throughput_ratio:.2f}x")
                logger.info(f"   📊 大规模vs小规模响应时间比: {response_ratio:.2f}x")

                if throughput_ratio > 0.8:
                    logger.info("   ✅ 系统在大规模下保持良好的吞吐量")
                else:
                    logger.info("   ⚠️ 大规模测试下吞吐量显著下降，需要优化")

                if response_ratio < 2.0:
                    logger.info("   ✅ 系统在大规模下响应时间控制良好")
                else:
                    logger.info("   ⚠️ 大规模测试下响应时间显著增加，需要优化")


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    # 解析命令行参数
    args = parse_arguments()

    # 处理特殊命令
    if args.list_scales:
        print(get_scale_summary())
        return 0

    if args.validate_config:
        warnings = TestScaleValidator.validate_all_configs()
        if warnings:
            print("⚠️ 配置警告:")
            for scale_name, scale_warnings in warnings.items():
                print(f"  {scale_name}: {', '.join(scale_warnings)}")
        else:
            print("✅ 所有配置验证通过")
        return 0

    logger.info("🚀 启动Nine CEX并发交易测试...")

    try:
        # 创建必要目录
        os.makedirs('tests/logs', exist_ok=True)
        os.makedirs('tests/reports', exist_ok=True)

        # 解析测试规模
        try:
            scale = parse_scale_from_string(args.scale)
        except ValueError as e:
            logger.error(f"❌ {e}")
            return 1

        # 显示测试配置
        if scale == TestScale.ALL:
            configs = get_all_test_configs()
            logger.info("📊 将运行所有规模测试:")
            for config in configs:
                logger.info(f"   🎯 {config.display_name}: {config.num_orders}订单, {config.purpose}")
        else:
            config = get_test_config(scale)
            logger.info(f"📊 将运行{config.display_name}:")
            logger.info(f"   🎯 配置: {config.num_orders}订单, 全量并发")
            logger.info(f"   🎯 目的: {config.purpose}")

        if args.dry_run:
            logger.info("🔍 仅显示配置，跳过实际测试执行")
            return 0

        # 运行测试
        if scale == TestScale.ALL:
            success = asyncio.run(run_multi_scale_test())
        else:
            config = get_test_config(scale)
            result = asyncio.run(run_single_scale_test(config))
            success = result is not None

        if success:
            logger.info("🎉 测试成功完成!")
            return 0
        else:
            logger.error("❌ 测试失败!")
            return 1

    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
