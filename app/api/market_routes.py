from flask import Blueprint, request, jsonify, current_app

market_bp = Blueprint('market_bp', __name__, url_prefix='/api/market')

@market_bp.route('/outer_pool_info', methods=['GET'])
def get_outer_pool_info_route():
    """
    获取外部交易对行情信息。
    查询参数:
    - apiKey (str): 用户API密钥 (必需)
    - secret (str): 用户密钥 (必需)
    - groupName (str, optional): 交易对组名，如 "SEPUSDT"
    - tradePairType (int, optional): 交易对类型，默认为 1
    - pageNum (int, optional): 页码，默认为 1
    - pageSize (int, optional): 每页数量，默认为 100
    """
    nine_client = current_app.nine_client

    # 获取必需的API认证参数
    api_key = request.args.get('apiKey')
    secret = request.args.get('secret')
    
    if not api_key or not secret:
        return jsonify({"code": 400, "message": "必须提供 apiKey 和 secret 查询参数", "data": None}), 400

    # 获取可选参数
    group_name = request.args.get('groupName')

    try:
        page_num = int(request.args.get('pageNum', 1))
        page_size = int(request.args.get('pageSize', 100))
        trade_pair_type = int(request.args.get('tradePairType', 1))
    except ValueError:
        return jsonify({"code": 400, "message": "pageNum, pageSize 和 tradePairType 必须是整数", "data": None}), 400
    
    # 验证 tradePairType
    if trade_pair_type not in [1, 2]:
        return jsonify({"code": 400, "message": "tradePairType 必须是 1 或 2", "data": None}), 400
    
    try:
        # 根据是否提供 groupName 决定使用哪个方法
        if group_name:
            response_data = nine_client.get_outer_pool_info_by_group(
                user_api_key=api_key,
                user_api_secret=secret,
                group_name=group_name,
                trade_pair_type=trade_pair_type,
                page_num=page_num, 
                page_size=page_size
            )
        else:
            response_data = nine_client.get_outer_pool_info_all(
                user_api_key=api_key,
                user_api_secret=secret,
                trade_pair_type=trade_pair_type,
                page_num=page_num, 
                page_size=page_size
            )
        
        if response_data:
            return jsonify({"code": 200, "message": "成功", "data": response_data}), 200
        else:
            return jsonify({"code": 500, "message": "获取交易对信息失败", "data": None}), 500
            
    except ValueError as ve:
        return jsonify({"code": 400, "message": f"参数错误: {ve}", "data": None}), 400
    except ConnectionError as ce:
        current_app.logger.error(f"❌ [NINE CEX] 连接失败: {ce}")
        return jsonify({"code": 503, "message": f"服务暂时不可用: {ce}", "data": None}), 503
    except RuntimeError as re:
        current_app.logger.error(f"❌ [NINE CEX] 运行时错误: {re}")
        return jsonify({"code": 500, "message": f"内部服务器错误: {re}", "data": None}), 500
    except Exception as e:
        current_app.logger.error(f"❌ [NINE CEX] 未知错误: {e}")
        return jsonify({"code": 500, "message": f"发生未知错误: {e}", "data": None}), 500

@market_bp.route('/orders_trade_detail', methods=['GET'])
def get_orders_trade_detail_route():
    """
    获取指定交易对的成交详情。
    查询参数:
    - apiKey (str): 用户API密钥 (必需)
    - secret (str): 用户密钥 (必需)
    - tradePairId (str): 交易对ID (必需)，来自 getOuterPoolInfo 接口返回的 id 字段
    - pageNum (int, optional): 页码，默认为 1
    - pageSize (int, optional): 每页数量，默认为 20
    """
    nine_client = current_app.nine_client

    # 获取必需的API认证参数
    api_key = request.args.get('apiKey')
    secret = request.args.get('secret')
    trade_pair_id = request.args.get('tradePairId')
    
    if not api_key or not secret:
        return jsonify({"code": 400, "message": "必须提供 apiKey 和 secret 查询参数", "data": None}), 400
    
    if not trade_pair_id:
        return jsonify({"code": 400, "message": "必须提供 tradePairId 查询参数", "data": None}), 400

    try:
        page_num = int(request.args.get('pageNum', 1))
        page_size = int(request.args.get('pageSize', 20))
    except ValueError:
        return jsonify({"code": 400, "message": "pageNum 和 pageSize 必须是整数", "data": None}), 400
    
    try:
        response_data = nine_client.get_orders_trade_detail(
            trade_pair_id=trade_pair_id,
            user_api_key=api_key,
            user_api_secret=secret,
            page_num=page_num,
            page_size=page_size
        )
        
        if response_data:
            return jsonify({"code": 200, "message": "成功", "data": response_data}), 200
        else:
            return jsonify({"code": 500, "message": "获取成交详情失败", "data": None}), 500
            
    except ValueError as ve:
        return jsonify({"code": 400, "message": f"参数错误: {ve}", "data": None}), 400
    except ConnectionError as ce:
        current_app.logger.error(f"❌ [NINE CEX] 连接失败: {ce}")
        return jsonify({"code": 503, "message": f"服务暂时不可用: {ce}", "data": None}), 503
    except RuntimeError as re:
        current_app.logger.error(f"❌ [NINE CEX] 运行时错误: {re}")
        return jsonify({"code": 500, "message": f"内部服务器错误: {re}", "data": None}), 500
    except Exception as e:
        current_app.logger.error(f"❌ [NINE CEX] 未知错误: {e}")
        return jsonify({"code": 500, "message": f"发生未知错误: {e}", "data": None}), 500 