from flask import Blueprint, request, jsonify, current_app
import os

binance_bp = Blueprint('binance_bp', __name__, url_prefix='/api/binance')

@binance_bp.route('/order_book', methods=['GET'])
def get_binance_order_book():
    """
    从币安获取指定交易对的订单薄数据。
    查询参数:
    - symbol (str): 交易对标识，例如 "BTCUSDT" (必需)
    - limit (int, optional): 返回的深度档位数。默认 100。
                             有效值: [5, 10, 20, 50, 100, 500, 1000, 5000]。
    """
    # Log environment variables for proxies


    binance_client = current_app.binance_client
    if not binance_client:
        return jsonify({"code": 503, "message": "Binance 服务客户端未初始化", "data": None}), 503

    symbol = request.args.get('symbol', default=current_app.config.get('MM_BINANCE_SYMBOL', 'BTCUSDT'))
    limit = request.args.get('limit', default=5, type=int)

    if not symbol:
        return jsonify({"code": 400, "message": "交易对(symbol)参数是必需的。"}), 400
    
    # 验证 limit 是否在允许的范围内 (币安API通常有特定允许的值)
    # 例如: [5, 10, 20, 50, 100, 500, 1000, 5000]
    # 这里我们先简单传递，实际应用中可能需要更严格的验证或调整
    allowed_limits = [1, 5, 10, 20, 50, 100, 500, 1000, 5000] # 假设1也是允许的用于测试
    if limit not in allowed_limits:
        # 为了简单起见，如果不在允许范围内，可以默认一个值或返回错误
        # current_app.logger.warning(f"订单薄深度 limit {limit} 不在允许值内，将使用默认值 5")
        # limit = 5 
        # 或者返回错误:
        return jsonify({
            "code": 400, 
            "message": f"参数 'limit' 的值 {limit} 无效。允许的值为: {allowed_limits}"
        }), 400

    try:
        order_book = binance_client.get_order_book(symbol=symbol, limit=limit)
        if order_book:
            return jsonify(order_book), 200
        else:
            current_app.logger.error(f"❌ [BINANCE] 获取 {symbol} 订单簿失败")
            return jsonify({"code": 500, "data": None, "message": "从币安获取订单薄数据失败"}), 500

    except ConnectionError as e:
        current_app.logger.error(f"❌ [BINANCE] 连接失败: {e}")
        return jsonify({"code": 502, "data": None, "message": f"币安API请求失败: {e}"}), 502
    except Exception as e:
        current_app.logger.error(f"❌ [BINANCE] 未知错误: {e}")
        return jsonify({"code": 500, "data": None, "message": f"获取币安订单薄时发生内部服务器错误: {e}"}), 500 