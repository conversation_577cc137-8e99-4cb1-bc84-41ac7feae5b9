from flask import Blueprint, request, jsonify, current_app

trade_bp = Blueprint('trade_bp', __name__, url_prefix='/api/trade')

@trade_bp.route('/batch', methods=['POST'])
def batch_trade_route():
    """
    执行批量交易。
    
    请求体格式:
    [
        {
            "userBean": {
                "apiKey": "用户API密钥",
                "secret": "用户密钥"
            },
            "tradeBean": [
                {
                    "accountType": 1,
                    "tradePairName": "交易对名称",
                    "tradePairId": "交易对ID (**************格式，支持自动转换)",
                    "orderDirection": 1,  // 1=买入, 2=卖出
                    "orderType": 1,       // 1=限价单
                    "orderQuantity": "0.1",
                    "orderPrice": "50000"
                }
            ]
        }
    ]
    
    返回格式:
    {
        "code": 200,
        "message": "成功",
        "data": {...}
    }
    """
    nine_client = current_app.nine_client
    
    if not nine_client:
        return jsonify({"code": 500, "message": "Nine CEX客户端不可用", "data": None}), 500
    
    try:
        # 获取请求数据
        trade_operations = request.get_json()
        
        if not isinstance(trade_operations, list):
            return jsonify({"code": 400, "message": "请求体必须是数组格式", "data": None}), 400
        
        if not trade_operations:
            return jsonify({"code": 400, "message": "请求体不能为空", "data": None}), 400
        
        # 执行批量交易
        response_data = nine_client.batch_trade(trade_operations)
        
        if response_data:
            return jsonify({"code": 200, "message": "成功", "data": response_data}), 200
        else:
            return jsonify({"code": 500, "message": "批量交易执行失败", "data": None}), 500
            
    except ValueError as ve:
        current_app.logger.error(f"❌ [批量交易] 参数错误: {ve}")
        return jsonify({"code": 400, "message": f"参数错误: {ve}", "data": None}), 400
    except ConnectionError as ce:
        current_app.logger.error(f"❌ [批量交易] 连接失败: {ce}")
        return jsonify({"code": 503, "message": f"服务暂时不可用: {ce}", "data": None}), 503
    except RuntimeError as re:
        current_app.logger.error(f"❌ [批量交易] 运行时错误: {re}")
        return jsonify({"code": 500, "message": f"内部服务器错误: {re}", "data": None}), 500
    except Exception as e:
        current_app.logger.error(f"❌ [批量交易] 未知错误: {e}")
        return jsonify({"code": 500, "message": f"发生未知错误: {e}", "data": None}), 500

@trade_bp.route('/health', methods=['GET'])
def trade_health_check():
    """交易模块健康检查"""
    nine_client = current_app.nine_client
    
    status = {
        "nine_client_available": nine_client is not None,
        "module": "trade",
        "endpoints": [
            "/api/trade/batch",
            "/api/trade/health"
        ]
    }
    
    if nine_client:
        return jsonify({"code": 200, "message": "交易模块正常", "data": status}), 200
    else:
        return jsonify({"code": 503, "message": "Nine CEX客户端不可用", "data": status}), 503 