from flask import Blueprint, request, jsonify, current_app

inner_pool_bp = Blueprint('inner_pool_bp', __name__, url_prefix='/api/inner-pool')

@inner_pool_bp.route('/info', methods=['GET'])
def get_inner_pool_info_route():
    """
    获取内盘信息。
    查询参数:
    - apiKey (str): 用户API密钥 (必需)
    - secret (str): 用户密钥 (必需)
    - currentPage (int, optional): 当前页码，默认为 1
    - pageSize (int, optional): 每页数量，默认为 20
    - chain (str, optional): 链名称，如 "Solana"
    """
    nine_client = current_app.nine_client

    if not nine_client:
        return jsonify({"code": 503, "message": "Nine CEX客户端未初始化", "data": None}), 503

    # 获取必需的API认证参数
    api_key = request.args.get('apiKey')
    secret = request.args.get('secret')
    
    if not api_key or not secret:
        return jsonify({"code": 400, "message": "必须提供 apiKey 和 secret 查询参数", "data": None}), 400

    # 获取可选参数
    chain = request.args.get('chain')

    try:
        current_page = int(request.args.get('currentPage', 1))
        page_size = int(request.args.get('pageSize', 20))
    except ValueError:
        return jsonify({"code": 400, "message": "currentPage 和 pageSize 必须是整数", "data": None}), 400
    
    try:
        response_data = nine_client.get_inner_pool_info(
            user_api_key=api_key,
            user_api_secret=secret,
            current_page=current_page,
            page_size=page_size,
            chain=chain
        )
        
        if response_data:
            return jsonify({"code": 200, "message": "成功", "data": response_data}), 200
        else:
            return jsonify({"code": 500, "message": "获取内盘信息失败", "data": None}), 500
            
    except ValueError as ve:
        return jsonify({"code": 400, "message": f"参数错误: {ve}", "data": None}), 400
    except ConnectionError as ce:
        current_app.logger.error(f"❌ [NINE CEX] 连接失败: {ce}")
        return jsonify({"code": 503, "message": f"服务暂时不可用: {ce}", "data": None}), 503
    except RuntimeError as re:
        current_app.logger.error(f"❌ [NINE CEX] 运行时错误: {re}")
        return jsonify({"code": 500, "message": f"内部服务器错误: {re}", "data": None}), 500
    except Exception as e:
        current_app.logger.error(f"❌ [NINE CEX] 未知错误: {e}")
        return jsonify({"code": 500, "message": f"发生未知错误: {e}", "data": None}), 500

@inner_pool_bp.route('/trade', methods=['POST'])
def batch_inner_disk_trade_route():
    """
    执行批量内盘交易。
    预期请求体 JSON (直接是一个列表):
    [
        {
            "userBean": {"apiKey": "...", "secret": "..."},
            "innerTradeBean": [
                {
                    "poolId": 1,
                    "amount": "1",
                    "tradeType": "BUY"  // BUY 或 SELL
                },
                // ... 更多交易详情
            ]
        },
        // ... 更多用户的交易操作
    ]
    """
    nine_client = current_app.nine_client

    if not nine_client:
        return jsonify({"code": 503, "message": "Nine CEX客户端未初始化", "data": None}), 503

    trade_operations = request.get_json()  # 请求体直接是列表

    if not isinstance(trade_operations, list):
        return jsonify({"code": 400, "message": "请求体必须是一个内盘交易操作的列表", "data": None}), 400

    # 验证请求体结构
    try:
        for i, operation in enumerate(trade_operations):
            if not isinstance(operation, dict):
                return jsonify({"code": 400, "message": f"操作#{i+1}必须是一个字典", "data": None}), 400
            
            # 验证 userBean
            user_bean = operation.get("userBean")
            if not isinstance(user_bean, dict) or not user_bean.get("apiKey") or not user_bean.get("secret"):
                return jsonify({"code": 400, "message": f"操作#{i+1}缺少有效的用户认证信息", "data": None}), 400
            
            # 验证 innerTradeBean
            inner_trade_bean = operation.get("innerTradeBean")
            if not isinstance(inner_trade_bean, list) or not inner_trade_bean:
                return jsonify({"code": 400, "message": f"操作#{i+1}必须包含至少一个交易详情", "data": None}), 400
            
            for j, trade_detail in enumerate(inner_trade_bean):
                if not isinstance(trade_detail, dict):
                    return jsonify({"code": 400, "message": f"操作#{i+1}的交易#{j+1}必须是一个字典", "data": None}), 400
                
                # 验证必需字段
                required_fields = ["poolId", "amount", "tradeType"]
                for field in required_fields:
                    if field not in trade_detail:
                        return jsonify({"code": 400, "message": f"操作#{i+1}的交易#{j+1}缺少必需字段: {field}", "data": None}), 400
                
                # 验证 tradeType
                if trade_detail["tradeType"] not in ["BUY", "SELL"]:
                    return jsonify({"code": 400, "message": f"操作#{i+1}的交易#{j+1}的tradeType必须是BUY或SELL", "data": None}), 400
                
                # 验证 poolId 是数字
                try:
                    int(trade_detail["poolId"])
                except (ValueError, TypeError):
                    return jsonify({"code": 400, "message": f"操作#{i+1}的交易#{j+1}的poolId必须是数字", "data": None}), 400

    except Exception as e:
        return jsonify({"code": 400, "message": f"请求体验证失败: {e}", "data": None}), 400

    try:
        response = nine_client.batch_inner_disk_trade(trade_operations=trade_operations)
        
        # 返回响应
        if isinstance(response, dict):
            status_code = response.get('code', 200) if response.get('code') else 200
            return jsonify(response), status_code
        else:
            return jsonify({"code": 500, "message": "API响应格式错误", "data": None}), 500

    except ValueError as ve:
        return jsonify({"code": 400, "message": f"参数错误: {ve}", "data": None}), 400
    except ConnectionError as ce:
        current_app.logger.error(f"❌ [NINE CEX] 连接失败: {ce}")
        return jsonify({"code": 503, "message": f"服务暂时不可用: {ce}", "data": None}), 503
    except RuntimeError as re:
        current_app.logger.error(f"❌ [NINE CEX] 运行时错误: {re}")
        return jsonify({"code": 500, "message": f"内部服务器错误: {re}", "data": None}), 500
    except Exception as e:
        current_app.logger.error(f"❌ [NINE CEX] 未知错误: {e}")
        return jsonify({"code": 500, "message": f"发生未知错误: {e}", "data": None}), 500 