"""
价格搬平策略 (MirrorPriceStrategy)

专注于价格同步的简洁策略，支持从任意价格源（CEX/DEX）同步到Nine CEX。
核心功能：
- 从Binance或Raydium获取价格
- 应用价格偏移
- 通过小额订单推动Nine CEX价格到目标位置
"""

from decimal import Decimal
from typing import List, Dict, Optional, Any
import logging

from .base_strategy import BaseStrategy
from app.services.nine_client import NineClient
from app.services.binance_client import BinanceClient
from app.services.raydium_client import RaydiumClient


class MirrorPriceStrategy(BaseStrategy):
    """价格搬平策略 - 专注于价格同步"""
    
    # 策略常量
    SAFE_BALANCE_RATIO = Decimal("0.9")        # 90%安全余额使用比例
    PRICE_DEVIATION_THRESHOLD = Decimal("0.02")  # 2%价格偏离阈值
    MIN_TOKEN_QUANTITY = Decimal("1000")       # 最小代币数量
    MARKET_IMPACT_RATIO = Decimal("0.5")       # 50%盘口冲击保护比例
    MIN_ORDER_AMOUNT = Decimal("1")            # 最小订单金额(USDT)
    BUY_PRICE_ADJUSTMENT = Decimal("0.999")    # 买单价格调整系数
    SELL_PRICE_ADJUSTMENT = Decimal("1.001")   # 卖单价格调整系数
    BID_PRICE_STEP = Decimal("0.9999")         # 买一价微调系数  
    ASK_PRICE_STEP = Decimal("1.0001")         # 卖一价微调系数
    
    def __init__(self, config: Dict[str, Any], nine_client: NineClient, 
                 binance_client: BinanceClient = None, logger: Optional[logging.Logger] = None):
        super().__init__(config, nine_client, logger)
        
        # 客户端初始化
        self.binance_client = binance_client
        self.raydium_client = RaydiumClient() if self._is_raydium_source() else None
        
        # API凭证 - 优先使用通用API密钥
        self.api_key = config.get("NINE_API_KEY") or config.get("MP_NINE_API_KEY")
        self.api_secret = config.get("NINE_API_SECRET") or config.get("MP_NINE_API_SECRET")
        
        if not self.api_key or not self.api_secret:
            raise ValueError("MP策略需要 NINE_API_KEY 和 NINE_API_SECRET")
            
        self.logger.info(f"🔑 API配置完成: {self.api_key[:8]}...")
        
        # 策略配置
        self.price_source = self._get_config_value("MP_PRICE_SOURCE", "binance", str)
        self.source_symbol = self._get_config_value("MP_SOURCE_SYMBOL", "BTCUSDT", str) 
        self.target_symbol = self._get_config_value("MP_TARGET_SYMBOL", "BTC/USDT", str)
        self.trading_pair = self.target_symbol  # 设置 trading_pair 供订单监控使用
        self.raydium_contract = self._get_config_value("MP_RAYDIUM_CONTRACT", None, str)
        
        # 同步参数
        self.price_offset = self._get_config_value("MP_PRICE_OFFSET", Decimal("0"), Decimal)
        self.sync_amount = self._get_config_value("MP_SYNC_AMOUNT", Decimal("10"), Decimal)
        
        # 精度配置
        self.price_precision = self._get_config_value("MP_PRICE_PRECISION", 8, int)
        self.qty_precision = self._get_config_value("MP_QTY_PRECISION", 2, int)
        
        # 订单簿配置
        self.fetch_nine_book_symbol = self.target_symbol
        self.fetch_nine_book_precision = f"{10 ** -self.price_precision:.8f}"
        self.fetch_nine_book_depth = 5
        
        self.logger.info(f"💱 价格搬平策略启动: {self.price_source.upper()} -> Nine CEX")
        self.logger.info(f"📊 {self.source_symbol} -> {self.target_symbol} (偏移: {float(self.price_offset)*100:+.2f}%)")
    
    def _is_raydium_source(self) -> bool:
        """判断是否使用Raydium作为价格源"""
        source = self.config.get("MP_PRICE_SOURCE", "binance").lower()
        return source in ["raydium", "dex"]
    
    def _get_source_price(self) -> Optional[Decimal]:
        """获取源价格"""
        if self.price_source.lower() == "binance":
            return self._get_binance_price()
        elif self.price_source.lower() == "raydium":
            return self._get_raydium_price()
        else:
            self.logger.error(f"❌ 未知的价格源: {self.price_source}")
            return None
    
    def _get_binance_price(self) -> Optional[Decimal]:
        """从Binance获取价格（通过订单簿中间价）"""
        if not self.binance_client:
            self.logger.error("❌ Binance客户端未初始化")
            return None
            
        try:
            # 通过订单簿获取价格
            order_book = self.binance_client.get_order_book(self.source_symbol, limit=5)
            if order_book and 'bids' in order_book and 'asks' in order_book:
                bids = order_book.get('bids', [])
                asks = order_book.get('asks', [])
                
                if bids and asks:
                    # 计算中间价
                    best_bid = Decimal(str(bids[0][0]))
                    best_ask = Decimal(str(asks[0][0])) 
                    mid_price = (best_bid + best_ask) / 2
                    self.logger.debug(f"📈 Binance价格: {self.source_symbol} = ${mid_price:.8f} (买{best_bid}/卖{best_ask})")
                    return mid_price
        except Exception as e:
            self.logger.error(f"❌ 获取Binance价格失败: {e}")
        
        return None
    
    def _get_raydium_price(self) -> Optional[Decimal]:
        """从Raydium获取价格"""
        if not self.raydium_client:
            self.logger.error("❌ Raydium客户端未初始化")
            return None
            
        try:
            # 使用合约地址或交易对名称
            if self.raydium_contract:
                token_identifier = self.raydium_contract
            else:
                # 从"二哈/USDT"中提取"二哈"部分
                if "/" in self.source_symbol:
                    token_identifier = self.source_symbol.split("/")[0]
                else:
                    token_identifier = self.source_symbol.replace("USDT", "").replace("usdt", "")
            
            price = self.raydium_client.get_token_price(token_identifier, "USDT")
            
            if price and price > 0:
                self.logger.debug(f"🌊 Raydium价格: {token_identifier} = ${price:.8f}")
                return price
        except Exception as e:
            self.logger.error(f"❌ 获取Raydium价格失败: {e}")
        
        return None
    
    def _calculate_target_price(self, source_price: Decimal) -> Decimal:
        """计算目标价格（应用偏移）"""
        target_price = source_price * (Decimal("1") + self.price_offset)
        self.logger.debug(f"🎯 目标价格: ${source_price:.8f} -> ${target_price:.8f} (偏移: {float(self.price_offset)*100:+.2f}%)")
        return target_price
    
    def _check_balance_and_create_orders(self, target_price: Decimal) -> List[Dict]:
        """基于余额检查创建价格同步订单"""
        orders = []
        
        # 1. 获取交易对余额
        balances = self.get_trading_pair_balances()
        if not balances:
            self.logger.warning("⚠️ 无法获取余额信息，按原计划下单（可能失败）")
            return self._create_orders_without_check(target_price)
        
        # 2. 解析交易对
        base_asset, quote_asset = self.target_symbol.split('/')
        base_balance = balances.get(base_asset, {})
        quote_balance = balances.get(quote_asset, {})
        
        base_available = Decimal(str(base_balance.get('available', 0)))
        quote_available = Decimal(str(quote_balance.get('available', 0)))
        
        self.logger.info(f"💳 当前余额: {base_asset}={base_available:.0f}, {quote_asset}={quote_available:.0f}")
        
        # 3. 创建买单检查（需要quote_asset如USDT）
        buy_price = target_price * Decimal("0.999")  # 低0.1%
        buy_amount = self.sync_amount
        safe_quote_balance = quote_available * Decimal("0.9")  # 90%安全使用
        
        if safe_quote_balance >= buy_amount:
            # 余额充足，按原计划金额下单
            buy_qty = buy_amount / buy_price
            buy_order = self.create_trade_bean(
                trading_pair=self.target_symbol,
                order_direction=1,
                order_quantity=self._format_quantity(buy_qty, self.qty_precision),
                order_price=self._format_price(buy_price, self.price_precision)
            )
            orders.append(buy_order)
            self.logger.info(f"✅ 买单: ${buy_amount:.0f} @ ${buy_price:.8f}")
        elif safe_quote_balance >= Decimal("1"):
            # 余额不足但够下小单，调整金额
            adjusted_amount = safe_quote_balance
            buy_qty = adjusted_amount / buy_price  
            buy_order = self.create_trade_bean(
                trading_pair=self.target_symbol,
                order_direction=1,
                order_quantity=self._format_quantity(buy_qty, self.qty_precision),
                order_price=self._format_price(buy_price, self.price_precision)
            )
            orders.append(buy_order)
            self.logger.info(f"⚡ 买单调整: ${adjusted_amount:.1f} @ ${buy_price:.8f} (余额限制)")
        else:
            self.logger.warning(f"❌ 跳过买单: {quote_asset}余额不足 ({quote_available:.1f} < {buy_amount:.0f})")
        
        # 4. 创建卖单检查（需要base_asset如SEPBTC）  
        sell_price = target_price * Decimal("1.001")  # 高0.1%
        sell_qty_needed = self.sync_amount / sell_price
        safe_base_balance = base_available * Decimal("0.9")  # 90%安全使用
        
        if safe_base_balance >= sell_qty_needed:
            # 余额充足，按原计划下单
            sell_order = self.create_trade_bean(
                trading_pair=self.target_symbol,
                order_direction=2,
                order_quantity=self._format_quantity(sell_qty_needed, self.qty_precision),
                order_price=self._format_price(sell_price, self.price_precision)
            )
            orders.append(sell_order)
            self.logger.info(f"✅ 卖单: {sell_qty_needed:.0f} @ ${sell_price:.8f}")
        elif safe_base_balance >= Decimal("1000"):  # 至少1000个代币
            # 余额不足但够下小单，调整数量
            adjusted_qty = safe_base_balance
            sell_order = self.create_trade_bean(
                trading_pair=self.target_symbol,
                order_direction=2,
                order_quantity=self._format_quantity(adjusted_qty, self.qty_precision),
                order_price=self._format_price(sell_price, self.price_precision)
            )
            orders.append(sell_order)
            self.logger.info(f"⚡ 卖单调整: {adjusted_qty:.0f} @ ${sell_price:.8f} (余额限制)")
        else:
            self.logger.warning(f"❌ 跳过卖单: {base_asset}余额不足 ({base_available:.0f} < {sell_qty_needed:.0f})")
        
        # 5. 总结
        if not orders:
            self.logger.error("❌ 所有方向余额都不足，无法下单")
        else:
            direction_count = len([o for o in orders if o.get("orderDirection") == 1])
            sell_count = len(orders) - direction_count
            self.logger.info(f"💼 生成订单: {direction_count}买单 + {sell_count}卖单")
        
        return orders
    
    def _create_orders_without_check(self, target_price: Decimal) -> List[Dict]:
        """无余额检查的订单创建（备用方案）"""
        orders = []
        
        # 创建买单
        buy_price = target_price * Decimal("0.999")
        buy_qty = self.sync_amount / buy_price
        buy_order = self.create_trade_bean(
            trading_pair=self.target_symbol,
            order_direction=1,
            order_quantity=self._format_quantity(buy_qty, self.qty_precision),
            order_price=self._format_price(buy_price, self.price_precision)
        )
        orders.append(buy_order)
        
        # 创建卖单
        sell_price = target_price * Decimal("1.001")
        sell_qty = self.sync_amount / sell_price
        sell_order = self.create_trade_bean(
            trading_pair=self.target_symbol,
            order_direction=2,
            order_quantity=self._format_quantity(sell_qty, self.qty_precision),
            order_price=self._format_price(sell_price, self.price_precision)
        )
        orders.append(sell_order)
        
        self.logger.info(f"💰 标准订单: 买@${buy_price:.8f} + 卖@${sell_price:.8f}")
        return orders
    
    def _analyze_order_book(self, nine_cex_order_book_data: Optional[Dict]) -> Dict:
        """
        分析订单簿数据，返回关键市场信息
        
        Returns:
            Dict: 包含最佳买卖价格、数量等信息
        """
        if not nine_cex_order_book_data:
            return {}
            
        nine_asks = nine_cex_order_book_data.get("asks", [])
        nine_bids = nine_cex_order_book_data.get("bids", [])
        
        if not nine_asks or not nine_bids:
            self.logger.warning("⚠️ 订单簿数据不完整")
            return {}
            
        try:
            # 解析最佳买卖价格和数量
            best_ask_price = Decimal(str(nine_asks[0][0]))  # 卖一价
            best_ask_qty = Decimal(str(nine_asks[0][1]))    # 卖一量
            best_bid_price = Decimal(str(nine_bids[0][0]))  # 买一价
            best_bid_qty = Decimal(str(nine_bids[0][1]))    # 买一量
            
            # 计算中间价格
            mid_price = (best_ask_price + best_bid_price) / 2
            
            # 计算价差
            spread = best_ask_price - best_bid_price
            spread_pct = spread / mid_price * 100
            
            self.logger.debug(f"📊 盘口分析: 买一{best_bid_price}({best_bid_qty}) | 卖一{best_ask_price}({best_ask_qty}) | 价差{spread_pct:.2f}%")
            
            return {
                "best_ask_price": best_ask_price,
                "best_ask_qty": best_ask_qty,
                "best_bid_price": best_bid_price,
                "best_bid_qty": best_bid_qty,
                "mid_price": mid_price,
                "spread": spread,
                "spread_pct": spread_pct
            }
        except (ValueError, IndexError, TypeError) as e:
            self.logger.error(f"❌ 订单簿数据解析失败: {e}")
            return {}
    
    def _create_smart_orders(self, target_price: Decimal, order_book_info: Dict) -> List[Dict]:
        """
        基于目标价格和盘口数据创建智能订单
        
        Args:
            target_price: Raydium源价格计算的目标价格
            order_book_info: 订单簿分析结果
            
        Returns:
            List[Dict]: 智能订单列表
        """
        orders = []
        
        # 1. 获取交易对余额
        balances = self.get_trading_pair_balances()
        if not balances:
            self.logger.warning("⚠️ 无法获取余额信息，使用标准下单逻辑")
            return self._check_balance_and_create_orders(target_price)
        
        # 2. 解析交易对
        base_asset, quote_asset = self.target_symbol.split('/')
        base_balance = balances.get(base_asset, {})
        quote_balance = balances.get(quote_asset, {})
        
        base_available = Decimal(str(base_balance.get('available', 0)))
        quote_available = Decimal(str(quote_balance.get('available', 0)))
        
        self.logger.info(f"💳 余额: {base_asset}={base_available:.0f}, {quote_asset}={quote_available:.0f}")
        
        # 3. 如果没有订单簿信息，使用标准逻辑
        if not order_book_info:
            self.logger.info("📋 无订单簿数据，使用标准下单逻辑")
            return self._check_balance_and_create_orders(target_price)
        
        # 4. 基于盘口数据智能定价和定量
        mid_price = order_book_info.get("mid_price")
        best_ask_price = order_book_info.get("best_ask_price")
        best_bid_price = order_book_info.get("best_bid_price")
        best_ask_qty = order_book_info.get("best_ask_qty", Decimal("0"))
        best_bid_qty = order_book_info.get("best_bid_qty", Decimal("0"))
        
        if not mid_price:
            self.logger.warning("⚠️ 无法获取盘口中间价，使用标准逻辑")
            return self._check_balance_and_create_orders(target_price)
        
        self.logger.info(f"🎯 目标价格: ${target_price:.8f} | 盘口中间价: ${mid_price:.8f}")
        
        # 5. 创建买单和卖单
        buy_order = self._create_buy_order(target_price, mid_price, best_bid_price, 
                                         best_ask_qty, quote_available, quote_asset)
        if buy_order:
            orders.append(buy_order)
            
        sell_order = self._create_sell_order(target_price, mid_price, best_ask_price,
                                           best_bid_qty, base_available, base_asset)
        if sell_order:
            orders.append(sell_order)
        
        # 6. 总结
        if orders:
            buy_count = len([o for o in orders if o.get("orderDirection") == 1])
            sell_count = len(orders) - buy_count
            self.logger.info(f"🧠 智能订单生成: {buy_count}买单 + {sell_count}卖单")
        else:
            self.logger.warning("❌ 智能分析后无合适订单，余额或盘口条件不足")
        
        return orders
    
    def _create_buy_order(self, target_price: Decimal, mid_price: Decimal, 
                         best_bid_price: Decimal, best_ask_qty: Decimal,
                         quote_available: Decimal, quote_asset: str) -> Optional[Dict]:
        """
        创建智能买单
        
        Args:
            target_price: 目标价格
            mid_price: 盘口中间价
            best_bid_price: 最佳买价
            best_ask_qty: 最佳卖单数量
            quote_available: 可用quote资产余额
            quote_asset: quote资产名称
            
        Returns:
            Dict: 买单对象，如果不满足条件则返回None
        """
        safe_quote_balance = quote_available * self.SAFE_BALANCE_RATIO
        if safe_quote_balance < self.MIN_ORDER_AMOUNT:
            self.logger.warning(f"❌ {quote_asset}余额不足，跳过买单")
            return None
        
        # 智能买单定价
        if target_price > mid_price:
            # 目标价格高于市场价，需要推高价格
            buy_price = target_price * self.BUY_PRICE_ADJUSTMENT
        else:
            # 目标价格低于市场价，买单价格应低于当前买一价，避免过度推高
            buy_price = min(best_bid_price * self.BID_PRICE_STEP, 
                           target_price * self.SELL_PRICE_ADJUSTMENT)
        
        # 智能买单数量
        desired_buy_amount = min(self.sync_amount, safe_quote_balance)
        buy_qty = desired_buy_amount / buy_price
        
        # 盘口冲击保护
        if best_ask_qty > 0 and buy_qty > best_ask_qty * self.MARKET_IMPACT_RATIO:
            buy_qty = best_ask_qty * self.MARKET_IMPACT_RATIO
            self.logger.info(f"🔄 调整买单数量避免冲击盘口: {buy_qty:.2f}")
        
        # 最小金额检查
        if buy_qty * buy_price < self.MIN_ORDER_AMOUNT:
            self.logger.warning(f"❌ 买单金额太小，跳过")
            return None
        
        buy_order = self.create_trade_bean(
            trading_pair=self.target_symbol,
            order_direction=1,
            order_quantity=self._format_quantity(buy_qty, self.qty_precision),
            order_price=self._format_price(buy_price, self.price_precision)
        )
        
        self.logger.info(f"✅ 智能买单: ${buy_qty * buy_price:.2f} @ ${buy_price:.8f}")
        return buy_order
    
    def _create_sell_order(self, target_price: Decimal, mid_price: Decimal,
                          best_ask_price: Decimal, best_bid_qty: Decimal, 
                          base_available: Decimal, base_asset: str) -> Optional[Dict]:
        """
        创建智能卖单
        
        Args:
            target_price: 目标价格
            mid_price: 盘口中间价  
            best_ask_price: 最佳卖价
            best_bid_qty: 最佳买单数量
            base_available: 可用base资产余额
            base_asset: base资产名称
            
        Returns:
            Dict: 卖单对象，如果不满足条件则返回None
        """
        safe_base_balance = base_available * self.SAFE_BALANCE_RATIO
        if safe_base_balance < self.MIN_TOKEN_QUANTITY:
            self.logger.warning(f"❌ {base_asset}余额不足，跳过卖单")
            return None
        
        # 智能卖单定价
        if target_price < mid_price:
            # 目标价格低于市场价，需要压低价格
            sell_price = target_price * self.SELL_PRICE_ADJUSTMENT
        else:
            # 目标价格高于市场价，卖单价格应高于当前卖一价，避免过度压低
            sell_price = max(best_ask_price * self.ASK_PRICE_STEP, 
                            target_price * self.BUY_PRICE_ADJUSTMENT)
        
        # 智能卖单数量
        desired_sell_amount = self.sync_amount
        sell_qty = desired_sell_amount / sell_price
        max_sell_qty = min(safe_base_balance, sell_qty)
        
        # 盘口冲击保护
        if best_bid_qty > 0 and max_sell_qty > best_bid_qty * self.MARKET_IMPACT_RATIO:
            max_sell_qty = best_bid_qty * self.MARKET_IMPACT_RATIO
            self.logger.info(f"🔄 调整卖单数量避免冲击盘口: {max_sell_qty:.2f}")
        
        # 最小数量检查
        if max_sell_qty < self.MIN_TOKEN_QUANTITY:
            self.logger.warning(f"❌ 卖单数量太小，跳过")
            return None
        
        sell_order = self.create_trade_bean(
            trading_pair=self.target_symbol,
            order_direction=2,
            order_quantity=self._format_quantity(max_sell_qty, self.qty_precision),
            order_price=self._format_price(sell_price, self.price_precision)
        )
        
        self.logger.info(f"✅ 智能卖单: {max_sell_qty:.0f} @ ${sell_price:.8f}")
        return sell_order
    
    def get_actions(self, current_active_orders: List[Dict], 
                   nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict]:
        """获取策略动作"""
        actions = []
        
        try:
            # 1. 智能撤单逻辑（不撤销其他策略的订单）
            # 注意：current_active_orders 可能包含其他策略的订单，我们需要谨慎处理
            cancel_order_ids = []
            need_new_orders = False  # 是否需要下新单
            
            # 获取源价格用于价格比较
            source_price = self._get_source_price()
            if source_price:
                target_price = self._calculate_target_price(source_price)
                
                # 检查现有订单是否需要调整
                price_reasonable_orders = 0  # 价格合理的订单数量
                
                for order in current_active_orders:
                    if order.get("orderId"):
                        order_price = order.get("price", Decimal("0"))
                        if order_price > 0:
                            # 计算价格偏离程度
                            price_diff_pct = abs(order_price - target_price) / target_price
                            # 如果价格偏离超过阈值，才考虑撤销（避免误撤其他策略订单）
                            if price_diff_pct > self.PRICE_DEVIATION_THRESHOLD:
                                cancel_order_ids.append(order.get("orderId"))
                            else:
                                price_reasonable_orders += 1
                                
                if cancel_order_ids:
                    actions.append({
                        "action_type": "cancel",
                        "reason": f"价格偏离>{self.PRICE_DEVIATION_THRESHOLD*100:.0f}%，目标价格${target_price:.8f}",
                        "order_ids": cancel_order_ids
                    })
                    self.logger.info(f"🚫 智能撤单: {len(cancel_order_ids)}/{len(current_active_orders)} 个订单 (价格偏离过大)")
                    need_new_orders = True  # 撤单后需要补充新订单
                else:
                    self.logger.info(f"✅ 跳过撤单: 现有 {len(current_active_orders)} 个订单价格合理")
                    # 如果没有合理价格的订单，或者订单数量不足，才需要下新单
                    if price_reasonable_orders == 0:
                        need_new_orders = True
                        self.logger.info("📝 需要下新单: 没有价格合理的订单")
                    else:
                        self.logger.info(f"🎯 策略稳定: {price_reasonable_orders} 个订单价格合理，跳过下单")
            else:
                self.logger.warning("⚠️ 无法获取源价格，跳过智能撤单逻辑")
                need_new_orders = True  # 无法获取价格时，尝试下单
            
            # 2. 创建新订单（仅在需要时）
            if source_price and need_new_orders:
                # 分析订单簿数据
                order_book_info = self._analyze_order_book(nine_cex_order_book_data)
                
                # 基于余额和盘口数据创建智能同步订单
                orders = self._create_smart_orders(target_price, order_book_info)
                if orders:
                    actions.append({
                        "action_type": "place", 
                        "reason": f"智能同步价格至${target_price:.8f}",
                        "orders": orders
                    })
            elif not need_new_orders:
                self.logger.info("💤 策略休眠: 现有订单充足且价格合理")
            else:
                self.logger.warning("⚠️ 无法获取源价格，跳过本轮下单")
            
        except Exception as e:
            self.logger.error(f"❌ 价格同步策略执行异常: {e}")
            import traceback
            self.logger.debug(f"详细错误: {traceback.format_exc()}")
        
        return actions