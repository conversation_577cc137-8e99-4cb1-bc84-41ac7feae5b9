"""
流动性提供策略 (LiquidityProviderStrategy)
功能: 监控新交易对并提供流动性
"""

import time
import json
import os
import random
from decimal import Decimal, ROUND_DOWN, ROUND_UP
from typing import List, Dict, Any, Optional, Set
from .base_strategy import BaseStrategy
import logging

class LiquidityProviderStrategy(BaseStrategy):
    """
    流动性提供策略：
    1. 监控新外盘交易对上线
    2. 在新交易对上提供流动性
    3. 逐步出货15%的币种，保持盈利
    """
    
    def __init__(self, config: dict, nine_client, logger: Optional[logging.Logger] = None):
        super().__init__(config, nine_client, logger)
        self.strategy_name = "LiquidityProviderStrategy"
        
        # 从配置中获取参数
        self.api_key = self._get_config_value("LP_NINE_API_KEY")
        self.api_secret = self._get_config_value("LP_NINE_API_SECRET")
        
        # 资金配置
        self.initial_usdt = Decimal(str(self._get_config_value("LP_INITIAL_USDT", "3000")))
        self.coin_percentage_to_sell = Decimal(str(self._get_config_value("LP_COIN_SELL_PERCENTAGE", "0.15")))  # 15%
        
        # 深度优化配置 (多层挂单)
        self.depth_levels = int(self._get_config_value("LP_DEPTH_LEVELS", "5"))  # 默认5层
        self.spread_base = Decimal(str(self._get_config_value("LP_SPREAD_BASE", "0.002")))  # 基础价差0.2%
        self.spread_increment = Decimal(str(self._get_config_value("LP_SPREAD_INCREMENT", "0.001")))  # 递增0.1%
        self.spread_randomness = Decimal(str(self._get_config_value("LP_SPREAD_RANDOMNESS", "0.0003")))  # 随机性±0.03%
        
        # 订单大小配置
        self.order_size_percentage = Decimal(str(self._get_config_value("LP_ORDER_SIZE_PERCENTAGE", "0.1")))  # 每层10%资金
        self.order_size_mode = self._get_config_value("LP_ORDER_SIZE_MODE", "reverse")  # 默认使用大单托底模式
        self.order_size_factor = Decimal(str(self._get_config_value("LP_ORDER_SIZE_FACTOR", "0.8")))  # 变化系数
        self.min_order_amount = Decimal(str(self._get_config_value("LP_MIN_ORDER_AMOUNT", "10")))  # 最小订单金额(USDT)
        
        # 出货配置
        self.sell_frequency_seconds = int(self._get_config_value("LP_SELL_FREQUENCY", "300"))  # 5分钟出货一次
        self.sell_amount_percentage = Decimal(str(self._get_config_value("LP_SELL_AMOUNT_PERCENTAGE", "0.01")))  # 每次出货1%
        
        # 价格精度
        self.price_precision = int(self._get_config_value("LP_PRICE_PRECISION", "2"))
        self.qty_precision = int(self._get_config_value("LP_QTY_PRECISION", "5"))
        
        # 随机化优化配置 (拟人化行为) - 移除monitor_interval，统一使用TradingBotService控制
        self.time_randomness_factor = Decimal(str(self._get_config_value("LP_TIME_RANDOMNESS_FACTOR", "0.3")))  # ±30%
        self.quantity_randomness_factor = Decimal(str(self._get_config_value("LP_QUANTITY_RANDOMNESS_FACTOR", "0.05")))  # ±5%
        self.partial_cancel_probability = Decimal(str(self._get_config_value("LP_PARTIAL_CANCEL_PROBABILITY", "0.1")))  # 10%
        self.reorder_delay_min = int(self._get_config_value("LP_REORDER_DELAY_MIN", "5"))  # 最小5秒
        self.reorder_delay_max = int(self._get_config_value("LP_REORDER_DELAY_MAX", "30"))  # 最大30秒
        self.enable_behavior_randomization = self._get_config_value("LP_ENABLE_BEHAVIOR_RANDOMIZATION", "true").lower() == "true"
        self.order_lifetime_min = int(self._get_config_value("LP_ORDER_LIFETIME_MIN", "300"))  # 最小5分钟
        self.order_lifetime_max = int(self._get_config_value("LP_ORDER_LIFETIME_MAX", "1800"))  # 最大30分钟
        
        # 持久化存储配置
        self.data_dir = "data"
        self.known_pairs_file = os.path.join(self.data_dir, "known_trading_pairs.json")
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 运行时状态
        self.known_trading_pairs: Set[str] = self._load_known_trading_pairs()  # 从文件加载已知交易对
        self.active_pairs: Dict[str, Dict] = {}  # 活跃的交易对配置
        self.last_sell_time: Dict[str, float] = {}  # 上次出货时间
        self.total_coin_sold: Dict[str, Decimal] = {}  # 已出货数量
        self.total_profit: Decimal = Decimal("0")  # 总利润
        
        # 随机化状态跟踪
        self.order_creation_times: Dict[str, float] = {}  # 订单创建时间 (order_id -> timestamp)
        self.pending_reorders: Dict[str, float] = {}  # 待重新挂单 (pair_symbol -> execution_time)
        self.last_partial_cancel_time: Dict[str, float] = {}  # 上次部分撤单时间
        self.randomized_intervals: Dict[str, int] = {}  # 随机化后的时间间隔
        
        # 日志设置
        self.logger.info(f"流动性提供策略初始化")
        self.logger.info(f"初始资金: {self.initial_usdt} USDT")
        self.logger.info(f"目标出货比例: {self.coin_percentage_to_sell * 100}%")
        self.logger.info(f"价差设置: {self.spread_base * 100}%")
        self.logger.info(f"📊 深度配置: {self.depth_levels}层挂单, 价差递增{self.spread_increment * 100}%, 随机性±{self.spread_randomness * 100}%")
        self.logger.info(f"💰 订单配置: 基础{self.order_size_percentage * 100}%资金, 变化系数{self.order_size_factor}")
        self.logger.info(f"🎯 订单模式: {'大单托底' if self.order_size_mode == 'reverse' else '传统递减'} (远价订单{'更大' if self.order_size_mode == 'reverse' else '更小'})")
        self.logger.info(f"⏰ 监控间隔: {self.sell_frequency_seconds}秒")
        
        # 随机化功能状态
        if self.enable_behavior_randomization:
            self.logger.info(f"🎲 随机化优化: 启用")
            self.logger.info(f"   时间随机性: ±{self.time_randomness_factor * 100}%")
            self.logger.info(f"   数量随机性: ±{self.quantity_randomness_factor * 100}%")
            self.logger.info(f"   部分撤单概率: {self.partial_cancel_probability * 100}%")
            self.logger.info(f"   重挂延迟: {self.reorder_delay_min}-{self.reorder_delay_max}秒")
            self.logger.info(f"   订单生命周期: {self.order_lifetime_min}-{self.order_lifetime_max}秒")
        else:
            self.logger.info(f"🎲 随机化优化: 禁用")

    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict]:
        """
        获取策略动作：
        1. 监控新交易对
        2. 提供流动性
        3. 定时出货
        4. 随机化行为优化
        """
        actions = []
        
        try:
            # 0. 处理随机化行为 (如果启用)
            if self.enable_behavior_randomization:
                randomization_actions = self._handle_randomization_behaviors(current_active_orders)
                actions.extend(randomization_actions)
            
            # 1. 监控新交易对
            new_pairs = self._monitor_new_trading_pairs()
            if new_pairs:
                self.logger.info(f"🆕 发现新交易对: {new_pairs}")
                for pair in new_pairs:
                    self._initialize_pair_config(pair)
            
            # 2. 为活跃交易对提供流动性
            liquidity_actions = self._provide_liquidity(current_active_orders, nine_cex_order_book_data)
            actions.extend(liquidity_actions)
            
            # 3. 定时出货 (带随机化)
            sell_actions = self._check_and_sell_randomized()
            actions.extend(sell_actions)
            
        except Exception as e:
            self.logger.error(f"策略执行异常: {e}")
        
        return actions

    def _monitor_new_trading_pairs(self) -> List[str]:
        """监控新的交易对上线"""
        try:
            # 这里需要Nine Client实例，但策略层面不直接访问
            # 实际实现时，可以通过额外的方法注入Nine Client
            # 或者在TradingBotService中实现监控逻辑
            # 暂时返回空列表，具体实现在TradingBotService中处理
            return []
        except Exception as e:
            self.logger.error(f"监控新交易对失败: {e}")
            return []

    def _initialize_pair_config(self, trading_pair: str):
        """初始化新交易对的配置"""
        self.active_pairs[trading_pair] = {
            'symbol': trading_pair,
            'created_at': time.time(),
            'initial_coin_balance': Decimal("0"),  # 需要从API获取
            'target_sell_amount': Decimal("0"),    # = initial_coin_balance * coin_percentage_to_sell
            'sold_amount': Decimal("0"),
            'is_active': True
        }
        self.last_sell_time[trading_pair] = time.time()
        self.total_coin_sold[trading_pair] = Decimal("0")
        
        self.logger.info(f"✅ 初始化交易对配置: {trading_pair}")

    def _provide_liquidity(self, current_active_orders: List[Dict], order_book_data: Optional[Dict]) -> List[Dict]:
        """为活跃交易对提供流动性"""
        actions = []
        
        if not self.active_pairs:
            return actions
        
        # 为每个活跃交易对提供多层流动性
        for pair_symbol, pair_config in self.active_pairs.items():
            if not pair_config.get('is_active', False):
                continue
            
            # 检查当前是否有活跃订单
            pair_orders = [order for order in current_active_orders 
                          if self._is_order_for_pair(order, pair_symbol)]
            
            # 多层挂单策略：期望有 depth_levels * 2 个订单（买卖各depth_levels层）
            expected_orders = self.depth_levels * 2
            
            # 如果订单数量不足期望的80%，则重新生成流动性
            if len(pair_orders) < expected_orders * 0.8:
                self.logger.info(f"📊 {pair_symbol} 订单不足: {len(pair_orders)}/{expected_orders}, 重新生成流动性")
                new_orders = self._generate_liquidity_orders(pair_symbol, order_book_data)
                if new_orders:
                    actions.append({
                        "action_type": "place",
                        "orders": new_orders,
                        "reason": f"为 {pair_symbol} 提供 {self.depth_levels} 层流动性"
                    })
            else:
                self.logger.debug(f"📈 {pair_symbol} 流动性充足: {len(pair_orders)} 个活跃订单")
        
        return actions

    def _is_order_for_pair(self, order: Dict, pair_symbol: str) -> bool:
        """检查订单是否属于指定交易对"""
        # 这里需要根据实际的订单结构来判断
        # 简化实现，实际需要根据订单的交易对字段判断
        return True  # 暂时返回True，实际实现需要完善

    def _generate_liquidity_orders(self, pair_symbol: str, order_book_data: Optional[Dict]) -> List[Dict]:
        """生成多层流动性订单"""
        orders = []
        
        try:
            # 如果没有订单簿数据，使用默认价格策略
            if not order_book_data or 'bids' not in order_book_data or 'asks' not in order_book_data:
                self.logger.warning(f"缺少 {pair_symbol} 订单簿数据，跳过流动性提供")
                return orders
            
            bids = order_book_data.get('bids', [])
            asks = order_book_data.get('asks', [])
            
            if not bids or not asks:
                self.logger.warning(f"{pair_symbol} 订单簿为空，跳过流动性提供")
                return orders
            
            # 获取最佳买卖价
            best_bid = Decimal(str(bids[0][0]))
            best_ask = Decimal(str(asks[0][0]))
            mid_price = (best_bid + best_ask) / 2
            
            self.logger.info(f"💧 为 {pair_symbol} 生成 {self.depth_levels} 层流动性订单")
            self.logger.info(f"   中间价: {mid_price}, 最佳买价: {best_bid}, 最佳卖价: {best_ask}")
            
            # 生成多层订单
            for level in range(1, self.depth_levels + 1):
                level_orders = self._generate_level_orders(pair_symbol, mid_price, level)
                orders.extend(level_orders)
            
            self.logger.info(f"✅ 成功生成 {len(orders)} 个流动性订单 ({self.depth_levels * 2} 层)")
            
        except Exception as e:
            self.logger.error(f"生成 {pair_symbol} 流动性订单失败: {e}")
        
        return orders

    def _generate_level_orders(self, pair_symbol: str, mid_price: Decimal, level: int) -> List[Dict]:
        """生成指定层级的买卖订单"""
        orders = []
        
        try:
            # 计算该层的价差
            level_spread = self._calculate_level_spread(level)
            
            # 计算买卖价格
            buy_price = mid_price * (1 - level_spread)
            sell_price = mid_price * (1 + level_spread)
            
            # 格式化价格
            buy_price_formatted = self._format_price(buy_price)
            sell_price_formatted = self._format_price(sell_price)
            
            # 分别计算买单和卖单的订单大小
            buy_order_size = self._calculate_buy_order_size(level)
            sell_order_size = self._calculate_sell_order_size(level)
            
            # 检查订单大小是否满足最小要求
            if buy_order_size < self.min_order_amount or sell_order_size < self.min_order_amount:
                self.logger.debug(f"第{level}层订单金额过小（买单:{buy_order_size}, 卖单:{sell_order_size} < {self.min_order_amount} USDT），跳过")
                return orders
            
            # 计算数量
            buy_quantity = buy_order_size / buy_price
            sell_quantity = sell_order_size / sell_price
            
            # 应用随机化
            if self.enable_behavior_randomization:
                buy_quantity = self._randomize_quantity(buy_quantity)
                sell_quantity = self._randomize_quantity(sell_quantity)
            
            buy_quantity_formatted = self._format_quantity(buy_quantity)
            sell_quantity_formatted = self._format_quantity(sell_quantity)
            
            # 检查数量是否有效
            if buy_quantity_formatted <= 0 or sell_quantity_formatted <= 0:
                self.logger.debug(f"第{level}层订单数量无效，跳过")
                return orders
            
            # 生成买单
            buy_order = self.create_trade_bean(
                trading_pair=pair_symbol,
                order_direction=1,  # 买入
                order_quantity=str(buy_quantity_formatted),
                order_price=str(buy_price_formatted),
                account_type=1,
                order_type=1  # 限价单
            )
            orders.append(buy_order)
            
            # 生成卖单
            sell_order = self.create_trade_bean(
                trading_pair=pair_symbol,
                order_direction=2,  # 卖出
                order_quantity=str(sell_quantity_formatted),
                order_price="0",    # 需要实际价格
                account_type=1,
                order_type=1  # 限价单
            )
            orders.append(sell_order)
            
            self.logger.info(f"  第{level}层: 买单 {buy_quantity_formatted} @ {buy_price_formatted} (金额:{buy_order_size:.2f}) | "
                           f"卖单 {sell_quantity_formatted} @ {sell_price_formatted} (金额:{sell_order_size:.2f}) | "
                           f"价差 {level_spread * 100:.3f}%")
            
        except Exception as e:
            self.logger.error(f"生成第{level}层订单失败: {e}")
        
        return orders

    def _calculate_level_spread(self, level: int) -> Decimal:
        """计算指定层级的价差（含随机性）"""
        # 基础价差 + (层级-1) * 递增幅度
        base_spread = self.spread_base + (level - 1) * self.spread_increment
        
        # 添加随机性：±spread_randomness
        if self.spread_randomness > 0:
            random_adjustment = Decimal(str(random.uniform(
                -float(self.spread_randomness), 
                float(self.spread_randomness)
            )))
            base_spread += random_adjustment
        
        # 确保价差不为负数
        return max(base_spread, Decimal("0.0001"))

    def _calculate_buy_order_size(self, level: int) -> Decimal:
        """
        计算买单的订单大小
        逻辑：价格越低的买单，数量应该越多（承接更多卖盘）
        """
        base_size = self.initial_usdt * self.order_size_percentage
        
        if self.order_size_mode == "reverse":
            # 大单托底模式：level越大（价格越低），订单金额越大
            # level 1: 最小金额, level 5: 最大金额
            # 使用(1/factor)^(level-1)，这样level越大，指数越大，金额越大
            level_size = base_size * ((1 / self.order_size_factor) ** (level - 1))
            self.logger.debug(f"🔄 买单大单托底: 第{level}层(价格越低金额越大) = {level_size:.2f} USDT")
        else:
            # 传统模式：level越小（价格越高），订单金额越大
            # level 1: 最大金额, level 5: 最小金额  
            level_size = base_size * (self.order_size_factor ** (level - 1))
            self.logger.debug(f"📉 买单传统模式: 第{level}层(价格越高金额越大) = {level_size:.2f} USDT")
        
        return level_size

    def _calculate_sell_order_size(self, level: int) -> Decimal:
        """
        计算卖单的订单大小  
        逻辑：价格越高的卖单，数量应该越多（出售更多代币）
        """
        base_size = self.initial_usdt * self.order_size_percentage
        
        if self.order_size_mode == "reverse":
            # 大单托底模式：level越大（价格越高），订单金额越大
            # level 1: 最小金额, level 5: 最大金额
            # 使用(1/factor)^(level-1)，这样level越大，指数越大，金额越大
            level_size = base_size * ((1 / self.order_size_factor) ** (level - 1))
            self.logger.debug(f"🔄 卖单大单托底: 第{level}层(价格越高金额越大) = {level_size:.2f} USDT")
        else:
            # 传统模式：level越小（价格越低），订单金额越大
            # level 1: 最大金额, level 5: 最小金额
            level_size = base_size * (self.order_size_factor ** (level - 1))
            self.logger.debug(f"📉 卖单传统模式: 第{level}层(价格越低金额越大) = {level_size:.2f} USDT")
        
        return level_size

    def _check_and_sell_randomized(self) -> List[Dict]:
        """带随机化的检查并执行定时出货"""
        actions = []
        current_time = time.time()
        
        for pair_symbol, pair_config in self.active_pairs.items():
            if not pair_config.get('is_active', False):
                continue
            
            last_sell = self.last_sell_time.get(pair_symbol, 0)
            
            # 使用随机化的出货间隔
            randomized_interval = self._randomize_time_interval(
                self.sell_frequency_seconds, 
                f"{pair_symbol}出货"
            )
            
            if current_time - last_sell >= randomized_interval:
                sell_action = self._generate_sell_order(pair_symbol, pair_config)
                if sell_action:
                    actions.append(sell_action)
                    self.last_sell_time[pair_symbol] = current_time
        
        return actions

    def _generate_sell_order(self, pair_symbol: str, pair_config: Dict) -> Optional[Dict]:
        """生成出货订单"""
        try:
            # 检查是否还需要出货
            target_sell = pair_config.get('target_sell_amount', Decimal("0"))
            already_sold = pair_config.get('sold_amount', Decimal("0"))
            
            if already_sold >= target_sell:
                self.logger.info(f"📊 {pair_symbol} 出货已完成: {already_sold}/{target_sell}")
                return None
            
            # 计算本次出货数量
            remaining_to_sell = target_sell - already_sold
            sell_amount = min(
                target_sell * self.sell_amount_percentage,  # 1%的目标出货量
                remaining_to_sell  # 剩余未出货量
            )
            
            if sell_amount <= 0:
                return None
            
            # 简化实现：使用市价卖出（实际应该获取当前市价）
            # 这里需要获取当前订单簿数据来确定合适的卖出价格
            
            sell_quantity_formatted = self._format_quantity(sell_amount)
            
            # 生成市价卖单（实际实现中应该使用略低于最佳买价的限价单）
            order = self.create_trade_bean(
                trading_pair=pair_symbol,
                order_direction=2,  # 卖出
                order_quantity=str(sell_quantity_formatted),
                order_price="0",    # 需要实际价格
                account_type=1,
                order_type=1  # 限价单
            )
            
            # 更新已出货数量
            pair_config['sold_amount'] = already_sold + sell_amount
            
            self.logger.info(f"🚀 计划出货: {pair_symbol} | 数量: {sell_quantity_formatted}")
            
            return {
                "action_type": "place",
                "orders": [order],
                "reason": f"定时出货 {pair_symbol}"
            }
            
        except Exception as e:
            self.logger.error(f"生成 {pair_symbol} 出货订单失败: {e}")
            return None

    def _format_price(self, price: Decimal) -> Decimal:
        """格式化价格到指定精度"""
        return price.quantize(Decimal('0.1') ** self.price_precision, rounding=ROUND_DOWN)

    def _format_quantity(self, quantity: Decimal) -> Decimal:
        """格式化数量到指定精度"""
        return quantity.quantize(Decimal('0.1') ** self.qty_precision, rounding=ROUND_DOWN)

    def _get_config_value(self, key: str, default_value: Optional[str] = None) -> str:
        """获取配置值"""
        value = self.config.get(key, default_value)
        if value is None:
            raise ValueError(f"配置项 {key} 不能为空")
        return str(value)

    def update_pair_balance(self, pair_symbol: str, coin_balance: Decimal):
        """更新交易对的币种余额（外部调用）"""
        if pair_symbol in self.active_pairs:
            self.active_pairs[pair_symbol]['initial_coin_balance'] = coin_balance
            self.active_pairs[pair_symbol]['target_sell_amount'] = coin_balance * self.coin_percentage_to_sell
            self.logger.info(f"📊 更新 {pair_symbol} 余额: {coin_balance}, 目标出货: {self.active_pairs[pair_symbol]['target_sell_amount']}")

    def get_status_report(self) -> Dict[str, Any]:
        """获取策略状态报告"""
        return {
            'strategy_name': self.strategy_name,
            'active_pairs': len(self.active_pairs),
            'total_profit': str(self.total_profit),
            'known_pairs_count': len(self.known_trading_pairs),
            'known_pairs': list(self.known_trading_pairs),
            'pairs_detail': {
                pair: {
                    'sold_amount': str(config.get('sold_amount', 0)),
                    'target_sell_amount': str(config.get('target_sell_amount', 0)),
                    'sell_progress': float(config.get('sold_amount', 0) / max(config.get('target_sell_amount', 1), 1) * 100)
                }
                for pair, config in self.active_pairs.items()
            }
        }

    def _load_known_trading_pairs(self) -> Set[str]:
        """从文件加载已知交易对列表"""
        try:
            if os.path.exists(self.known_pairs_file):
                with open(self.known_pairs_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    pairs = set(data.get('trading_pairs', []))
                    self.logger.info(f"📋 [流动性策略] 加载已知交易对: {len(pairs)} 个")
                    if pairs:
                        self.logger.info(f"已知交易对列表: {list(pairs)}")
                    return pairs
            else:
                self.logger.info(f"📋 [流动性策略] 未找到历史交易对文件，将创建新的基线")
                return set()
        except Exception as e:
            self.logger.error(f"❌ [流动性策略] 加载已知交易对失败: {e}")
            return set()

    def _save_known_trading_pairs(self):
        """保存已知交易对列表到文件"""
        try:
            data = {
                'trading_pairs': list(self.known_trading_pairs),
                'last_updated': time.time(),
                'last_updated_readable': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.known_pairs_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.debug(f"💾 [流动性策略] 保存已知交易对: {len(self.known_trading_pairs)} 个")
        except Exception as e:
            self.logger.error(f"❌ [流动性策略] 保存已知交易对失败: {e}")

    def initialize_baseline(self, force: bool = False) -> int:
        """
        初始化交易对基线。
        建议在首次使用策略时调用，将当前所有交易对设置为已知交易对。
        
        Args:
            force: 是否强制重新初始化，即使已有数据
            
        Returns:
            int: 初始化的交易对数量
        """
        if not force and self.known_trading_pairs:
            self.logger.warning(f"⚠️ [流动性策略] 已存在 {len(self.known_trading_pairs)} 个已知交易对，跳过基线初始化")
            self.logger.info(f"如需重新初始化，请设置 force=True")
            return len(self.known_trading_pairs)
        
        self.logger.info(f"🔧 [流动性策略] 开始初始化交易对基线...")
        
        # 这里需要通过TradingBotService来获取当前所有交易对
        # 暂时返回0，具体实现在TradingBotService中处理
        return 0 

    # === 随机化优化功能 ===
    
    def _handle_randomization_behaviors(self, current_active_orders: List[Dict]) -> List[Dict]:
        """处理各种随机化行为"""
        actions = []
        current_time = time.time()
        
        try:
            # 1. 检查并处理待重挂订单
            actions.extend(self._handle_pending_reorders(current_time))
            
            # 2. 随机部分撤单
            actions.extend(self._handle_random_partial_cancels(current_active_orders, current_time))
            
            # 3. 检查订单生命周期超时
            actions.extend(self._handle_order_lifetime_cancels(current_active_orders, current_time))
            
        except Exception as e:
            self.logger.error(f"随机化行为处理异常: {e}")
        
        return actions

    def _handle_pending_reorders(self, current_time: float) -> List[Dict]:
        """处理待重新挂单的订单"""
        actions = []
        
        # 检查是否有到期的重挂任务
        pairs_to_reorder = []
        for pair_symbol, execution_time in self.pending_reorders.items():
            if current_time >= execution_time:
                pairs_to_reorder.append(pair_symbol)
        
        # 执行重挂
        for pair_symbol in pairs_to_reorder:
            del self.pending_reorders[pair_symbol]
            self.logger.info(f"🔄 执行延迟重挂: {pair_symbol}")
            
            # 这里应该生成新的流动性订单
            # 为了简化，暂时标记为需要生成新订单
            actions.append({
                "action_type": "reorder",
                "pair_symbol": pair_symbol,
                "reason": f"随机延迟重挂 {pair_symbol}"
            })
        
        return actions

    def _handle_random_partial_cancels(self, current_active_orders: List[Dict], current_time: float) -> List[Dict]:
        """随机部分撤单处理"""
        actions = []
        
        if not current_active_orders:
            return actions
        
        # 为每个交易对单独处理
        for pair_symbol in self.active_pairs.keys():
            pair_orders = [order for order in current_active_orders 
                          if self._is_order_for_pair(order, pair_symbol)]
            
            if not pair_orders:
                continue
            
            # 检查是否该进行随机撤单
            last_cancel_time = self.last_partial_cancel_time.get(pair_symbol, 0)
            min_interval = 300  # 最少5分钟间隔
            
            if current_time - last_cancel_time < min_interval:
                continue
            
            # 按概率决定是否撤单
            if random.random() < float(self.partial_cancel_probability):
                # 随机选择20-50%的订单进行撤销
                cancel_ratio = random.uniform(0.2, 0.5)
                cancel_count = max(1, int(len(pair_orders) * cancel_ratio))
                
                # 随机选择订单
                orders_to_cancel = random.sample(pair_orders, cancel_count)
                
                order_ids = [order.get('orderId', 'unknown') for order in orders_to_cancel]
                
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids,
                    "reason": f"随机部分撤单 {pair_symbol}: {cancel_count}个订单"
                })
                
                # 记录撤单时间并安排重挂
                self.last_partial_cancel_time[pair_symbol] = current_time
                
                # 随机延迟后重挂
                delay = random.randint(self.reorder_delay_min, self.reorder_delay_max)
                self.pending_reorders[pair_symbol] = current_time + delay
                
                self.logger.info(f"🎲 随机撤单: {pair_symbol} | 撤销{cancel_count}个订单 | {delay}秒后重挂")
        
        return actions

    def _handle_order_lifetime_cancels(self, current_active_orders: List[Dict], current_time: float) -> List[Dict]:
        """处理订单生命周期超时撤单"""
        actions = []
        
        orders_to_cancel = []
        
        for order in current_active_orders:
            order_id = order.get('orderId', '')
            if not order_id:
                continue
            
            # 检查订单创建时间
            creation_time = self.order_creation_times.get(order_id)
            if not creation_time:
                # 如果没有记录，假设是刚创建的
                self.order_creation_times[order_id] = current_time
                continue
            
            # 计算订单年龄
            order_age = current_time - creation_time
            
            # 随机生成该订单的生命周期
            max_lifetime = random.randint(self.order_lifetime_min, self.order_lifetime_max)
            
            if order_age > max_lifetime:
                orders_to_cancel.append(order_id)
                # 清理记录
                del self.order_creation_times[order_id]
        
        if orders_to_cancel:
            actions.append({
                "action_type": "cancel",
                "order_ids": orders_to_cancel,
                "reason": f"订单生命周期超时: {len(orders_to_cancel)}个订单"
            })
            
            self.logger.info(f"⏰ 生命周期撤单: {len(orders_to_cancel)}个订单")
        
        return actions

    def _randomize_time_interval(self, base_interval: int, context: str = "") -> int:
        """对时间间隔添加随机性"""
        if not self.enable_behavior_randomization:
            return base_interval
        
        # 计算随机范围
        randomness = float(self.time_randomness_factor)
        min_interval = int(base_interval * (1 - randomness))
        max_interval = int(base_interval * (1 + randomness))
        
        randomized = random.randint(min_interval, max_interval)
        
        if context:
            self.logger.debug(f"🎲 随机化{context}时间间隔: {base_interval}s → {randomized}s")
        
        return randomized

    def _randomize_quantity(self, base_quantity: Decimal) -> Decimal:
        """对订单数量添加随机性"""
        if not self.enable_behavior_randomization:
            return base_quantity
        
        # 计算随机调整范围
        randomness = float(self.quantity_randomness_factor)
        adjustment = random.uniform(-randomness, randomness)
        
        # 应用随机调整
        randomized_quantity = base_quantity * (1 + Decimal(str(adjustment)))
        
        # 确保不为负数
        return max(randomized_quantity, base_quantity * Decimal("0.1"))

    def record_order_creation(self, order_id: str, creation_time: Optional[float] = None):
        """记录订单创建时间（供外部调用）"""
        if not order_id:
            return
        
        if creation_time is None:
            creation_time = time.time()
        
        self.order_creation_times[order_id] = creation_time
        self.logger.debug(f"📝 记录订单创建: {order_id}")

    def cleanup_order_records(self, order_ids: List[str]):
        """清理已完成/取消订单的记录（供外部调用）"""
        for order_id in order_ids:
            if order_id in self.order_creation_times:
                del self.order_creation_times[order_id]
                self.logger.debug(f"🗑️ 清理订单记录: {order_id}")

    def get_randomization_status(self) -> Dict[str, Any]:
        """获取随机化功能状态报告"""
        return {
            "enabled": self.enable_behavior_randomization,
            "pending_reorders": len(self.pending_reorders),
            "tracked_orders": len(self.order_creation_times),
            "last_partial_cancels": len(self.last_partial_cancel_time),
            "config": {
                "time_randomness": f"±{self.time_randomness_factor * 100}%",
                "quantity_randomness": f"±{self.quantity_randomness_factor * 100}%", 
                "cancel_probability": f"{self.partial_cancel_probability * 100}%",
                "reorder_delay": f"{self.reorder_delay_min}-{self.reorder_delay_max}s",
                "order_lifetime": f"{self.order_lifetime_min}-{self.order_lifetime_max}s"
            }
        } 