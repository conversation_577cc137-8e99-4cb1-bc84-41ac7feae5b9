"""
梯度流动性支撑策略

核心功能：
1. 在指定交易对周围提供分层流动性支撑
2. 支持梯度挂单（价格距离分层）
3. 随机订单数量（避免机械化）
4. 动态订单补充（成交后往后推移价格）
5. 专注流动性提供，不做投机交易
"""

import time
import random
import math
from typing import List, Dict, Any, Optional
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import logging

from .base_strategy import BaseStrategy


class OrderSide(Enum):
    """订单方向"""
    BUY = 1
    SELL = 2


@dataclass
class GradientLevel:
    """梯度等级配置"""
    level: int                    # 等级 (1=最近, 5=最远)
    price_offset_ratio: Decimal   # 价格偏移比例
    base_quantity_ratio: Decimal  # 基础数量比例
    max_orders: int              # 该等级最大订单数


@dataclass  
class OrderLevelInfo:
    """订单等级信息（用于跟踪订单属于哪个梯度等级）"""
    order_id: str
    side: OrderSide
    level: int
    target_price: Decimal
    create_time: float = 0
    base_price_when_created: Optional[Decimal] = None  # 创建时的基准价格




class GradientLiquidityStrategy(BaseStrategy):
    """梯度流动性支撑策略"""
    
    def __init__(self, config: Dict[str, Any], nine_client, logger: logging.Logger):
        super().__init__(config, nine_client, logger)
        
        # 基础配置
        self.trading_pair = self._get_config_value("GL_TRADING_PAIR", "TST/USDT", str)
        self.max_total_fund = Decimal(self._get_config_value("GL_MAX_TOTAL_FUND", "500", str))
        
        # 梯度配置
        self.gradient_levels = int(self._get_config_value("GL_GRADIENT_LEVELS", "3", str))
        self.base_spread_ratio = Decimal(self._get_config_value("GL_BASE_SPREAD_RATIO", "0.005", str))  # 0.5%
        self.spread_multiplier = Decimal(self._get_config_value("GL_SPREAD_MULTIPLIER", "1.5", str))   # 递增倍数
        
        # 订单数量配置
        self.base_order_value = Decimal(self._get_config_value("GL_BASE_ORDER_VALUE", "0.5", str))      # 基础订单金额
        self.max_orders_per_level = int(self._get_config_value("GL_MAX_ORDERS_PER_LEVEL", "3", str))   # 每层最大订单数
        self.quantity_randomness = Decimal(self._get_config_value("GL_QUANTITY_RANDOMNESS", "0.3", str))  # 数量随机性±30%
        
        # 价格推动配置
        self.price_push_enabled = self._get_config_value("GL_PRICE_PUSH_ENABLED", "true", str).lower() == "true"
        self.push_step_ratio = Decimal(self._get_config_value("GL_PUSH_STEP_RATIO", "0.002", str))     # 每次推动0.2%
        
        # 精度配置  
        self.price_precision = int(self._get_config_value("GL_PRICE_PRECISION", "8", str))
        self.qty_precision = int(self._get_config_value("GL_QTY_PRECISION", "2", str))
        
        # 高频活跃模式配置
        self.active_mode = self._get_config_value("GL_ACTIVE_MODE", "false", str).lower() == "true"
        self.refresh_per_cycle = int(self._get_config_value("GL_REFRESH_PER_CYCLE", "2", str))  # 每次刷新2个订单
        self.max_order_age = float(self._get_config_value("GL_MAX_ORDER_AGE", "30", str))      # 订单最大存活30秒
        self.price_jitter = Decimal(self._get_config_value("GL_PRICE_JITTER", "0.001", str))   # 价格抖动±0.1%
        self.max_active_orders = int(self._get_config_value("GL_MAX_ACTIVE_ORDERS", "20", str)) # 活跃模式最大订单数
        self.wave_frequency = float(self._get_config_value("GL_WAVE_FREQUENCY", "0.1", str))   # 价格波动频率
        
        # API凭证
        self.api_key = self._get_config_value("NINE_API_KEY")
        self.api_secret = self._get_config_value("NINE_API_SECRET")
        
        # 状态管理
        self.first_run = True
        self.order_level_info: Dict[str, OrderLevelInfo] = {}  # 订单等级信息跟踪
        self.last_price: Optional[Decimal] = None
        self.target_center_price: Optional[Decimal] = None  # 目标中心价格
        self.last_base_price: Optional[Decimal] = None  # 上次生成订单时的基准价格
        
        # 活跃模式状态管理
        self.wave_start_time = time.time()  # 波动起始时间
        
        # API频率保护
        self.last_api_call_time = 0.0
        self.api_call_count_per_minute = 0
        self.minute_start_time = time.time()
        self.max_api_calls_per_minute = 30  # 每分钟最多30次API调用（安全限制）
        
        # 初始化梯度等级配置
        self._init_gradient_levels()
        
        # 配置框架订单簿获取参数 (让框架自动获取订单簿数据)
        self.fetch_nine_book_symbol = self.trading_pair
        self.fetch_nine_book_precision = "0.00000001"  # 使用最高精度
        self.fetch_nine_book_depth = 10  # 获取前10档深度
        
        self.logger.info(f"✅ GradientLiquidityStrategy初始化完成")
        self.logger.info(f"📈 交易对: {self.trading_pair}")
        self.logger.info(f"💰 最大资金: {self.max_total_fund} USDT")
        if self.active_mode:
            self.logger.info(f"🚀 高频活跃模式: 开启 | 最大{self.max_active_orders}单 | 每次刷新{self.refresh_per_cycle}单 | 订单寿命{self.max_order_age}秒")
        else:
            self.logger.info(f"📊 梯度等级: {self.gradient_levels}, 每层最多: {self.max_orders_per_level} 个订单")
    
    def _init_gradient_levels(self):
        """初始化梯度等级配置"""
        self.gradient_config = []
        
        for level in range(1, self.gradient_levels + 1):
            # 价格偏移：递增扩大
            price_offset = self.base_spread_ratio * (self.spread_multiplier ** (level - 1))
            
            # 订单数量权重：远离价格的等级订单更大
            quantity_ratio = Decimal("0.3") + (Decimal("0.2") * level)  # 0.3, 0.5, 0.7...
            
            gradient_level = GradientLevel(
                level=level,
                price_offset_ratio=price_offset,
                base_quantity_ratio=quantity_ratio, 
                max_orders=self.max_orders_per_level
            )
            
            self.gradient_config.append(gradient_level)
            
            self.logger.info(f"📊 等级{level}: 价格偏移{price_offset:.3%}, 数量权重{quantity_ratio:.1f}, 最多{self.max_orders_per_level}单")
    
    def _check_api_rate_limit(self) -> bool:
        """检查API频率限制"""
        current_time = time.time()
        
        # 重置每分钟计数器
        if current_time - self.minute_start_time >= 60:
            self.api_call_count_per_minute = 0
            self.minute_start_time = current_time
        
        # 检查是否超过频率限制
        if self.api_call_count_per_minute >= self.max_api_calls_per_minute:
            self.logger.warning(f"⚠️ API频率限制: 已达到每分钟{self.max_api_calls_per_minute}次上限")
            return False
            
        # 检查最小间隔（防止突发调用）
        if current_time - self.last_api_call_time < 1.0:  # 最小1秒间隔
            self.logger.debug("⏱️ API调用间隔过短，跳过本次执行")
            return False
            
        return True
    
    def _record_api_call(self):
        """记录API调用"""
        self.last_api_call_time = time.time()
        self.api_call_count_per_minute += 1
    
    def get_actions(self,
                    current_active_orders: Optional[List[Dict]] = None,
                    nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """生成流动性支撑订单"""
        actions = []
        
        # 首次运行：清理现有挂单
        if self.first_run:
            try:
                if self.api_key and self.api_secret:
                    self.logger.info("🧹 清理现有挂单...")
                    result = self.nine_client.cancel_all_orders(self.api_key, self.api_secret)
                    if result and result.get('code') == 200:
                        self.logger.info("✅ 清理完成")
            except Exception as e:
                self.logger.error(f"❌ 清理失败: {e}")
            
            self.first_run = False
            return actions
        
        # 简化的保护机制：基于实际订单数检查
        total_current_orders = len(current_active_orders) if current_active_orders else 0
        self.logger.debug(f"📊 [策略检查] 当前实际订单数: {total_current_orders}")
        
        # 获取当前市场价格
        current_price = self._get_current_price(nine_cex_order_book_data)
        if not current_price:
            self.logger.warning("⚠️ 无法获取当前价格")
            return actions
        
        # 更新价格历史
        if self.last_price:
            price_change = (current_price - self.last_price) / self.last_price
            if abs(price_change) > Decimal("0.01"):  # 价格变化超过1%
                self.logger.info(f"📈 价格变化: {self.last_price} → {current_price} ({price_change:+.2%})")
        
        self.last_price = current_price
        
        # 根据模式选择不同的处理逻辑（统一：两种模式都使用等级跟踪）
        if self.active_mode:
            # 高频活跃梯度模式（保留等级信息）
            return self._handle_active_mode(current_active_orders, current_price)
        else:
            # 传统梯度模式（重置波动状态）
            self.wave_start_time = time.time()  # 重置波动起始时间
            
            # 传统梯度模式
            return self._handle_gradient_mode(current_active_orders, current_price)
        
    def _handle_gradient_mode(self, current_active_orders: Optional[List[Dict]], current_price: Decimal) -> List[Dict[str, Any]]:
        """处理传统梯度模式"""
        actions = []
        
        # 更新订单等级信息
        self._update_order_levels(current_active_orders, current_price)
        
        # 生成梯度订单
        order_actions = self._generate_gradient_orders(current_price)
        actions.extend(order_actions)
        
        # 检查是否需要价格推动
        if self.price_push_enabled:
            push_actions = self._check_price_push(current_price)
            actions.extend(push_actions)
        
        return actions
        
    def _handle_active_mode(self, current_active_orders: Optional[List[Dict]], current_price: Decimal) -> List[Dict[str, Any]]:
        """处理高频活跃梯度模式"""
        actions = []
        
        # API频率保护检查
        if not self._check_api_rate_limit():
            return actions
        
        # 更新订单等级信息（统一使用梯度跟踪）
        self._update_order_levels(current_active_orders, current_price)
        
        # 检查总订单数量
        total_orders = len(current_active_orders) if current_active_orders else 0
        self.logger.debug(f"📊 [活跃模式] 当前订单数: {total_orders}, 目标: {self.max_active_orders}")
        
        # 核心修复：只基于实际订单数判断，不依赖可能不准确的跟踪器
        if total_orders == 0:
            initial_orders = self._generate_active_orders(current_price, self.max_active_orders)
            if initial_orders:
                actions.append({
                    "action_type": "place",
                    "orders": initial_orders,
                    "reason": f"高频活跃模式初始化: {len(initial_orders)}个订单"
                })
                self.logger.info(f"🚀 活跃模式启动，生成 {len(initial_orders)} 个初始订单")
                self._record_api_call()  # 记录API调用
            return actions
        
        # 检查是否需要刷新订单（基于实际订单数据）
        orders_to_cancel = self._get_orders_to_refresh_from_actual_orders(current_active_orders)
        
        # 安全检查：确保要撤销的订单确实存在于当前活跃订单中
        if orders_to_cancel and current_active_orders:
            active_order_ids = {order.get('orderId') for order in current_active_orders if order.get('orderId')}
            valid_cancellations = [order_id for order_id in orders_to_cancel if order_id in active_order_ids]
            
            if len(valid_cancellations) != len(orders_to_cancel):
                invalid_count = len(orders_to_cancel) - len(valid_cancellations)
                self.logger.warning(f"⚠️ 跳过{invalid_count}个无效撤单请求（订单已不存在）")
                orders_to_cancel = valid_cancellations
        
        # 修复：确保订单数逐步增长到目标数量
        # 计算需要的总订单数：目标数量减去不会被取消的订单数
        remaining_orders = total_orders - len(orders_to_cancel)
        needed_orders = max(0, self.max_active_orders - remaining_orders)
        
        if orders_to_cancel:
            actions.append({
                "action_type": "cancel",
                "order_ids": orders_to_cancel,
                "reason": f"高频活跃模式订单刷新: {len(orders_to_cancel)}个订单"
            })
            
        # 生成足够的新订单以达到目标数量
        if needed_orders > 0:
            new_orders = self._generate_active_orders(current_price, needed_orders)
            if new_orders:
                actions.append({
                    "action_type": "place",
                    "orders": new_orders,
                    "reason": f"高频活跃模式: {len(new_orders)}个新订单"
                })
                final_total = remaining_orders + len(new_orders)
                self.logger.info(f"🔄 活跃模式: 取消{len(orders_to_cancel)}, 创建{len(new_orders)}, 预期总数{final_total}")
        else:
            self.logger.info(f"✅ 订单数量已达目标({total_orders}/{self.max_active_orders})，仅刷新订单")
        
        # 记录API调用（只有在实际产生actions时才记录）
        if actions:
            self._record_api_call()
            
        return actions
    
    def _get_current_price(self, order_book_data: Optional[Dict]) -> Optional[Decimal]:
        """获取当前市场价格（基于框架传递的完整订单簿数据）"""
        try:
            if not order_book_data:
                self.logger.debug("📊 未收到订单簿数据")
                return None
            
            self.logger.debug(f"📊 收到订单簿数据，顶层键: {list(order_book_data.keys())}")
            
            # 检查是否为完整的business_data格式（包含orderDepth和quote）
            if "orderDepth" in order_book_data:
                order_depth = order_book_data["orderDepth"]
                quote_info = order_book_data.get("quote", {})
                
                # 优先使用quote中的最新成交价
                if quote_info and isinstance(quote_info, dict):
                    latest_price_str = quote_info.get("n")  # n字段是最新成交价
                    if latest_price_str and latest_price_str != "0" and latest_price_str != "0.00000000":
                        price = Decimal(str(latest_price_str))
                        self.logger.info(f"✅ 从quote.n获取最新成交价: {price}")
                        return price
                    
                    # 备用：使用quote中的其他价格字段
                    for price_field in ['price', 'p', 'last_price']:
                        if quote_info.get(price_field):
                            price = Decimal(str(quote_info[price_field]))
                            self.logger.info(f"✅ 从quote.{price_field}获取价格: {price}")
                            return price
                
                # 使用orderDepth计算中间价
                if order_depth and isinstance(order_depth, dict):
                    asks = order_depth.get('asks', [])
                    bids = order_depth.get('bids', [])
                    
                    if asks and bids and len(asks) > 0 and len(bids) > 0:
                        best_ask = Decimal(str(asks[0][0]))
                        best_bid = Decimal(str(bids[0][0]))
                        mid_price = (best_ask + best_bid) / Decimal("2")
                        self.logger.info(f"✅ 从orderDepth中间价获取: {mid_price} (买1:{best_bid}, 卖1:{best_ask})")
                        return mid_price
                
                self.logger.info(f"📊 orderDepth结构: asks={len(asks) if asks else 0}, bids={len(bids) if bids else 0}")
                self.logger.info(f"📊 quote信息: {quote_info}")
            
            # 兼容旧格式：直接包含asks/bids的数据
            elif "asks" in order_book_data and "bids" in order_book_data:
                asks = order_book_data.get('asks', [])
                bids = order_book_data.get('bids', [])
                
                if asks and bids and len(asks) > 0 and len(bids) > 0:
                    best_ask = Decimal(str(asks[0][0]))
                    best_bid = Decimal(str(bids[0][0]))
                    mid_price = (best_ask + best_bid) / Decimal("2")
                    self.logger.info(f"✅ 从旧格式中间价获取: {mid_price} (买1:{best_bid}, 卖1:{best_ask})")
                    return mid_price
                
                self.logger.info(f"📊 旧格式结构: asks={len(asks)}, bids={len(bids)}")
            
            else:
                self.logger.warning(f"📊 未知的订单簿数据格式: {list(order_book_data.keys())}")
        
        except Exception as e:
            self.logger.warning(f"⚠️ 价格获取失败: {e}")
        
        self.logger.warning("⚠️ 无法从订单簿数据获取价格")
        return None
    
    def _update_order_levels(self, current_active_orders: Optional[List[Dict]], current_price: Decimal):
        """更新订单等级信息（优化版本：更精确的等级识别）"""
        # 简化调试日志
        self.logger.debug(f"🔍 收到 {len(current_active_orders) if current_active_orders else 0} 个活跃订单")
        
        if not current_active_orders:
            # 所有订单都成交了，清空等级信息
            if self.order_level_info:
                self.logger.info(f"✅ 所有订单已成交，清空 {len(self.order_level_info)} 个订单等级信息")
                self.order_level_info.clear()
            return
        
        # 更新订单等级信息：对于新订单，尝试识别其等级
        current_order_ids = {order.get('orderId') for order in current_active_orders if order.get('orderId')}
        
        # 清理已完成订单的等级信息
        completed_order_ids = []
        for order_id in list(self.order_level_info.keys()):
            if order_id not in current_order_ids:
                completed_order_ids.append(order_id)
                del self.order_level_info[order_id]
        
        # 为新订单识别等级
        self.logger.debug(f"开始识别等级，当前已识别 {len(self.order_level_info)} 个订单")
        identified_count = 0
        for order in current_active_orders:
            order_id = order.get('orderId')
            if order_id and order_id not in self.order_level_info:
                success = self._identify_order_level_improved(order, current_price)
                if success:
                    identified_count += 1
        
        if identified_count > 0:
            self.logger.info(f"✅ 新识别 {identified_count} 个订单，总计 {len(self.order_level_info)} 个订单")
        
        if completed_order_ids:
            self.logger.debug(f"清理 {len(completed_order_ids)} 个已成交订单的等级信息")
    
    def _identify_order_level_improved(self, order: Dict, current_base_price: Decimal) -> bool:
        """改进的订单等级识别算法"""
        try:
            order_id = order.get('orderId')
            order_price = order.get('price')
            order_direction = order.get('direction')
            
            self.logger.debug(f"尝试识别订单 {order_id}: 价格={order_price}, 方向={order_direction}")
            
            if not all([order_id, order_price, order_direction]):
                self.logger.debug(f"缺少必要参数，跳过识别")
                return False
            
            order_price = Decimal(str(order_price))
            is_buy = (order_direction == 1)
            
            # 使用当前价格作为基准进行等级识别
            base_price_for_identification = current_base_price
            
            # 计算价格偏离基准价格的比例
            if is_buy:
                # 买单：价格应该低于基准价格
                if order_price >= base_price_for_identification:
                    self.logger.debug(f"买单价格异常: {order_price} >= {base_price_for_identification}")
                    return False
                price_diff_ratio = (base_price_for_identification - order_price) / base_price_for_identification
            else:
                # 卖单：价格应该高于基准价格
                if order_price <= base_price_for_identification:
                    self.logger.debug(f"卖单价格异常: {order_price} <= {base_price_for_identification}")
                    return False
                price_diff_ratio = (order_price - base_price_for_identification) / base_price_for_identification
            
            # 找到最接近的梯度等级
            best_level = 1
            min_diff = float('inf')
            
            # 始终使用最接近的等级，不设容差限制
            for gradient_level in self.gradient_config:
                expected_ratio = gradient_level.price_offset_ratio  # 保持Decimal类型
                diff = abs(price_diff_ratio - expected_ratio)
                if diff < Decimal(str(min_diff)):
                    min_diff = float(diff)
                    best_level = gradient_level.level
            
            # 记录等级信息（包含创建时的基准价格）
            self.order_level_info[order_id] = OrderLevelInfo(
                order_id=order_id,
                side=OrderSide.BUY if is_buy else OrderSide.SELL,
                level=best_level,
                target_price=order_price,
                create_time=time.time(),
                base_price_when_created=base_price_for_identification
            )
            
            self.logger.debug(f"✅ 成功识别: {order_id} -> 等级{best_level} ({'买' if is_buy else '卖'}单), 偏差{price_diff_ratio:.3%}")
            return True
            
        except Exception as e:
            self.logger.debug(f"❌ 订单等级识别失败: {e}")
            return False
    
    def _generate_gradient_orders(self, current_price: Decimal) -> List[Dict[str, Any]]:
        """生成梯度订单"""
        actions = []
        
        # 设定目标中心价格（首次设定或价格推动时更新）
        if not self.target_center_price:
            self.target_center_price = current_price
        
        # 使用实时价格作为梯度订单的基准价格（防止价格偏移导致立即成交）
        base_price_for_orders = current_price
        self.last_base_price = base_price_for_orders
        
        # 统计各等级的现有订单数量（基于我们的等级信息跟踪）
        level_order_counts = {level: {"buy": 0, "sell": 0} for level in range(1, self.gradient_levels + 1)}
        
        for order_info in self.order_level_info.values():
            side_key = "buy" if order_info.side == OrderSide.BUY else "sell"
            if order_info.level in level_order_counts:
                level_order_counts[order_info.level][side_key] += 1
        
        # 为每个等级生成需要的订单
        new_orders = []
        
        # 统计信息（仅在有变化时显示）
        total_orders = sum(level_order_counts[level]["buy"] + level_order_counts[level]["sell"] for level in level_order_counts)
        if total_orders != len(self.order_level_info):
            self.logger.info(f"📊 订单统计不匹配: 计算{total_orders}个 vs 跟踪{len(self.order_level_info)}个")
        
        # 简化显示（仅在有订单时显示详情）
        if self.order_level_info and len(self.order_level_info) <= 20:  # 避免日志过多
            self.logger.debug(f"🔍 当前跟踪 {len(self.order_level_info)} 个订单")
        
        for gradient_level in self.gradient_config:
            level = gradient_level.level
            current_buy_count = level_order_counts[level]["buy"]
            current_sell_count = level_order_counts[level]["sell"]
            
            # 只在需要生成新订单时显示详情
            if current_buy_count < gradient_level.max_orders or current_sell_count < gradient_level.max_orders:
                self.logger.info(f"📊 等级{level}: 现有买单{current_buy_count}个, 卖单{current_sell_count}个, 需要各{gradient_level.max_orders}个")
            else:
                self.logger.debug(f"等级{level}: 买单{current_buy_count}个, 卖单{current_sell_count}个 (已满足)")
            
            # 计算该等级的目标价格（使用实时价格作为基准）
            buy_price = base_price_for_orders * (Decimal("1") - gradient_level.price_offset_ratio)
            sell_price = base_price_for_orders * (Decimal("1") + gradient_level.price_offset_ratio)
            
            # 计算订单数量
            base_quantity = self._calculate_order_quantity(gradient_level, buy_price)
            
            # 生成买单
            need_buy_orders = gradient_level.max_orders - current_buy_count
            for _ in range(need_buy_orders):
                if len(new_orders) * self.base_order_value >= self.max_total_fund:
                    break
                
                # 添加随机性
                quantity = self._add_quantity_randomness(base_quantity)
                price = self._add_price_randomness(buy_price)
                
                try:
                    # 获取Nine订单参数
                    account_type = self._get_config_value("NINE_ACCOUNT_TYPE", 1, int)
                    order_type = self._get_config_value("NINE_ORDER_TYPE_LIMIT", 1, int)
                    
                    trade_bean = self.create_trade_bean(
                        trading_pair=self.trading_pair,
                        order_direction=OrderSide.BUY.value,
                        order_quantity=self._format_quantity(quantity, self.qty_precision),
                        order_price=self._format_price(price, self.price_precision),
                        account_type=account_type,
                        order_type=order_type
                    )
                    new_orders.append({
                        "trade_bean": trade_bean,
                        "side": OrderSide.BUY,
                        "level": level,
                        "price": price,
                        "quantity": quantity
                    })
                except Exception as e:
                    self.logger.error(f"❌ 创建买单失败: {e}")
            
            # 生成卖单
            need_sell_orders = gradient_level.max_orders - current_sell_count
            for _ in range(need_sell_orders):
                if len(new_orders) * self.base_order_value >= self.max_total_fund:
                    break
                
                # 添加随机性
                quantity = self._add_quantity_randomness(base_quantity)
                price = self._add_price_randomness(sell_price)
                
                try:
                    # 获取Nine订单参数
                    account_type = self._get_config_value("NINE_ACCOUNT_TYPE", 1, int)
                    order_type = self._get_config_value("NINE_ORDER_TYPE_LIMIT", 1, int)
                    
                    trade_bean = self.create_trade_bean(
                        trading_pair=self.trading_pair,
                        order_direction=OrderSide.SELL.value,
                        order_quantity=self._format_quantity(quantity, self.qty_precision),
                        order_price=self._format_price(price, self.price_precision),
                        account_type=account_type,
                        order_type=order_type
                    )
                    new_orders.append({
                        "trade_bean": trade_bean,
                        "side": OrderSide.SELL,
                        "level": level,
                        "price": price,
                        "quantity": quantity
                    })
                except Exception as e:
                    self.logger.error(f"❌ 创建卖单失败: {e}")
        
        # 如果有新订单，创建下单动作
        if new_orders:
            trade_beans = [order["trade_bean"] for order in new_orders]
            
            action = {
                "action_type": "place",
                "reason": f"梯度流动性支撑: {len(new_orders)} 个订单",
                "orders": trade_beans
            }
            actions.append(action)
            
            self.logger.info(f"📤 生成梯度订单: {len(new_orders)} 个")
        
        return actions
    
    def _calculate_order_quantity(self, gradient_level: GradientLevel, price: Decimal) -> Decimal:
        """计算订单数量"""
        # 基于订单金额和价格计算数量
        order_value = self.base_order_value * gradient_level.base_quantity_ratio
        quantity = order_value / price
        
        return quantity
    
    def _add_quantity_randomness(self, base_quantity: Decimal) -> Decimal:
        """添加数量随机性"""
        randomness_factor = Decimal(str(random.uniform(
            1 - float(self.quantity_randomness),
            1 + float(self.quantity_randomness)
        )))
        
        return base_quantity * randomness_factor
    
    def _add_price_randomness(self, base_price: Decimal) -> Decimal:
        """添加价格随机性（小幅度）"""
        price_randomness = Decimal("0.001")  # 0.1%的价格随机性
        randomness_factor = Decimal(str(random.uniform(
            1 - float(price_randomness),
            1 + float(price_randomness)
        )))
        
        return base_price * randomness_factor
    
    def _check_price_push(self, current_price: Decimal) -> List[Dict[str, Any]]:
        """检查是否需要价格推动"""
        actions = []
        
        if not self.target_center_price:
            return actions
        
        # 检查是否所有订单都成交完了（表示需要推动价格）
        if not self.order_level_info:
            # 推动目标价格
            if current_price > self.target_center_price:
                # 价格上涨，推高目标价格
                new_center = self.target_center_price * (Decimal("1") + self.push_step_ratio)
                self.logger.info(f"📈 价格推动: 目标价格 {self.target_center_price} → {new_center} (+{self.push_step_ratio:.2%})")
            else:
                # 价格下跌，推低目标价格
                new_center = self.target_center_price * (Decimal("1") - self.push_step_ratio)
                self.logger.info(f"📉 价格推动: 目标价格 {self.target_center_price} → {new_center} (-{self.push_step_ratio:.2%})")
            
            self.target_center_price = new_center
        
        return actions
    
    # ===== 高频活跃模式相关方法 =====
    
    def _get_orders_to_refresh_from_actual_orders(self, current_active_orders: Optional[List[Dict]]) -> List[str]:
        """基于实际订单数据获取需要刷新的订单ID列表（优化：保持买卖单平衡）"""
        if not current_active_orders:
            return []
            
        orders_to_cancel = []
        
        # 统计各等级的买卖单分布
        level_orders = {level: {"buy": [], "sell": []} for level in range(1, self.gradient_levels + 1)}
        untracked_orders = []
        
        for order in current_active_orders:
            order_id = order.get('orderId')
            if not order_id:
                continue
                
            # 查找订单所属等级和买卖方向
            if order_id in self.order_level_info:
                order_info = self.order_level_info[order_id]
                level = order_info.level
                side = "buy" if order_info.side == OrderSide.BUY else "sell"
                
                if level in level_orders:
                    level_orders[level][side].append(order_id)
                else:
                    untracked_orders.append(order_id)
            else:
                untracked_orders.append(order_id)
        
        # 优先刷新买卖单不平衡的订单，保持买卖平衡
        refresh_count = min(self.refresh_per_cycle, len(current_active_orders))
        target_per_side = max(1, self.max_active_orders // (self.gradient_levels * 2))  # 每边每等级的目标数量
        
        # 选择刷新策略：
        # 1. 优先刷新买卖单严重不平衡的等级
        # 2. 然后随机刷新其他订单，但保持买卖单平衡
        
        # 步骤1：找出买卖单不平衡的等级
        for level in range(1, self.gradient_levels + 1):
            if len(orders_to_cancel) >= refresh_count:
                break
                
            buy_orders = level_orders[level]["buy"]
            sell_orders = level_orders[level]["sell"]
            
            # 检查买单是否过多
            if len(buy_orders) > len(sell_orders) + 1:  # 买单比卖单多2个以上
                excess_buy = len(buy_orders) - max(len(sell_orders), target_per_side)
                if excess_buy > 0 and buy_orders:
                    select_count = min(excess_buy, refresh_count - len(orders_to_cancel), len(buy_orders))
                    selected = random.sample(buy_orders, select_count)
                    orders_to_cancel.extend(selected)
                    self.logger.debug(f"等级{level}买单过多({len(buy_orders)}vs{len(sell_orders)})，选择刷新{len(selected)}个买单")
            
            # 检查卖单是否过多
            elif len(sell_orders) > len(buy_orders) + 1:  # 卖单比买单多2个以上
                excess_sell = len(sell_orders) - max(len(buy_orders), target_per_side)
                if excess_sell > 0 and sell_orders:
                    select_count = min(excess_sell, refresh_count - len(orders_to_cancel), len(sell_orders))
                    selected = random.sample(sell_orders, select_count)
                    orders_to_cancel.extend(selected)
                    self.logger.debug(f"等级{level}卖单过多({len(sell_orders)}vs{len(buy_orders)})，选择刷新{len(selected)}个卖单")
        
        # 步骤2：如果还需要更多订单，平衡地随机选择
        if len(orders_to_cancel) < refresh_count:
            remaining_orders = []
            
            # 平衡地收集剩余订单（避免单边倾斜）
            for level in range(1, self.gradient_levels + 1):
                buy_remaining = [oid for oid in level_orders[level]["buy"] if oid not in orders_to_cancel]
                sell_remaining = [oid for oid in level_orders[level]["sell"] if oid not in orders_to_cancel]
                
                # 交替添加买卖单，保持平衡
                max_len = max(len(buy_remaining), len(sell_remaining))
                for i in range(max_len):
                    if i < len(buy_remaining):
                        remaining_orders.append(buy_remaining[i])
                    if i < len(sell_remaining):
                        remaining_orders.append(sell_remaining[i])
            
            # 添加未跟踪的订单
            remaining_orders.extend([oid for oid in untracked_orders if oid not in orders_to_cancel])
            
            if remaining_orders:
                additional_count = refresh_count - len(orders_to_cancel)
                additional_orders = random.sample(remaining_orders, min(additional_count, len(remaining_orders)))
                orders_to_cancel.extend(additional_orders)
        
        if orders_to_cancel:
            # 统计刷新订单的买卖分布
            side_distribution = {"buy": 0, "sell": 0, "unknown": 0}
            level_distribution = {}
            
            for order_id in orders_to_cancel:
                if order_id in self.order_level_info:
                    order_info = self.order_level_info[order_id]
                    level = order_info.level
                    side = "buy" if order_info.side == OrderSide.BUY else "sell"
                    
                    level_distribution[level] = level_distribution.get(level, 0) + 1
                    side_distribution[side] += 1
                else:
                    side_distribution["unknown"] += 1
            
            self.logger.info(f"🔄 [活跃梯度模式] 选择刷新 {len(orders_to_cancel)} 个订单")
            self.logger.info(f"📊 买卖分布: 买{side_distribution['buy']}个, 卖{side_distribution['sell']}个, 未知{side_distribution['unknown']}个")
            self.logger.debug(f"📊 等级分布: {level_distribution}")
        
        return orders_to_cancel
        
    def _generate_active_orders(self, current_price: Decimal, order_count: int) -> List[Dict]:
        """生成新的活跃梯度订单（结合梯度结构和活跃刷新）"""
        if order_count <= 0:
            return []
        
        orders = []
        
        # 统计当前各等级的订单数量（基于等级信息跟踪）
        level_counts = {level: {"buy": 0, "sell": 0} for level in range(1, self.gradient_levels + 1)}
        for order_info in self.order_level_info.values():
            side_key = "buy" if order_info.side == OrderSide.BUY else "sell"
            if order_info.level in level_counts:
                level_counts[order_info.level][side_key] += 1
        
        # 计算每个等级需要补充的订单数
        level_needs = []
        for gradient_level in self.gradient_config:
            level = gradient_level.level
            buy_need = max(0, gradient_level.max_orders - level_counts[level]["buy"])
            sell_need = max(0, gradient_level.max_orders - level_counts[level]["sell"])
            level_needs.extend([("buy", level, gradient_level)] * buy_need)
            level_needs.extend([("sell", level, gradient_level)] * sell_need)
        
        # 如果还需要更多订单，重复添加各等级（保持平衡）
        if len(level_needs) < order_count:
            additional_count = order_count - len(level_needs)
            for i in range(additional_count):
                gradient_level = self.gradient_config[i % len(self.gradient_config)]
                is_buy = i % 2 == 0  # 交替买卖单
                level_needs.append(("buy" if is_buy else "sell", gradient_level.level, gradient_level))
        
        # 打乱订单生成顺序，增加随机性
        random.shuffle(level_needs[:order_count])
        
        # 生成指定数量的订单
        for i in range(min(order_count, len(level_needs))):
            try:
                side, level, gradient_level = level_needs[i]
                is_buy = (side == "buy")
                
                # 计算该等级的基础价格
                if is_buy:
                    base_price = current_price * (Decimal("1") - gradient_level.price_offset_ratio)
                else:
                    base_price = current_price * (Decimal("1") + gradient_level.price_offset_ratio)
                
                # 在基础价格上添加小幅波动（保持梯度结构但增加活跃度）
                wave_price = self._calculate_gradient_wave_price(base_price)
                
                # 价格安全检查：确保价格合理
                if not self._is_price_safe_for_trading(wave_price, current_price, is_buy):
                    self.logger.warning(f"⚠️ 跳过不安全的价格: {wave_price} (基准: {current_price})")
                    continue
                
                # 计算订单数量（考虑等级权重）
                base_quantity = self._calculate_order_quantity(gradient_level, wave_price)
                quantity = self._add_quantity_randomness(base_quantity)
                
                # 创建订单
                order_direction = 1 if is_buy else 2
                trade_bean = self.create_trade_bean(
                    trading_pair=self.trading_pair,
                    order_direction=order_direction,
                    order_quantity=self._format_quantity(quantity, self.qty_precision),
                    order_price=self._format_price(wave_price, self.price_precision)
                )
                
                orders.append(trade_bean)
                
                # 预设等级信息（在订单创建时就设定等级）
                # 注意：由于还没有订单ID，这个信息会在订单下单成功后通过API回调更新
                self.logger.debug(f"🎯 生成活跃梯度订单: L{level} {'买' if is_buy else '卖'} {quantity:.2f} @ {wave_price}")
                
            except Exception as e:
                self.logger.error(f"❌ 生成活跃梯度订单失败: {e}")
        
        # 显示生成的订单统计
        if orders:
            level_distribution = {}
            for i, (side, level, _) in enumerate(level_needs[:len(orders)]):
                level_distribution[level] = level_distribution.get(level, 0) + 1
            self.logger.info(f"✅ 生成 {len(orders)} 个活跃梯度订单，等级分布: {level_distribution}")
                
        return orders
        
    def _calculate_gradient_wave_price(self, base_price: Decimal) -> Decimal:
        """计算梯度订单的波动价格（在梯度基础上添加小幅波动）"""
        
        # 修复：简化波动计算，避免价格混乱
        current_time = time.time()
        time_elapsed = current_time - self.wave_start_time
        
        # 波动幅度减小系数（可配置）
        jitter_reduction = float(self._get_config_value("GL_JITTER_REDUCTION_FACTOR", "0.2", str))  # 默认20%
        safe_jitter = float(self.price_jitter) * jitter_reduction
        
        # 只使用轻微的随机抖动，去掉正弦波
        jitter_component = random.uniform(-safe_jitter, safe_jitter)
        
        # 应用偏移
        final_price = base_price * (Decimal("1") + Decimal(str(jitter_component)))
        
        # 价格变动范围限制（可配置）
        max_wave_range = Decimal(self._get_config_value("GL_MAX_WAVE_RANGE", "0.005", str))  # 默认0.5%
        min_price = base_price * (Decimal("1") - max_wave_range)
        max_price = base_price * (Decimal("1") + max_wave_range)
        
        return max(min_price, min(max_price, final_price))
    
    def _is_price_safe_for_trading(self, order_price: Decimal, market_price: Decimal, is_buy: bool) -> bool:
        """检查订单价格是否安全可交易（防止价格异常）"""
        try:
            # 基本正数检查
            if order_price <= 0:
                return False
                
            # 价格偏离度检查（使用配置参数）
            max_deviation = Decimal(self._get_config_value("GL_MAX_PRICE_DEVIATION", "0.5", str))  # 默认50%
            price_deviation = abs(order_price - market_price) / market_price
            if price_deviation > max_deviation:
                self.logger.debug(f"价格偏离过大: {price_deviation:.2%} > {max_deviation:.2%}")
                return False
                
            # 买卖方向检查（使用配置参数）
            buy_limit_ratio = Decimal(self._get_config_value("GL_BUY_LIMIT_RATIO", "1.1", str))  # 默认110%
            sell_limit_ratio = Decimal(self._get_config_value("GL_SELL_LIMIT_RATIO", "0.9", str))  # 默认90%
            
            if is_buy and order_price > market_price * buy_limit_ratio:
                self.logger.debug(f"买单价格过高: {order_price} > {market_price * buy_limit_ratio}")
                return False
            elif not is_buy and order_price < market_price * sell_limit_ratio:
                self.logger.debug(f"卖单价格过低: {order_price} < {market_price * sell_limit_ratio}")
                return False
                
            # 最小价格检查（使用配置参数）
            min_price = Decimal(self._get_config_value("GL_MIN_PRICE_LIMIT", "0.000001", str))  # 默认1微元
            if order_price < min_price:
                self.logger.debug(f"价格低于最小值: {order_price} < {min_price}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"价格安全检查失败: {e}")
            return False