import time
import decimal
import logging # Import logging
from typing import List, Dict, Any, Optional
from decimal import Decimal, InvalidOperation # Ensure Decimal is imported

from .base_strategy import BaseStrategy, _REQUIRED # Import _REQUIRED
from app.services.nine_client import NineClient # For type hinting

class CumulativeDepthStrategy(BaseStrategy):
    def __init__(
        self,
        config: Dict[str, Any],
        nine_client: NineClient,
        logger: logging.Logger 
    ):
        super().__init__(config, nine_client, logger)
        self.last_execution_time: float = 0.0

        # Load CumulativeDepthStrategy specific API keys. These are now mandatory.
        self.api_key = self._get_config_value("CDS_NINE_API_KEY")
        self.api_secret = self._get_config_value("CDS_NINE_API_SECRET")

        # 初始化日志已在 BaseStrategy 中统一处理

    def _get_price_from_cumulative_depth(
        self,
        side: str, 
        pool_orders: List[List[str]], 
        cumulative_amount_threshold: Decimal,
        price_adjustment: Decimal,
        max_levels_to_scan: int
    ) -> Optional[Decimal]:
        accumulated_amount = Decimal("0")
        target_price = None
        scanned_levels_count = 0

        if not pool_orders:
            self.logger.warning(f"⚠️ {side}方向无Nine CEX订单簿数据")
            return None

        for i, order_level in enumerate(pool_orders):
            scanned_levels_count = i + 1
            if i >= max_levels_to_scan:
                self.logger.warning(f"⚠️ {side}方向达到最大扫描深度{max_levels_to_scan}，累积量{accumulated_amount}未达标{cumulative_amount_threshold}")
                break
            
            if not (isinstance(order_level, list) and len(order_level) >= 2):
                continue

            try:
                price_str, amount_str = str(order_level[0]), str(order_level[1])
                price = Decimal(price_str)
                amount = Decimal(amount_str)
            except (InvalidOperation, TypeError, ValueError) as e:
                continue

            if price <= Decimal("0") or amount <= Decimal("0"):
                continue
                
            accumulated_amount += amount
            if accumulated_amount >= cumulative_amount_threshold:
                target_price = price + price_adjustment 
                break
        
        if target_price is None:
            # Fallback: Use the price from the best available level if any meaningful level was scanned
            if pool_orders and scanned_levels_count > 0: # Ensure at least one level was processed to attempt fallback
                # Try to get price from the first valid level encountered, or the last scanned valid one
                # For simplicity, let's try the very first level if it's valid.
                first_level = pool_orders[0]
                if isinstance(first_level, list) and len(first_level) >= 2:
                    try:
                        fallback_price = Decimal(str(first_level[0])) + price_adjustment
                        return fallback_price
                    except (InvalidOperation, TypeError, ValueError) as e:
                        return None
                else:
                    return None
            else: # No levels scanned or pool_orders was empty initially
                return None

        return target_price

    def get_actions(self, current_active_orders: List[Dict[str, Any]], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        actions: List[Dict[str, Any]] = []

        try:
            trading_pair = str(self._get_config_value("CDS_TRADING_PAIR", expected_type=str))
            
            cumulative_buy_depth_amount = self._get_config_value("CDS_CUMULATIVE_BUY_DEPTH_AMOUNT", expected_type=Decimal)
            cumulative_sell_depth_amount = self._get_config_value("CDS_CUMULATIVE_SELL_DEPTH_AMOUNT", expected_type=Decimal)
            price_adj_buy = self._get_config_value("CDS_PRICE_ADJUSTMENT_BUY", Decimal("0.01"), Decimal)
            price_adj_sell = self._get_config_value("CDS_PRICE_ADJUSTMENT_SELL", Decimal("-0.01"), Decimal)
            
            min_spread_price_cfg = self._get_config_value("CDS_MIN_SPREAD_PRICE", expected_type=Decimal)
            spread_adj_buy_cfg = self._get_config_value("CDS_SPREAD_ADJUSTMENT_BUY", expected_type=Decimal)
            spread_adj_sell_cfg = self._get_config_value("CDS_SPREAD_ADJUSTMENT_SELL", expected_type=Decimal)

            # simulated_quote_balance = self._get_config_value("CDS_SIMULATED_ACCOUNT_QUOTE_CURRENCY", expected_type=Decimal)
            # simulated_base_balance = self._get_config_value("CDS_SIMULATED_ACCOUNT_BASE_CURRENCY", expected_type=Decimal)
            # buy_qty_reserve_quote = self._get_config_value("CDS_BUY_QTY_RESERVE_QUOTE", Decimal("0.1"), Decimal)
            order_amount_base = self._get_config_value("CDS_ORDER_AMOUNT_BASE", expected_type=Decimal)
            min_order_qty_base = self._get_config_value("CDS_MIN_ORDER_QTY_BASE", Decimal("0.02"), Decimal)

            price_precision = self._get_config_value("CDS_PRICE_PRECISION", expected_type=int)
            qty_precision = self._get_config_value("CDS_QTY_PRECISION", expected_type=int)
            
            # Get general Nine CEX order parameters
            account_type = self._get_config_value("NINE_ACCOUNT_TYPE", expected_type=int)
            order_dir_buy = self._get_config_value("NINE_ORDER_DIR_BUY", expected_type=int)
            order_dir_sell = self._get_config_value("NINE_ORDER_DIR_SELL", expected_type=int)
            order_type_limit = self._get_config_value("NINE_ORDER_TYPE_LIMIT", expected_type=int)
            
            max_pool_scan_levels = self._get_config_value("CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN", 20, int)
        except ValueError as e:
            self.logger.error(f"❌ 配置参数错误: {e}")
            return actions

        current_time = time.time()
        # 移除间隔检查 - 现在由TradingBotService统一控制执行频率

        if current_active_orders:
            order_ids_to_cancel = [order['orderId'] for order in current_active_orders if order.get('orderId')]
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids_to_cancel,
                    "reason": "撤销旧订单"
                })
        
        pool_data = None
        try:
            # 解析交易对ID以支持新的API格式
            trade_pair_id = self.resolve_trading_pair_id(trading_pair)
            
            business_data = self.nine_client.get_outer_pool_latest_order(
                trade_pair_name=trading_pair,
                api_key=self.api_key,
                secret=self.api_secret,
                trade_pair_id=trade_pair_id,  # 新增交易对ID参数
                num=5, # 暂时固定为5，与Postman和VolumeKlineStrategy的成功调用一致
                precision="0.00000001" # 使用最高精度字符串格式，确保价格显示正确
            )
        except Exception as e:
            self.logger.error(f"❌ 获取Nine CEX {trading_pair}订单簿失败: {e}")
            self.last_execution_time = current_time # Update time to prevent rapid retries on error
            return actions

        # 适配新的business_data格式
        pool_data = None
        if business_data and isinstance(business_data, dict):
            if "orderDepth" in business_data:
                pool_data = business_data["orderDepth"]
            elif "asks" in business_data and "bids" in business_data:
                pool_data = business_data  # 向后兼容

        if not pool_data or not pool_data.get("bids") or not pool_data.get("asks"):
            self.logger.warning(f"⚠️ Nine CEX {trading_pair}订单簿数据不完整")
            self.last_execution_time = current_time
            return actions

        pool_bids = pool_data.get("bids", [])
        pool_asks = pool_data.get("asks", [])

        target_buy_price_raw = self._get_price_from_cumulative_depth(
            "buy", pool_bids, cumulative_buy_depth_amount, price_adj_buy, max_pool_scan_levels
        )
        target_sell_price_raw = self._get_price_from_cumulative_depth(
            "sell", pool_asks, cumulative_sell_depth_amount, price_adj_sell, max_pool_scan_levels
        )

        if target_buy_price_raw is None or target_sell_price_raw is None:
            self.logger.warning("⚠️ 无法确定有效的买卖价格")
            self.last_execution_time = current_time
            return actions 
            
        final_buy_price = target_buy_price_raw
        final_sell_price = target_sell_price_raw

        # Add check for crossed market BEFORE min_spread_price adjustments
        if target_buy_price_raw is not None and target_sell_price_raw is not None and target_buy_price_raw >= target_sell_price_raw:
            self.logger.warning(f"⚠️ 目标价格交叉 买:{target_buy_price_raw} ≥ 卖:{target_sell_price_raw}")
            self.last_execution_time = current_time
            return actions

        if (final_sell_price - final_buy_price) <= min_spread_price_cfg:
            final_buy_price += spread_adj_buy_cfg   
            final_sell_price += spread_adj_sell_cfg

        if final_buy_price <= Decimal("0") or final_sell_price <= Decimal("0") or final_buy_price >= final_sell_price:
            self.logger.error(f"❌ 调整后价格无效 买:{final_buy_price} 卖:{final_sell_price}")
            self.last_execution_time = current_time
            return actions

        orders_to_place = []
        
        # Prepare Sell Order (based on fixed amount)
        if order_amount_base > Decimal("0") and order_amount_base >= min_order_qty_base:
            formatted_sell_qty = self._format_quantity(order_amount_base, qty_precision)
            formatted_sell_price = self._format_price(final_sell_price, price_precision)
            
            if Decimal(formatted_sell_qty) > Decimal("0"): # Ensure positive quantity after formatting
                sell_order = self.create_trade_bean(
                    trading_pair=trading_pair,
                    order_direction=order_dir_sell,
                    order_quantity=formatted_sell_qty,
                    order_price=formatted_sell_price,
                    account_type=account_type,
                    order_type=order_type_limit
                )
                orders_to_place.append(sell_order)

        # Prepare Buy Order (based on fixed amount)
        if order_amount_base > Decimal("0") and final_buy_price > Decimal("0") and order_amount_base >= min_order_qty_base:
            formatted_buy_qty = self._format_quantity(order_amount_base, qty_precision)
            formatted_buy_price = self._format_price(final_buy_price, price_precision)

            if Decimal(formatted_buy_qty) > Decimal("0"): # Ensure positive quantity after formatting
                buy_order = self.create_trade_bean(
                    trading_pair=trading_pair,
                    order_direction=order_dir_buy,
                    order_quantity=formatted_buy_qty,
                    order_price=formatted_buy_price,
                    account_type=account_type,
                    order_type=order_type_limit
                )
                orders_to_place.append(buy_order)

        if orders_to_place:
            actions.append({
                "action_type": "place",
                "orders": orders_to_place,
                "reason": f"累积深度策略订单"
            })

        self.last_execution_time = current_time
        return actions


