from decimal import Decimal, ROUND_DOWN, ROUND_UP
from typing import List, Dict, Optional, Any
import time
import logging

from app.services.nine_client import NineClient
from app.services.binance_client import BinanceClient
from .base_strategy import BaseStrategy # 确保 BaseStrategy 被正确导入

class CrossExchangeArbitrageStrategy(BaseStrategy):
    """
    跨交易所套利策略 (Nine CEX vs Binance CEX)。
    """
    def __init__(self, config: Dict[str, Any], nine_client: NineClient, logger: logging.Logger, binance_client: Optional[BinanceClient] = None):
        super().__init__(config, nine_client, logger)
        
        self.nine_client = nine_client
        self.binance_client = binance_client
        
        if not self.binance_client:
            self.logger.warning(f"⚠️ 币安客户端不可用，币安交易功能将不可用")

        # API Keys - 设置为 BaseStrategy 期望的属性名
        self.nine_api_key = self._get_config_value("ARB_NINE_API_KEY", expected_type=str)
        self.nine_api_secret = self._get_config_value("ARB_NINE_API_SECRET", expected_type=str)
        
        # 设置 BaseStrategy 期望的 api_key 和 api_secret 以便与现有的 _execute_placements 兼容
        self.api_key = self.nine_api_key
        self.api_secret = self.nine_api_secret

        # 基础配置
        self.nine_cex_symbol = self._get_config_value("ARB_NINE_SYMBOL", "SEPETH", str)
        self.binance_symbol = self._get_config_value("ARB_BINANCE_SYMBOL", "SEPETH", str)
        self.min_profit_percentage = self._get_config_value("ARB_MIN_PROFIT_PCT", Decimal("0.001"), Decimal)
        self.slippage_factor = self._get_config_value("ARB_SLIPPAGE_FACTOR", Decimal("0.0005"), Decimal)
        
        # 数量计算策略配置
        self.order_amount_mode = self._get_config_value("ARB_ORDER_AMOUNT_MODE", "fixed", str)  # fixed, percentage, adaptive
        self.order_amount_base = self._get_config_value("ARB_ORDER_AMOUNT_BASE", Decimal("0.001"), Decimal)
        self.order_amount_percentage = self._get_config_value("ARB_ORDER_AMOUNT_PERCENTAGE", Decimal("0.1"), Decimal)  # 盘口数量的10%
        self.order_amount_max = self._get_config_value("ARB_ORDER_AMOUNT_MAX", Decimal("0.01"), Decimal)  # 最大数量限制
        self.order_amount_min = self._get_config_value("ARB_ORDER_AMOUNT_MIN", Decimal("0.0001"), Decimal)  # 最小数量限制
        
        # 费率配置
        self.nine_fee_rate = self._get_config_value("ARB_NINE_FEE_RATE", Decimal("0.001"), Decimal)
        self.binance_fee_rate = self._get_config_value("ARB_BINANCE_FEE_RATE", Decimal("0.001"), Decimal)
        
        # 其他配置
        self.max_pending_orders = self._get_config_value("ARB_MAX_PENDING_ORDERS", 5, int)
        self.order_timeout_seconds = self._get_config_value("ARB_ORDER_TIMEOUT_SECONDS", 300, int)
        self.binance_ioc_check_timeout = self._get_config_value("ARB_BINANCE_IOC_CHECK_TIMEOUT", 5, int)
        
        # Binance 精度配置
        self.binance_qty_precision_fallback = self._get_config_value("ARB_BINANCE_QTY_PRECISION", 6, int)
        self.binance_price_precision_fallback = self._get_config_value("ARB_BINANCE_PRICE_PRECISION", 8, int)
        self.binance_min_base_qty = self._get_config_value("ARB_BINANCE_MIN_BASE_QTY", Decimal("0.0001"), Decimal)
        
        # Nine CEX 订单簿配置
        self.nine_pool_precision_api_param = float(self._get_config_value("ARB_NINE_POOL_PRECISION_API_PARAM", "0.01", str))
        
        # 订单跟踪
        self.executed_arbitrage_legs: List[Dict[str, Any]] = []
        self.pending_arbitrage_orders: Dict[str, Dict] = {}
        self.last_run_time = 0

        self.logger.info(f"跨交易所套利策略已初始化 | Nine:{self.nine_cex_symbol}, Binance:{self.binance_symbol}, 最小收益:{self.min_profit_percentage*100:.2f}%")

    def _get_nine_cex_order_book(self) -> Optional[Dict]:
        try:
            if not self.nine_client: return None
            
            # 解析交易对ID以支持新的API格式
            trade_pair_id = self.resolve_trading_pair_id(self.nine_cex_symbol)
            
            # Add appropriate API key and secret if needed by the client method, or ensure client handles it.
            business_data = self.nine_client.get_outer_pool_latest_order(
                api_key=self.nine_api_key,
                secret=self.nine_api_secret,
                trade_pair_name=self.nine_cex_symbol,
                trade_pair_id=trade_pair_id,  # 新增交易对ID参数
                precision="0.00000001",  # 使用最高精度字符串格式
                num=5 # Fetch a few levels
            )
            # get_outer_pool_latest_order 现在返回完整的business_data，包含orderDepth和quote信息
            book = None
            if business_data and isinstance(business_data, dict):
                if "orderDepth" in business_data:
                    book = business_data["orderDepth"]
                elif "asks" in business_data and "bids" in business_data:
                    book = business_data  # 向后兼容
            
            if book and isinstance(book, dict) and "asks" in book and "bids" in book:
                self.last_nine_book_fetch_time = time.time()
                return book
            self.logger.warning(f"⚠️ Nine CEX订单簿数据异常: {book}")
            return None
        except Exception as e:
            self.logger.error(f"❌ 获取Nine CEX订单簿失败: {e}")
            return None
    
    def _get_binance_order_book(self) -> Optional[Dict]:
        if not self.binance_client:
            return None
        try:
            order_book = self.binance_client.get_order_book(symbol=self.binance_symbol, limit=5)
            if order_book and order_book.get("bids") and order_book.get("asks"):
                self.last_binance_book_fetch_time = time.time()
                return order_book
            self.logger.warning(f"⚠️ 币安{self.binance_symbol}订单簿数据异常: {order_book}")
            return None
        except Exception as e:
            self.logger.error(f"❌ 获取币安{self.binance_symbol}订单簿失败: {e}")
            return None

    def _format_decimal_for_nine(self, value: Decimal, precision_type: str) -> str:
        return str(value)

    def _calculate_trade_quantity(self, nine_qty: Decimal, binance_qty: Decimal, profit_pct: Decimal) -> Decimal:
        """
        根据配置的模式计算交易数量
        
        Args:
            nine_qty: Nine CEX 盘口数量
            binance_qty: Binance 盘口数量
            profit_pct: 预期收益率
            
        Returns:
            计算出的交易数量
        """
        available_qty = min(nine_qty, binance_qty)
        
        if self.order_amount_mode == "fixed":
            # 固定数量模式
            calculated_qty = self.order_amount_base
            
        elif self.order_amount_mode == "percentage":
            # 按盘口数量百分比计算
            calculated_qty = available_qty * self.order_amount_percentage
            
        elif self.order_amount_mode == "adaptive":
            # 自适应模式：根据收益率和盘口深度动态调整
            # 收益率越高，使用更多数量；盘口越深，可以使用更多数量
            profit_multiplier = min(profit_pct / self.min_profit_percentage, Decimal("5"))  # 最多5倍基础数量
            depth_multiplier = min(available_qty / self.order_amount_base, Decimal("3"))  # 最多3倍基础数量
            
            calculated_qty = self.order_amount_base * profit_multiplier * depth_multiplier * Decimal("0.5")
            
        else:
            # 默认使用固定数量
            calculated_qty = self.order_amount_base
        
        # 应用最大最小限制
        calculated_qty = max(self.order_amount_min, min(calculated_qty, self.order_amount_max))
        
        # 确保不超过可用盘口数量
        final_qty = min(calculated_qty, available_qty)
        
        self.logger.debug(f"📊 数量计算 | 模式: {self.order_amount_mode} | 盘口可用: {available_qty} | 计算结果: {calculated_qty} | 最终数量: {final_qty}")
        
        return final_qty

    def _process_executed_legs(self):
        processed_count = 0
        for leg_info in list(self.executed_arbitrage_legs): 
            processed_count += 1
            exchange = leg_info.get("exchange")
            order_id = leg_info.get("order_id")
            client_order_id = leg_info.get("client_order_id")
            api_params = leg_info.get("api_params", {})
            symbol = api_params.get("symbol") or api_params.get("pair_code")
            
            unique_key = None
            if order_id:
                unique_key = f"{exchange}_{order_id}"
            elif exchange == "binance" and client_order_id:
                unique_key = f"{exchange}_cid_{client_order_id}"
            else:
                timestamp_ms = int(leg_info.get("submitted_at", time.time()) * 1000)
                unique_key = f"{exchange}_failed_{symbol}_{timestamp_ms}"

            if unique_key in self.pending_arbitrage_orders:
                continue

            self.pending_arbitrage_orders[unique_key] = {
                "exchange": exchange, "order_id": order_id, "client_order_id": client_order_id,
                "symbol": symbol, "side": api_params.get("side") or api_params.get("order_direction"),
                "type": api_params.get("type") or api_params.get("order_type"),
                "price": api_params.get("price"), "quantity": api_params.get("quantity"),
                "status": leg_info.get("status_from_response", "PENDING_SUBMISSION"),
                "executed_qty": leg_info.get("executed_qty_from_response", "0"),
                "submitted_at": leg_info.get("submitted_at", time.time()),
                "last_checked_at": time.time(), "api_params": api_params,
                "error": leg_info.get("error"), "finalized": False
            }
            # 移除冗余的订单添加日志
        
        self.executed_arbitrage_legs.clear()
        # 移除冗余的处理统计日志

    def _check_pending_orders_status(self) -> List[Dict[str, Any]]:
        actions: List[Dict[str, Any]] = []
        current_time = time.time()
        orders_to_remove_keys: List[str] = []

        for key, order_data in list(self.pending_arbitrage_orders.items()): # Iterate on copy for safe modification
            if order_data.get("finalized", False):
                continue # Already processed to a terminal state

            exchange = order_data["exchange"]
            order_id = order_data["order_id"]
            client_order_id = order_data.get("client_order_id")
            symbol = order_data["symbol"]
            submitted_at = order_data["submitted_at"]
            status = order_data["status"]
            order_type = order_data["type"]

            if exchange == "binance":
                if not self.binance_client:
                    order_data["status"], order_data["finalized"] = "CHECK_FAILED_NO_CLIENT", True
                    orders_to_remove_keys.append(key)
                    continue
                
                if not order_id and not client_order_id:
                    order_data["status"], order_data["finalized"] = "ID_MISSING_ERROR", True
                    orders_to_remove_keys.append(key)
                    continue

                # Check IOC orders if they were not immediately finalized or if status is unknown
                is_ioc_potentially_pending = order_type == "IOC" and status in ["NEW", "PENDING_SUBMISSION", "UNKNOWN", "PARTIALLY_FILLED"]
                
                if is_ioc_potentially_pending and (current_time - order_data.get("last_checked_at", submitted_at) > self.binance_ioc_check_timeout):
                    try:
                        order_info = self.binance_client.get_order_info(
                            symbol=symbol, 
                            order_id=order_id, 
                            orig_client_order_id=client_order_id if not order_id else None
                        )
                        new_status = order_info.get("status")
                        executed_qty = order_info.get("executedQty", order_data["executed_qty"])
                        
                        order_data["status"] = new_status
                        order_data["executed_qty"] = executed_qty
                        order_data["last_checked_at"] = current_time

                        if new_status in ["FILLED", "CANCELED", "EXPIRED", "REJECTED"]: 
                            order_data["finalized"] = True
                        # PARTIALLY_FILLED for IOC is also a kind of terminal state as it won't fill further.
                        elif new_status == "PARTIALLY_FILLED" and order_type == "IOC":
                             order_data["finalized"] = True
                    except Exception as e:
                        order_data["last_checked_at"] = current_time # Update check time even on error to avoid rapid retries
                
                elif order_type == "IOC" and status in ["FILLED", "CANCELED", "EXPIRED", "REJECTED"] and not order_data["finalized"]:
                    order_data["finalized"] = True
            
            elif exchange == "nine_cex":
                if order_id and status not in ["FILLED", "CANCELED", "REJECTED", "FAILED_TO_SUBMIT"] and not order_data["finalized"]:
                    if current_time - submitted_at > self.order_timeout_seconds:
                        order_data["status"] = "TIMED_OUT_AWAITING_CANCEL_LOGIC"
                        order_data["finalized"] = True 
                        # TODO: When NineClient supports cancel_order, generate an action here.
                        # actions.append({"action_type": "cancel_order_nine", ...})
                elif status in ["FAILED_TO_SUBMIT", "REJECTED"] and not order_data["finalized"]:
                     order_data["finalized"] = True
                     order_data["error"] = order_data.get("error", f"Order was {status}")
            
            if order_data["finalized"] and key not in orders_to_remove_keys:
                orders_to_remove_keys.append(key)
        
        for key_to_remove in orders_to_remove_keys:
            if key_to_remove in self.pending_arbitrage_orders:
                final_details = self.pending_arbitrage_orders.pop(key_to_remove) # Remove from active tracking
                # Optionally, move to an archive list: self.archived_orders.append(final_details)
        
        return actions # Currently, no actions are generated from this method for bot service to act upon

    def get_actions(self, current_active_orders: Optional[List[Dict]] = None, nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        actions: List[Dict[str, Any]] = []
        current_time = time.time()

        self._process_executed_legs()
        
        # Check status of pending orders before deciding to fetch new books or create new orders.
        # This can also help in clearing out finalized orders from self.pending_arbitrage_orders.
        status_check_actions = self._check_pending_orders_status()
        # actions.extend(status_check_actions) # If any actions like cancellations were generated

        # 移除间隔检查 - 现在由TradingBotService统一控制执行频率
        
        self.logger.info(f"🔄 开始套利检查周期 ({time.strftime('%Y-%m-%d %H:%M:%S')})")
        self.last_run_time = current_time

        if len(self.pending_arbitrage_orders) >= self.max_pending_orders:
            return actions

        # Fetch fresh order books
        nine_book_data = nine_cex_order_book_data or self._get_nine_cex_order_book()
        binance_book_data = self._get_binance_order_book()

        if not nine_book_data or not binance_book_data:
            return actions
        
        try:
            # Assuming nine_book_data is the direct 'data' part of the API response
            nine_bids_raw = nine_book_data.get("bids", [])
            nine_asks_raw = nine_book_data.get("asks", [])

            if not nine_bids_raw or not nine_asks_raw:
                return actions
            
            # Nine CEX: 实际格式是 [[price_str, quantity_str], ...] （和 Binance 一样）
            if not isinstance(nine_bids_raw[0], list) or len(nine_bids_raw[0]) < 2:
                return actions
            if not isinstance(nine_asks_raw[0], list) or len(nine_asks_raw[0]) < 2:
                return actions

            nine_best_bid_price = Decimal(str(nine_bids_raw[0][0]))
            nine_best_bid_qty = Decimal(str(nine_bids_raw[0][1]))
            nine_best_ask_price = Decimal(str(nine_asks_raw[0][0]))
            nine_best_ask_qty = Decimal(str(nine_asks_raw[0][1]))

            # Binance: structure assumed [[price_str, quantity_str], ...]
            binance_bids_raw = binance_book_data.get("bids", [])
            binance_asks_raw = binance_book_data.get("asks", [])

            if not binance_bids_raw or not binance_asks_raw:
                return actions
            
            binance_best_bid_price = Decimal(str(binance_bids_raw[0][0]))
            binance_best_bid_qty = Decimal(str(binance_bids_raw[0][1]))
            binance_best_ask_price = Decimal(str(binance_asks_raw[0][0]))
            binance_best_ask_qty = Decimal(str(binance_asks_raw[0][1]))

            self.logger.info(f"📊 市场数据 | Nine({self.nine_cex_symbol}): 买₁ {nine_best_bid_price} (量:{nine_best_bid_qty}), 卖₁ {nine_best_ask_price} (量:{nine_best_ask_qty}) | Binance({self.binance_symbol}): 买₁ {binance_best_bid_price} (量:{binance_best_bid_qty}), 卖₁ {binance_best_ask_price} (量:{binance_best_ask_qty})")

            # --- Opportunity 1: Buy on Nine CEX, Sell on Binance ---
            nine_buy_price_adjusted = nine_best_ask_price * (Decimal(1) + self.slippage_factor)
            binance_sell_price_adjusted = binance_best_bid_price * (Decimal(1) - self.slippage_factor) # For calculation only

            nine_cost_after_fee = nine_buy_price_adjusted * (Decimal(1) + self.nine_fee_rate)
            binance_revenue_after_fee = binance_best_bid_price * (Decimal(1) - self.binance_fee_rate) # Actual sell uses best bid
            
            if nine_cost_after_fee > 0:
                profit_buy_nine_sell_binance_pct = (binance_revenue_after_fee - nine_cost_after_fee) / nine_cost_after_fee
                self.logger.info(f"📈 方向1分析 | Nine买入成本: {nine_cost_after_fee:.6f}, Binance卖出收益: {binance_revenue_after_fee:.6f}, 收益率: {profit_buy_nine_sell_binance_pct*100:.4f}%")

                if profit_buy_nine_sell_binance_pct >= self.min_profit_percentage:
                    self.logger.info(f"💰 发现套利机会1! Nine买入, Binance卖出 | 预期收益: {profit_buy_nine_sell_binance_pct*100:.4f}%")
                    qty_to_trade = self._calculate_trade_quantity(nine_best_ask_qty, binance_best_bid_qty, profit_buy_nine_sell_binance_pct)
                    if qty_to_trade >= self.binance_min_base_qty:                        
                        actions.append({
                            "action_type": "place_order_binance",
                            "params": {
                                "symbol": self.binance_symbol, "side": "SELL", "order_type": "LIMIT",
                                "quantity": f"{qty_to_trade:.{self.binance_qty_precision_fallback}f}", 
                                "price": f"{binance_best_bid_price:.{self.binance_price_precision_fallback}f}", # Sell at Binance's best bid for IOC
                                "time_in_force": "IOC"
                            },
                            "reason": f"套利交易: 币安卖出, Nine CEX买入. 收益: {profit_buy_nine_sell_binance_pct*100:.2f}%"
                        })
                        # 使用统一的 place action，符合现有的 _execute_placements 格式
                        nine_order = {
                            "accountType": self._get_config_value("NINE_ACCOUNT_TYPE", "1", str),
                            "tradePairName": self.nine_cex_symbol,
                            "orderDirection": self._get_config_value("NINE_ORDER_DIR_BUY", 1, int),
                            "orderType": self._get_config_value("NINE_ORDER_TYPE_LIMIT", 1, int),
                            "orderPrice": self._format_decimal_for_nine(nine_buy_price_adjusted, "price"),
                            "orderQuantity": self._format_decimal_for_nine(qty_to_trade, "quantity")
                        }
                        actions.append({
                            "action_type": "place",
                            "orders": [nine_order],
                            "reason": f"套利交易: Nine CEX买入, 币安卖出. 收益: {profit_buy_nine_sell_binance_pct*100:.2f}%"
                        })
                        return actions 

            # --- Opportunity 2: Buy on Binance, Sell on Nine CEX ---
            binance_buy_price_adjusted = binance_best_ask_price * (Decimal(1) + self.slippage_factor)
            nine_sell_price_adjusted = nine_best_bid_price * (Decimal(1) - self.slippage_factor) # For calculation

            binance_cost_after_fee = binance_buy_price_adjusted * (Decimal(1) + self.binance_fee_rate)
            nine_revenue_after_fee = nine_best_bid_price * (Decimal(1) - self.nine_fee_rate) # Actual sell uses best bid

            if binance_cost_after_fee > 0:
                profit_buy_binance_sell_nine_pct = (nine_revenue_after_fee - binance_cost_after_fee) / binance_cost_after_fee
                self.logger.info(f"📈 方向2分析 | Binance买入成本: {binance_cost_after_fee:.6f}, Nine卖出收益: {nine_revenue_after_fee:.6f}, 收益率: {profit_buy_binance_sell_nine_pct*100:.4f}%")

                if profit_buy_binance_sell_nine_pct >= self.min_profit_percentage:
                    self.logger.info(f"💰 发现套利机会2! Binance买入, Nine卖出 | 预期收益: {profit_buy_binance_sell_nine_pct*100:.4f}%")
                    qty_to_trade = self._calculate_trade_quantity(binance_best_ask_qty, nine_best_bid_qty, profit_buy_binance_sell_nine_pct)
                    if qty_to_trade >= self.binance_min_base_qty:
                        actions.append({
                            "action_type": "place_order_binance",
                            "params": {
                                "symbol": self.binance_symbol, "side": "BUY", "order_type": "LIMIT",
                                "quantity": f"{qty_to_trade:.{self.binance_qty_precision_fallback}f}", 
                                "price": f"{binance_best_ask_price:.{self.binance_price_precision_fallback}f}", # Buy at Binance's best ask for IOC
                                "time_in_force": "IOC"
                            },
                            "reason": f"套利交易: 币安买入, Nine CEX卖出. 收益: {profit_buy_binance_sell_nine_pct*100:.2f}%"
                        })
                        # 使用统一的 place action，符合现有的 _execute_placements 格式
                        nine_order = {
                            "accountType": self._get_config_value("NINE_ACCOUNT_TYPE", "1", str),
                            "tradePairName": self.nine_cex_symbol,
                            "orderDirection": self._get_config_value("NINE_ORDER_DIR_SELL", 2, int),
                            "orderType": self._get_config_value("NINE_ORDER_TYPE_LIMIT", 1, int),
                            "orderPrice": self._format_decimal_for_nine(nine_sell_price_adjusted, "price"),
                            "orderQuantity": self._format_decimal_for_nine(qty_to_trade, "quantity")
                        }
                        actions.append({
                            "action_type": "place",
                            "orders": [nine_order],
                            "reason": f"套利交易: Nine CEX卖出, 币安买入. 收益: {profit_buy_binance_sell_nine_pct*100:.2f}%"
                        })
                        return actions 
            
            self.logger.info(f"❌ 本轮无套利机会符合条件")

        except Exception as e:
            self.logger.error(f"❌ 套利策略执行异常: {e}")

        return actions 