from decimal import Decimal, InvalidOperation as DecimalInvalidOperation
from typing import List, Dict, Optional, Any
import logging
import time

from .base_strategy import BaseStrategy
from app.services.nine_client import NineClient
from app.services.binance_client import BinanceClient


class MirrorBinanceStrategy(BaseStrategy):
    def __init__(self, config: Dict[str, Any], nine_client: NineClient, binance_client: BinanceClient, logger: logging.Logger):
        super().__init__(config, nine_client, logger)
        self.binance_client = binance_client
        # Specific config for this strategy
        self.trade_pair = self._get_config_value("MM_NINE_CEX_SYMBOL", "BTC/USDT")
        self.binance_symbol = self._get_config_value("MM_BINANCE_SYMBOL", "BTCUSDT")
        
        # API凭证 - 优先使用通用API密钥，如果没有则使用MM专用密钥
        self.api_key = config.get("NINE_API_KEY") or config.get("MM_NINE_API_KEY")
        self.api_secret = config.get("NINE_API_SECRET") or config.get("MM_NINE_API_SECRET")
        
        # 验证API密钥
        if not self.api_key or not self.api_secret:
            self.logger.error(f"❌ API密钥获取失败 - NINE_API_KEY: {'✅' if config.get('NINE_API_KEY') else '❌'}, MM_NINE_API_KEY: {'✅' if config.get('MM_NINE_API_KEY') else '❌'}")
            raise ValueError("NINE_API_KEY 和 NINE_API_SECRET 必须在配置中设置")
            
        self.logger.info(f"🔑 API配置完成: {self.api_key[:8]}...")
        
        # 如果策略有API凭证，设置为交易对管理器的默认凭证
        if hasattr(self, 'trading_pair_manager') and self.trading_pair_manager:
            self.trading_pair_manager.set_default_credentials(self.api_key, self.api_secret)
        
        # 优化相关的缓存和配置
        self.trade_detail_cache = {}  # 缓存成交详情
        self.cache_ttl = self._get_config_value("MM_TRADE_DETAIL_CACHE_TTL", 30, int)  # 缓存30秒
        self.last_trade_detail_fetch = 0
        
        # 撤单策略配置
        self.cancel_unfilled_only = self._get_config_value("MM_CANCEL_UNFILLED_ONLY", True, bool)
        self.cancel_partial_threshold = self._get_config_value("MM_CANCEL_PARTIAL_THRESHOLD", Decimal("0.1"), Decimal)  # 成交比例阈值
        self.order_timeout_seconds = self._get_config_value("MM_ORDER_TIMEOUT_SECONDS", 300, int)  # 订单超时时间
        self.price_deviation_threshold = self._get_config_value("MM_PRICE_DEVIATION_THRESHOLD", Decimal("0.005"), Decimal)  # 价格偏离阈值5%
        
        # 订单跟踪
        self.order_creation_times = {}  # 记录订单创建时间

    def _fetch_binance_order_book(self) -> Optional[Dict[str, List[Dict[str, str]]]]:
        """
        从Binance获取指定深度的订单薄数据。
        与原 MarketMakerService._get_orders_from_binance 逻辑类似。
        """
        depth_levels = self._get_config_value("MM_BINANCE_DEPTH_LEVELS", default_value=5, expected_type=int)
        api_limit_options = [5, 10, 20, 50, 100, 500, 1000]
        chosen_api_limit = min([l for l in api_limit_options if l >= depth_levels] or [api_limit_options[-1]])


        try:
            order_book = self.binance_client.get_order_book(symbol=self.binance_symbol, limit=chosen_api_limit)
            if not (order_book and isinstance(order_book, dict) and
                    isinstance(order_book.get('bids'), list) and
                    isinstance(order_book.get('asks'), list)):
                self.logger.error(f"❌ [BINANCE] 获取 {self.binance_symbol} 订单簿失败")
                return None

            formatted_bids = [{'price': bid[0], 'quantity': bid[1]} for bid in order_book['bids'][:depth_levels]]
            formatted_asks = [{'price': ask[0], 'quantity': ask[1]} for ask in order_book['asks'][:depth_levels]]

            return {"bids": formatted_bids, "asks": formatted_asks}

        except ConnectionError as e:
            self.logger.error(f"❌ [BINANCE] 连接失败: {e}")
            return None
        except ValueError as e:
            self.logger.error(f"❌ [BINANCE] 参数错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ [BINANCE] 获取订单簿异常: {e}")
            return None

    def _should_cancel_order_by_price_deviation(self, order_data: Dict, current_binance_book: Dict) -> bool:
        """
        根据价格偏离度判断是否应该撤单
        """
        try:
            order_price = order_data.get('price', Decimal(0))
            order_direction = order_data.get('direction')
            
            if order_direction == 1:  # 买单，比较与币安最佳卖价
                binance_asks = current_binance_book.get("asks", [])
                if binance_asks:
                    binance_best_ask = Decimal(binance_asks[0]['price'])
                    spread_percentage = self._get_config_value("MM_SPREAD_PERCENTAGE", expected_type=Decimal)
                    expected_price = binance_best_ask * (Decimal('1') - spread_percentage)
                    price_deviation = abs(order_price - expected_price) / expected_price
                    return price_deviation > self.price_deviation_threshold
            elif order_direction == 2:  # 卖单，比较与币安最佳买价
                binance_bids = current_binance_book.get("bids", [])
                if binance_bids:
                    binance_best_bid = Decimal(binance_bids[0]['price'])
                    spread_percentage = self._get_config_value("MM_SPREAD_PERCENTAGE", expected_type=Decimal)
                    expected_price = binance_best_bid * (Decimal('1') + spread_percentage)
                    price_deviation = abs(order_price - expected_price) / expected_price
                    return price_deviation > self.price_deviation_threshold
        except Exception as e:
            self.logger.warning(f"⚠️ 价格偏离检查异常: {e}")
        
        return False

    def _get_cached_trade_details(self) -> Dict[str, Decimal]:
        """
        获取缓存的成交详情，如果缓存过期则重新获取
        """
        current_time = time.time()
        
        # 检查缓存是否有效
        if (current_time - self.last_trade_detail_fetch < self.cache_ttl and 
            self.trade_detail_cache):
            return self.trade_detail_cache
        
        # 重新获取成交详情
        filled_quantities = {}
        try:
            # 只获取最近的成交记录，减少API调用
            trade_pair_id = self.resolve_trading_pair_id(self.trade_pair)
            trade_details_response = self.nine_client.get_orders_trade_detail(
                trade_pair_id=trade_pair_id,
                user_api_key=self.api_key,
                user_api_secret=self.api_secret,
                page_num=1,
                page_size=100  # 减少页面大小，只获取最近的成交
            )
            
            if (trade_details_response and isinstance(trade_details_response, dict) and 
                trade_details_response.get("code") == 200):
                
                data_level1 = trade_details_response.get("data", {})
                data_level2 = data_level1.get("data", {})
                trades = data_level2.get("list", [])
                
                for trade in trades:
                    order_id = trade.get("orderId")
                    quantity_filled_str = trade.get("quantity")
                    if order_id and quantity_filled_str:
                        try:
                            quantity_filled = Decimal(quantity_filled_str)
                            filled_quantities[order_id] = filled_quantities.get(order_id, Decimal(0)) + quantity_filled
                        except DecimalInvalidOperation:
                            continue
                
                # 更新缓存
                self.trade_detail_cache = filled_quantities
                self.last_trade_detail_fetch = current_time
                
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取成交详情异常: {e}")
        
        return filled_quantities

    def _determine_orders_to_cancel(self, current_active_orders: List[Dict], current_binance_book: Optional[Dict] = None) -> List[str]:
        """
        优化后的撤单逻辑：
        1. 使用缓存减少API调用
        2. 支持多种撤单策略
        3. 考虑价格偏离和订单超时
        """
        if not current_active_orders:
            return []

        current_time = time.time()
        filled_quantities = self._get_cached_trade_details()
        order_ids_to_cancel = []
        
        for order_data_item in current_active_orders:
            order_id = order_data_item['orderId']
            original_quantity = order_data_item.get('original_quantity', Decimal(0))
            current_order_filled_qty = filled_quantities.get(order_id, Decimal(0))
            
            # 记录订单创建时间（如果还没记录）
            if order_id not in self.order_creation_times:
                self.order_creation_times[order_id] = current_time
            
            order_age = current_time - self.order_creation_times.get(order_id, current_time)
            fill_ratio = current_order_filled_qty / original_quantity if original_quantity > 0 else Decimal(0)
            
            should_cancel = False
            cancel_reason = ""
            
            # 策略1: 完全未成交订单
            if self.cancel_unfilled_only and current_order_filled_qty <= Decimal(0):
                should_cancel = True
                cancel_reason = "完全未成交"
            
            # 策略2: 部分成交但成交比例过低
            elif (not self.cancel_unfilled_only and 
                  fill_ratio > 0 and fill_ratio < self.cancel_partial_threshold):
                should_cancel = True
                cancel_reason = f"成交比例过低({fill_ratio*100:.1f}%)"
            
            # 策略3: 订单超时
            elif order_age > self.order_timeout_seconds:
                should_cancel = True
                cancel_reason = f"订单超时({order_age:.0f}秒)"
            
            # 策略4: 价格偏离过大
            elif (current_binance_book and 
                  self._should_cancel_order_by_price_deviation(order_data_item, current_binance_book)):
                should_cancel = True
                cancel_reason = "价格偏离过大"
            
            if should_cancel:
                order_ids_to_cancel.append(order_id)
                self.logger.info(f"🚫 标记撤单: {order_id} - {cancel_reason}")
        
        # 清理已撤销订单的创建时间记录
        for order_id in order_ids_to_cancel:
            self.order_creation_times.pop(order_id, None)
        
        return order_ids_to_cancel

    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict]:
        """
        实现做市策略的核心逻辑：
        1. 获取币安订单簿。
        2. 决定哪些旧订单需要取消（使用优化后的逻辑）。
        3. 根据币安订单簿和策略配置，生成新的Nine CEX订单。

        Args:
            current_active_orders: 当前活动订单列表
            nine_cex_order_book_data: 可选的 Nine CEX 订单簿数据（此策略不使用）
        """
        # 此策略不使用Nine CEX订单簿数据，仅基于Binance数据
        _ = nine_cex_order_book_data

        # 首先获取币安订单簿，因为撤单逻辑也需要用到
        binance_order_book = self._fetch_binance_order_book()
        if not binance_order_book:
            self.logger.error("❌ [BINANCE] 无法获取订单簿，仅执行撤单")
            # 即使币安订单簿获取失败，也尝试撤单（不考虑价格偏离）
            order_ids_to_cancel = self._determine_orders_to_cancel(current_active_orders, None)
            actions = []
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids_to_cancel,
                    "reason": "Binance订单簿获取失败，撤销订单"
                })
            return actions

        # 使用优化后的撤单逻辑，传入币安订单簿用于价格偏离检查
        order_ids_to_cancel = self._determine_orders_to_cancel(current_active_orders, binance_order_book)
        
        orders_to_place = []
        try:
            account_type = self._get_config_value("NINE_ACCOUNT_TYPE", expected_type=int)
            order_type_limit = self._get_config_value("NINE_ORDER_TYPE_LIMIT", expected_type=int)
            order_type_buy = order_type_limit 
            order_type_sell = order_type_limit
            direction_buy = self._get_config_value("NINE_ORDER_DIR_BUY", expected_type=int)
            direction_sell = self._get_config_value("NINE_ORDER_DIR_SELL", expected_type=int)
            spread_percentage = self._get_config_value("MM_SPREAD_PERCENTAGE", expected_type=Decimal)
            quantity_coefficient = self._get_config_value("MM_QUANTITY_COEFFICIENT", expected_type=Decimal)
            # Get precision settings for formatting
            price_precision = self._get_config_value("MM_PRICE_PRECISION", expected_type=int)
            qty_precision = self._get_config_value("MM_QTY_PRECISION", expected_type=int)
        except ValueError as e:
            self.logger.error(f"❌ 配置参数获取失败: {e}")
            actions = []
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids_to_cancel,
                    "reason": "配置参数获取失败，撤销订单"
                })
            return actions

        # 处理买单 (基于币安买盘创建 Nine CEX 买单)
        for i, binance_bid_data in enumerate(binance_order_book.get("bids", [])):
            price_str = binance_bid_data.get('price')
            quantity_str = binance_bid_data.get('quantity')

            if not price_str or not quantity_str:
                continue

            try:
                binance_price = Decimal(price_str)
                binance_quantity = Decimal(quantity_str)
            except DecimalInvalidOperation as e:
                continue

            if binance_quantity.is_zero():
                continue

            target_nine_price = binance_price * (Decimal('1') - spread_percentage)
            target_nine_quantity = binance_quantity * quantity_coefficient

            formatted_price_str = self._format_price(target_nine_price, price_precision)
            formatted_quantity_str = self._format_quantity(target_nine_quantity, qty_precision)

            if Decimal(formatted_quantity_str) <= Decimal(0):
                continue

            # 使用 create_trade_bean 方法，确保订单格式正确
            try:
                buy_order = self.create_trade_bean(
                    trading_pair=self.trade_pair,
                    order_direction=direction_buy,
                    order_quantity=formatted_quantity_str,
                    order_price=formatted_price_str,
                    account_type=account_type,
                    order_type=order_type_buy
                )
                orders_to_place.append(buy_order)
            except ValueError as e:
                self.logger.error(f"❌ [策略:{self.strategy_name}] 创建买单失败: {e}")
                continue

        # 处理卖单 (基于币安卖盘创建 Nine CEX 卖单)
        for i, binance_ask_data in enumerate(binance_order_book.get("asks", [])):
            price_str = binance_ask_data.get('price')
            quantity_str = binance_ask_data.get('quantity')

            if not price_str or not quantity_str:
                continue

            try:
                binance_price = Decimal(price_str)
                binance_quantity = Decimal(quantity_str)
            except DecimalInvalidOperation as e:
                continue

            if binance_quantity.is_zero():
                continue

            target_nine_price = binance_price * (Decimal('1') + spread_percentage)
            target_nine_quantity = binance_quantity * quantity_coefficient

            formatted_price_str = self._format_price(target_nine_price, price_precision)
            formatted_quantity_str = self._format_quantity(target_nine_quantity, qty_precision)

            if Decimal(formatted_quantity_str) <= Decimal(0):
                continue

            # 使用 create_trade_bean 方法，确保订单格式正确
            try:
                sell_order = self.create_trade_bean(
                    trading_pair=self.trade_pair,
                    order_direction=direction_sell,
                    order_quantity=formatted_quantity_str,
                    order_price=formatted_price_str,
                    account_type=account_type,
                    order_type=order_type_sell
                )
                orders_to_place.append(sell_order)
            except ValueError as e:
                self.logger.error(f"❌ [策略:{self.strategy_name}] 创建卖单失败: {e}")
                continue
        
        # Construct actions list
        actions = []
        if order_ids_to_cancel:
            actions.append({
                "action_type": "cancel",
                "order_ids": order_ids_to_cancel,
                "reason": "撤销标记取消的订单"
            })
        
        if orders_to_place:
            actions.append({
                "action_type": "place",
                "orders": orders_to_place,
                "reason": "基于币安订单簿下新单"
            })
            
        return actions # Return list of actions 