"""
增强型流动性提供策略 (EnhancedLiquidityProviderStrategy)
功能: 
1. 支持指定运行交易对和自动新交易对操作
2. 专门针对AMM token，使用固定初始价格进行上下挂单
3. 持续出货功能
4. 与Nine交易所交互
"""

import time
import json
import os
import random
from decimal import Decimal, ROUND_DOWN, ROUND_UP
from typing import List, Dict, Any, Optional, Set
from .base_strategy import BaseStrategy
from app.services.nine_client import NineClient
import logging
import asyncio
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class EnhancedLiquidityProviderStrategy(BaseStrategy):
    """
    增强型流动性提供策略：
    1. 支持两种运行模式：指定交易对 和 自动监控新交易对
    2. 专门针对AMM token，使用固定价格(0.0000161)作为基础价格
    3. 在基础价格上进行多层上下挂单（如2x价格等）
    4. 持续出货以实现盈利
    """
    
    def __init__(self, config: Dict[str, Any], nine_client: Any, logger: Optional[logging.Logger] = None):
        super().__init__(config, nine_client, logger)
        self.state = {
            'monitoring_started': False,
            'current_orders': {},
            'last_sell_time': None,
            'strategy_state': {},
            'pump_phase': 'waiting',  # waiting, preparing, pumping, dumping, completed
            'pump_start_price': None,
            'pump_target_price': None,
            'pump_phase_start_time': None,
            'original_orders_backup': {},  # 备份原始订单
            'removed_sell_orders': [],  # 已移除的卖单
            'total_token_holdings': Decimal('0'),  # 估算的代币持仓
            'dump_progress': Decimal('0'),  # 出货进度
            'last_pump_buy_time': None,  # 最后一次主动买入时间
            'pump_buy_count': 0,  # 拉盘买入次数
            'total_pump_invested': Decimal('0'),  # 拉盘总投入
            'pump_efficiency_score': Decimal('1.0'),  # 拉盘效率评分
            'initial_token_balance': Decimal('0'),  # 拉盘前的代币余额
            'pump_start_target_ratio': Decimal('0'),  # 记录开始时的目标涨幅
            'current_pump_stage': 0,  # 当前拉盘阶段
            'stage_start_time': None,  # 当前阶段开始时间
            'stage_investment_used': Decimal('0'),  # 当前阶段已用资金
            'external_volume_detected': False  # 是否检测到外部交易量
        }
        
        # 从配置加载API密钥
        self.api_key = self._get_config_value('ELP_NINE_API_KEY')
        self.api_secret = self._get_config_value('ELP_NINE_API_SECRET')
        
        if not self.api_key or not self.api_secret:
            raise ValueError("缺少必需的 API 配置: ELP_NINE_API_KEY 和 ELP_NINE_API_SECRET")
        
        # 使用传入的 Nine 客户端（从父类获取）
        # self.nine_client 已经在父类初始化中设置
        
        # 初始化价格缓存
        self._last_market_price = None
        
        # 加载所有配置
        self._load_config()
        
        logger.info(f"增强型流动性提供策略初始化完成 - 模式: {self.operating_mode}")
    
    def _load_config(self):
        """从配置加载配置"""
        # 运行模式配置
        self.operating_mode = self._get_config_value('ELP_OPERATING_MODE', 'manual').lower()
        manual_pairs_str = self._get_config_value('ELP_MANUAL_PAIRS', '')
        self.manual_pairs = manual_pairs_str.split(',') if manual_pairs_str else []
        
        # 资金配置
        self.total_usdt = Decimal(self._get_config_value('ELP_TOTAL_USDT', '3000'))
        
        # 价格策略配置
        self.base_price = Decimal(self._get_config_value('ELP_BASE_PRICE', '0.0000161'))
        price_multipliers_str = self._get_config_value('ELP_PRICE_MULTIPLIERS', '0.5,1.0,2.0')
        self.price_multipliers = [
            Decimal(x.strip()) for x in price_multipliers_str.split(',')
        ]
        
        # 订单配置
        self.orders_per_level = int(self._get_config_value('ELP_ORDERS_PER_LEVEL', '3'))
        self.total_levels = len(self.price_multipliers) * 2  # 买单 + 卖单
        self.fund_per_order = self.total_usdt / (self.total_levels * self.orders_per_level)
        
        # 卖出机制配置
        self.sell_frequency = int(self._get_config_value('ELP_SELL_FREQUENCY', '300'))  # 5分钟
        self.sell_percentage = Decimal(self._get_config_value('ELP_SELL_PERCENTAGE', '0.01'))  # 1%
        self.min_sell_amount = Decimal(self._get_config_value('ELP_MIN_SELL_AMOUNT', '100'))
        
        # 价格模式配置
        self.price_mode = self._get_config_value('ELP_PRICE_MODE', 'fixed').lower()  # fixed=固定价格模式, market=市场模式
        
        # 拉盘出货配置 - 结合订单深度控制
        self.enable_pump_dump = self._get_config_value('ELP_ENABLE_PUMP_DUMP', 'false').lower() == 'true'
        self.pump_target_ratio = Decimal(self._get_config_value('ELP_PUMP_TARGET_RATIO', '2.0'))  # 拉盘目标倍数
        self.pump_trigger_price = Decimal(self._get_config_value('ELP_PUMP_TRIGGER_PRICE', '0.0000161'))  # 拉盘触发价格
        
        # 深度控制配置
        self.pump_sell_wall_removal = Decimal(os.getenv('ELP_PUMP_SELL_WALL_REMOVAL', '0.8'))  # 拉盘时移除多少比例的卖单
        self.pump_buy_wall_boost = Decimal(os.getenv('ELP_PUMP_BUY_WALL_BOOST', '1.5'))  # 拉盘时增强买单深度的倍数
        self.dump_sell_wall_restore = Decimal(os.getenv('ELP_DUMP_SELL_WALL_RESTORE', '2.0'))  # 出货时恢复卖单的倍数
        
        # 拉盘节奏配置
        self.pump_phase_duration = int(os.getenv('ELP_PUMP_PHASE_DURATION', '300'))  # 每个拉盘阶段持续时间(秒)
        self.dump_batch_size = Decimal(os.getenv('ELP_DUMP_BATCH_SIZE', '0.1'))  # 每批出货的比例
        
        # 买入策略模式
        self.pump_buy_mode = os.getenv('ELP_PUMP_BUY_MODE', 'natural').lower()  # natural=自然模式, aggressive=激进模式, balanced=平衡模式
        
        # 智能拉盘配置 - 5x目标优化
        self.pump_max_investment = Decimal(os.getenv('ELP_PUMP_MAX_INVESTMENT', '2000'))  # 拉盘最大投入金额(USDT)
        self.pump_buy_interval = int(os.getenv('ELP_PUMP_BUY_INTERVAL', '30'))  # 主动买入间隔(秒)
        self.pump_buy_step_ratio = Decimal(os.getenv('ELP_PUMP_BUY_STEP_RATIO', '0.05'))  # 每次买入推高幅度
        self.pump_efficiency_threshold = Decimal(os.getenv('ELP_PUMP_EFFICIENCY_THRESHOLD', '0.6'))  # 拉盘效率阈值
        
        # 分阶段拉盘配置
        self.pump_stage_targets = [
            Decimal(x.strip()) for x in os.getenv('ELP_PUMP_STAGE_TARGETS', '1.5,2.5,4.0,5.5').split(',')
        ]  # 分阶段目标倍数
        self.pump_stage_pause = int(os.getenv('ELP_PUMP_STAGE_PAUSE', '60'))  # 每阶段间隔时间
        self.pump_fomo_multiplier = Decimal(os.getenv('ELP_PUMP_FOMO_MULTIPLIER', '1.5'))  # FOMO阶段资金倍数
        
        # 精度配置
        self.price_precision = int(os.getenv('ELP_PRICE_PRECISION', '8'))
        self.quantity_precision = int(os.getenv('ELP_QTY_PRECISION', '6'))
        
        # 通用 Nine CEX 配置
        self.account_type = int(os.getenv('NINE_ACCOUNT_TYPE', '1'))
        self.order_type_limit = int(os.getenv('NINE_ORDER_TYPE_LIMIT', '1'))
        self.order_dir_buy = int(os.getenv('NINE_ORDER_DIR_BUY', '1'))
        self.order_dir_sell = int(os.getenv('NINE_ORDER_DIR_SELL', '2'))
        
        logger.info(f"配置加载完成: {self.total_usdt} USDT, {len(self.price_multipliers)} 个价格等级")
    
    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        实现BaseStrategy的抽象方法，返回增强型流动性提供策略的交易操作。
        """
        try:
            # 根据运行模式选择交易对
            if self.operating_mode == 'manual':
                pairs = self.manual_pairs
                logger.info(f"手动模式: 处理指定交易对 {pairs}")
            else:
                pairs = self._discover_new_pairs()
                logger.info(f"自动模式: 发现 {len(pairs)} 个新交易对")
            
            if not pairs:
                logger.info("没有需要处理的交易对")
                return []
            
            actions = []
            
            # 为每个交易对生成订单
            for pair in pairs:
                try:
                    # 生成订单
                    orders = self._generate_orders(pair)
                    if orders:
                        actions.append({
                            "action_type": "place",
                            "reason": f"为交易对 {pair} 提供增强型流动性",
                            "orders": orders
                        })
                        
                    # 检查是否需要取消现有订单
                    cancel_orders = self._get_orders_to_cancel(current_active_orders, pair)
                    if cancel_orders:
                        actions.append({
                            "action_type": "cancel", 
                            "reason": f"取消交易对 {pair} 的过期订单",
                            "orders": cancel_orders
                        })
                        
                except Exception as e:
                    logger.error(f"处理交易对 {pair} 时发生错误: {e}")
            
            return actions
            
        except Exception as e:
            logger.error(f"增强型流动性策略执行失败: {e}")
            return []

    def _get_orders_to_cancel(self, current_active_orders: List[Dict], pair: str) -> List[str]:
        """获取需要取消的订单ID列表"""
        # 这里可以实现更复杂的取消逻辑
        # 暂时返回空列表，表示不取消任何订单
        return []

    async def execute(self) -> Dict[str, Any]:
        """执行策略"""
        try:
            # 根据运行模式选择交易对
            if self.operating_mode == 'manual':
                pairs = self.manual_pairs
                logger.info(f"手动模式: 处理指定交易对 {pairs}")
            else:
                pairs = self._discover_new_pairs()
                logger.info(f"自动模式: 发现 {len(pairs)} 个新交易对")
            
            if not pairs:
                logger.info("没有需要处理的交易对")
                return {'status': '无交易对需要处理'}
            
            results = {}
            for pair in pairs:
                try:
                    result = self._process_trading_pair(pair)
                    results[pair] = result
                except Exception as e:
                    logger.error(f"处理交易对 {pair} 时发生错误: {e}")
                    results[pair] = {'error': str(e)}
            
            # 执行拉盘出货逻辑
            pump_dump_result = None
            if self.enable_pump_dump:
                pump_dump_result = self._execute_pump_dump(pairs)
            
            # 执行定期卖出
            self._execute_periodic_selling()
            
            return {
                'status': 'success',
                'pairs_processed': len(results),
                'results': results,
                'pump_dump_result': pump_dump_result,
                'pump_phase': self.state['pump_phase'],
                'next_sell_time': self._get_next_sell_time()
            }
            
        except Exception as e:
            logger.error(f"策略执行失败: {e}")
            return {'error': str(e)}
    
    def _discover_new_pairs(self) -> List[str]:
        """发现新的交易对"""
        try:
            # 使用 get_outer_pool_info_all 获取所有外盘交易对信息
            pool_info = self.nine_client.get_outer_pool_info_all(
                user_api_key=self.api_key,
                user_api_secret=self.api_secret,
                page_size=100
            )
            
            if not pool_info:
                logger.warning("无法获取外盘交易对信息，使用默认交易对")
                return ['NINE/USDT', 'SEPBTC/USDT'][:2]
            
            logger.debug(f"外盘交易对原始数据: {pool_info}")
            
            # 解析交易对列表
            discovered_pairs = []
            
            # 根据返回的数据结构解析：data.data.list
            if isinstance(pool_info, dict) and 'list' in pool_info:
                token_list = pool_info['list']
                if isinstance(token_list, list):
                    for item in token_list:
                        if isinstance(item, dict) and 's' in item:
                            token_symbol = item['s']  # 代币符号，如 "NINE", "SEPBTC", "SEPETH"
                            
                            # 构建完整的交易对名称 (token/USDT)
                            if token_symbol and isinstance(token_symbol, str):
                                # 如果代币本身不包含USDT，则添加/USDT
                                if 'SEPUSDT' not in token_symbol.upper():
                                    pair_name = f"{token_symbol}/SEPUSDT"
                                else:
                                    pair_name = token_symbol
                                
                                discovered_pairs.append(pair_name)
                                logger.debug(f"发现代币: {token_symbol} -> 交易对: {pair_name}")
            
            # 所有生成的交易对都包含USDT
            usdt_pairs = discovered_pairs
            
            # 过滤已处理的交易对
            processed_pairs = self.state.get('processed_pairs', set())
            new_pairs = [pair for pair in usdt_pairs if pair not in processed_pairs]
            
            # 更新已处理的交易对
            if 'processed_pairs' not in self.state:
                self.state['processed_pairs'] = set()
            self.state['processed_pairs'].update(new_pairs)
            
            if new_pairs:
                logger.info(f"发现 {len(new_pairs)} 个新的交易对: {new_pairs}")
            else:
                logger.info("没有发现新的交易对，所有交易对都已处理过")
            
            return new_pairs[:3]  # 限制一次处理的数量
            
        except Exception as e:
            logger.error(f"发现新交易对失败: {e}")
    
    def _get_market_data(self, pair: str) -> Optional[Dict[str, Any]]:
        """获取市场数据（订单簿）"""
        try:
            # 解析交易对ID以支持新的API格式
            trade_pair_id = self.resolve_trading_pair_id(pair)
            
            business_data = self.nine_client.get_outer_pool_latest_order(
                trade_pair_name=pair,
                api_key=self.api_key,
                secret=self.api_secret,
                trade_pair_id=trade_pair_id,  # 新增交易对ID参数
                num=10,  # 获取前10档
                precision="0.00000001"  # 使用最高精度字符串格式
            )
            
            # 适配新的business_data格式
            order_book = None
            if business_data and isinstance(business_data, dict):
                if "orderDepth" in business_data:
                    order_book = business_data["orderDepth"]
                elif "asks" in business_data and "bids" in business_data:
                    order_book = business_data  # 向后兼容
            
            if order_book and 'asks' in order_book and 'bids' in order_book:
                # 计算最佳买卖价
                best_bid = None
                best_ask = None
                
                if order_book['bids'] and len(order_book['bids']) > 0:
                    best_bid = Decimal(str(order_book['bids'][0][0]))  # 最高买价
                
                if order_book['asks'] and len(order_book['asks']) > 0:
                    best_ask = Decimal(str(order_book['asks'][0][0]))  # 最低卖价
                
                market_data = {
                    'best_bid': best_bid,
                    'best_ask': best_ask,
                    'mid_price': (best_bid + best_ask) / 2 if best_bid and best_ask else None,
                    'spread': best_ask - best_bid if best_bid and best_ask else None,
                    'order_book': order_book
                }
                
                # 更新最近的市场价格缓存
                if market_data['mid_price']:
                    self._last_market_price = market_data['mid_price']
                    logger.debug(f"更新市场价格缓存: {market_data['mid_price']}")
                
                return market_data
            else:
                logger.warning(f"无法获取 {pair} 的有效订单簿数据")
                return None
                
        except Exception as e:
            logger.warning(f"获取 {pair} 市场数据失败: {e}")
            return None
    
    def _process_trading_pair(self, pair: str) -> Dict[str, Any]:
        """处理单个交易对"""
        try:
            # 获取订单簿信息（可选，用于优化价格）
            market_data = self._get_market_data(pair)
            
            # 撤销现有订单
            self._cancel_existing_orders(pair)
            
            # 生成新订单
            orders = self._generate_orders(pair, market_data)
            
            # 使用批量交易提交订单
            if orders:
                trade_operations = self._build_batch_trade_request(orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"批量订单已提交: {pair} - {len(orders)} 个订单")
                    # 更新状态
                    self.state['current_orders'][pair] = orders
                    
                    return {
                        'orders_placed': len(orders),
                        'total_orders_generated': len(orders),
                        'pair': pair,
                        'market_data': market_data,
                        'result': result
                    }
                else:
                    return {'error': '批量下单失败'}
            else:
                return {'error': '没有生成订单'}
            
        except Exception as e:
            logger.error(f"处理交易对 {pair} 失败: {e}")
            return {'error': str(e)}
    
    def _generate_orders(self, pair: str, market_data: Optional[Dict] = None) -> List[Dict]:
        """生成订单"""
        orders = []
        
        if self.price_mode == 'fixed':
            # 固定价格模式：开盘价 * 系数
            orders = self._generate_fixed_price_orders(pair)
            logger.info(f"{pair} 固定价格模式: 生成 {len(orders)} 个订单")
        elif self.price_mode == 'market':
            # 市场模式：在现有盘口基础上增加订单
            orders = self._generate_market_based_orders(pair, market_data)
            logger.info(f"{pair} 市场模式: 生成 {len(orders)} 个订单")
        else:
            logger.error(f"未知的价格模式: {self.price_mode}")
            return []
        
        return orders
    
    def _generate_fixed_price_orders(self, pair: str) -> List[Dict]:
        """固定价格模式：开盘价 * 系数"""
        orders = []
        
        for multiplier in self.price_multipliers:
            # 计算价格等级：开盘价 * 系数
            level_price = self.base_price * multiplier
            
            # 为每个价格等级生成多个订单
            for i in range(self.orders_per_level):
                # 买单价格（略低于等级价格）
                buy_price = level_price * (1 - Decimal('0.001') * (i + 1))
                buy_quantity = self.fund_per_order / buy_price
                
                orders.append({
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(buy_quantity)),
                    'orderPrice': str(self._format_price(buy_price)),
                    'side': 'buy',
                    'level': multiplier,
                    'layer': i + 1
                })
                
                # 卖单价格（略高于等级价格）
                sell_price = level_price * (1 + Decimal('0.001') * (i + 1))
                sell_quantity = buy_quantity  # 简化处理
                
                orders.append({
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_sell,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(sell_quantity)),
                    'orderPrice': str(self._format_price(sell_price)),
                    'side': 'sell',
                    'level': multiplier,
                    'layer': i + 1
                })
        
        return orders
    
    def _generate_market_based_orders(self, pair: str, market_data: Optional[Dict]) -> List[Dict]:
        """市场模式：在现有盘口基础上增加订单"""
        orders = []
        
        if not market_data or not market_data.get('order_book'):
            logger.warning(f"{pair} 市场模式需要订单簿数据，降级为固定价格模式")
            return self._generate_fixed_price_orders(pair)
        
        order_book = market_data['order_book']
        bids = order_book.get('bids', [])
        asks = order_book.get('asks', [])
        
        # 基于现有买盘增加买单
        for i in range(min(self.orders_per_level, len(bids))):
            if bids[i]:
                market_buy_price = Decimal(str(bids[i][0]))
                # 在市场买价基础上略低一点
                buy_price = market_buy_price * (1 - Decimal('0.0005') * (i + 1))
                buy_quantity = self.fund_per_order / buy_price
                
                orders.append({
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(buy_quantity)),
                    'orderPrice': str(self._format_price(buy_price)),
                    'side': 'buy',
                    'market_ref': market_buy_price,
                    'layer': i + 1
                })
        
        # 基于现有卖盘增加卖单
        for i in range(min(self.orders_per_level, len(asks))):
            if asks[i]:
                market_sell_price = Decimal(str(asks[i][0]))
                # 在市场卖价基础上略高一点
                sell_price = market_sell_price * (1 + Decimal('0.0005') * (i + 1))
                sell_quantity = self.fund_per_order / sell_price
                
                orders.append({
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_sell,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(sell_quantity)),
                    'orderPrice': str(self._format_price(sell_price)),
                    'side': 'sell',
                    'market_ref': market_sell_price,
                    'layer': i + 1
                })
        
        return orders
    
    def _execute_pump_dump(self, pairs: List[str]) -> Dict[str, Any]:
        """执行基于深度控制的拉盘出货逻辑"""
        try:
            if not pairs:
                return {'status': 'no_pairs'}
            
            # 选择第一个交易对进行拉盘操作
            target_pair = pairs[0]
            current_time = datetime.now()
            
            # 获取当前市场价格
            market_data = self._get_market_data(target_pair)
            if not market_data or not market_data.get('mid_price'):
                return {'status': 'no_market_data', 'pair': target_pair}
            
            current_price = market_data['mid_price']
            
            # 根据当前阶段执行不同逻辑
            if self.state['pump_phase'] == 'waiting':
                return self._check_pump_trigger(target_pair, current_price)
            elif self.state['pump_phase'] == 'preparing':
                return self._prepare_pump_environment(target_pair, current_price)
            elif self.state['pump_phase'] == 'pumping':
                return self._execute_pump_phase(target_pair, current_price, current_time)
            elif self.state['pump_phase'] == 'dumping':
                return self._execute_dump_phase(target_pair, current_price, current_time)
            elif self.state['pump_phase'] == 'cooling_down':
                return self._execute_cooling_phase(target_pair, current_time)
            else:
                return {'status': 'completed', 'phase': self.state['pump_phase']}
                
        except Exception as e:
            logger.error(f"拉盘出货执行失败: {e}")
            return {'error': str(e)}
    
    def _check_pump_trigger(self, pair: str, current_price: Decimal) -> Dict[str, Any]:
        """检查拉盘触发条件"""
        # 如果当前价格接近或低于触发价格，开始准备拉盘
        if current_price <= self.pump_trigger_price * Decimal('1.05'):  # 5% 容忍度
            self.state['pump_phase'] = 'preparing'
            self.state['pump_start_price'] = current_price
            self.state['pump_target_price'] = current_price * self.pump_target_ratio
            self.state['pump_phase_start_time'] = datetime.now().isoformat()
            
            logger.info(f"触发拉盘准备: {pair}, 当前价格: {current_price}, 目标价格: {self.state['pump_target_price']}")
            return {'status': 'pump_triggered', 'current_price': current_price}
        
        return {'status': 'waiting_for_trigger', 'current_price': current_price, 'trigger_price': self.pump_trigger_price}
    
    def _prepare_pump_environment(self, pair: str, current_price: Decimal) -> Dict[str, Any]:
        """精简策略：订单薄深度控制"""
        try:
            # 1. 建立底部托单 (1000u安全网)
            bottom_support = self._establish_bottom_support(pair, current_price)
            
            # 2. 设计阶梯式订单薄深度 (核心创新)
            depth_design = self._design_orderbook_depth(pair, current_price)
            
            # 转入拉盘阶段
            self.state['pump_phase'] = 'pumping'
            
            logger.info(f"🎨 订单薄深度控制启动: {pair}")
            logger.info(f"  📍 底部托单: {len(bottom_support)} 个")
            logger.info(f"  🎯 深度设计: {len(depth_design)} 个价位")
            
            return {
                'status': 'depth_control_ready',
                'bottom_support': len(bottom_support),
                'depth_levels': len(depth_design)
            }
            
        except Exception as e:
            logger.error(f"深度控制准备失败: {e}")
            return {'error': str(e)}
    
    def _design_orderbook_depth(self, pair: str, current_price: Decimal) -> List[Dict]:
        """设计阶梯式订单薄深度：让拉盘变得容易"""
        depth_orders = []
        
        try:
            start_price = Decimal(str(self.state['pump_start_price']))
            
            # 定义深度设计：价格倍数 -> 卖单厚度
            depth_design = [
                # 阶段1: 超薄深度 - 让100-200u就能突破
                {'price_ratio': Decimal('1.1'), 'token_amount': Decimal('50'), 'purpose': 'easy_break'},
                {'price_ratio': Decimal('1.2'), 'token_amount': Decimal('100'), 'purpose': 'easy_break'},
                {'price_ratio': Decimal('1.3'), 'token_amount': Decimal('200'), 'purpose': 'easy_break'},
                {'price_ratio': Decimal('1.4'), 'token_amount': Decimal('150'), 'purpose': 'easy_break'},
                
                # 阶段2: 正常深度 - 需要一定资金但不会太难
                {'price_ratio': Decimal('1.8'), 'token_amount': Decimal('500'), 'purpose': 'normal_resistance'},
                {'price_ratio': Decimal('2.0'), 'token_amount': Decimal('800'), 'purpose': 'normal_resistance'},
                {'price_ratio': Decimal('2.2'), 'token_amount': Decimal('600'), 'purpose': 'normal_resistance'},
                
                # 阶段3: 出货深度 - 既能出货又不崩盘
                {'price_ratio': Decimal('2.8'), 'token_amount': Decimal('1000'), 'purpose': 'dump_exit'},
                {'price_ratio': Decimal('3.2'), 'token_amount': Decimal('1200'), 'purpose': 'dump_exit'},
                {'price_ratio': Decimal('3.6'), 'token_amount': Decimal('1000'), 'purpose': 'dump_exit'},
                
                # 阶段4: 防护深度 - 防止过度拉升
                {'price_ratio': Decimal('4.5'), 'token_amount': Decimal('2000'), 'purpose': 'protection'},
                {'price_ratio': Decimal('5.0'), 'token_amount': Decimal('3000'), 'purpose': 'protection'},
                {'price_ratio': Decimal('5.5'), 'token_amount': Decimal('4000'), 'purpose': 'final_exit'},
            ]
            
            # 生成订单
            for level in depth_design:
                sell_price = start_price * level['price_ratio']
                sell_quantity = level['token_amount']
                
                sell_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_sell,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(sell_quantity)),
                    'orderPrice': str(self._format_price(sell_price)),
                    'action': 'depth_design',
                    'purpose': level['purpose'],
                    'price_ratio': str(level['price_ratio'])
                }
                
                depth_orders.append(sell_order)
            
            # 提交深度设计订单
            if depth_orders:
                trade_operations = self._build_batch_trade_request(depth_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"🎨 订单薄深度设计完成: {pair}, {len(depth_orders)} 个关键价位")
            
        except Exception as e:
            logger.error(f"设计订单薄深度失败: {e}")
        
        return depth_orders
    
    def _establish_bottom_support(self, pair: str, current_price: Decimal) -> List[Dict]:
        """第一层：建立底部托单 (1000 USDT)"""
        support_orders = []
        
        try:
            start_price = Decimal(str(self.state['pump_start_price']))
            support_budget = Decimal('1000')  # 固定1000u托底
            
            # 三档托底
            support_levels = [
                {'price': start_price * Decimal('0.85'), 'ratio': Decimal('0.5')},   # 85%位置，500u
                {'price': start_price * Decimal('0.90'), 'ratio': Decimal('0.3')},   # 90%位置，300u  
                {'price': current_price * Decimal('0.95'), 'ratio': Decimal('0.2')}   # 95%位置，200u
            ]
            
            for level in support_levels:
                support_amount = support_budget * level['ratio']
                support_quantity = support_amount / level['price']
                
                support_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(support_quantity)),
                    'orderPrice': str(self._format_price(level['price'])),
                    'action': 'bottom_support',
                    'support_level': str(level['price'])
                }
                
                support_orders.append(support_order)
            
            # 提交托底订单
            if support_orders:
                trade_operations = self._build_batch_trade_request(support_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"📍 底部托单已建立: {pair}, 投入 {support_budget} USDT")
            
        except Exception as e:
            logger.error(f"建立底部托单失败: {e}")
        
        return support_orders
    
    def _maintain_liquidity_grid(self, pair: str, current_price: Decimal) -> List[Dict]:
        """第二层：维持密集流动性网格 (高频做市)"""
        grid_orders = []
        
        try:
            # 密集流动性网格设计
            grid_config = self._design_dense_liquidity_grid()
            total_grid_budget = Decimal('400')  # 流动性网格总预算
            
            for level in grid_config:
                order_price = current_price * (1 + level['offset'])
                
                # 动态金额分配：越靠近当前价格，金额越大
                distance_factor = abs(level['offset'])
                amount_multiplier = 1 / (1 + distance_factor * 10)  # 距离越远金额越小
                level_amount = total_grid_budget * level['budget_ratio'] * amount_multiplier
                
                if level['side'] == 'buy':
                    # 买单根据拉盘状态动态增强
                    buy_enhancement = self._get_pump_buy_enhancement()
                    order_quantity = (level_amount * buy_enhancement) / order_price
                    direction = self.order_dir_buy
                else:
                    # 卖单根据拉盘状态动态调整
                    sell_adjustment = self._get_pump_sell_adjustment()
                    order_quantity = (level_amount * level['sell_ratio'] * sell_adjustment) / order_price
                    direction = self.order_dir_sell
                
                # 添加小幅随机化，避免机器人识别
                randomized_quantity = self._add_grid_randomization(order_quantity)
                
                grid_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': direction,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(randomized_quantity)),
                    'orderPrice': str(self._format_price(order_price)),
                    'action': 'dense_liquidity_grid',
                    'grid_offset': str(level['offset']),
                    'layer_type': level['layer_type']
                }
                
                grid_orders.append(grid_order)
            
            # 提交密集流动性网格
            if grid_orders:
                trade_operations = self._build_batch_trade_request(grid_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"🌊 密集流动性网格已建立: {pair}, {len(grid_orders)} 个做市单 (买:{sum(1 for o in grid_orders if o['orderDirection'] == self.order_dir_buy)}, 卖:{sum(1 for o in grid_orders if o['orderDirection'] == self.order_dir_sell)})")
            
        except Exception as e:
            logger.error(f"维持密集流动性网格失败: {e}")
        
        return grid_orders
    
    def _design_dense_liquidity_grid(self) -> List[Dict]:
        """设计密集流动性网格配置"""
        grid_config = []
        
        # 核心流动性区域 (±0.5% 以内) - 最密集
        core_offsets = [
            Decimal('-0.005'), Decimal('-0.003'), Decimal('-0.001'),  # 买单
            Decimal('0.001'), Decimal('0.003'), Decimal('0.005')      # 卖单
        ]
        
        for offset in core_offsets:
            grid_config.append({
                'offset': offset,
                'side': 'buy' if offset < 0 else 'sell',
                'budget_ratio': Decimal('0.25'),  # 核心区域占25%预算
                'sell_ratio': Decimal('0.2'),     # 卖单只放20%
                'layer_type': 'core'
            })
        
        # 紧密流动性区域 (0.5%-1.5%) - 中等密集
        tight_offsets = [
            Decimal('-0.015'), Decimal('-0.01'), Decimal('-0.008'),   # 买单
            Decimal('0.008'), Decimal('0.01'), Decimal('0.015')       # 卖单
        ]
        
        for offset in tight_offsets:
            grid_config.append({
                'offset': offset,
                'side': 'buy' if offset < 0 else 'sell',
                'budget_ratio': Decimal('0.15'),  # 紧密区域占15%预算
                'sell_ratio': Decimal('0.25'),    # 卖单25%
                'layer_type': 'tight'
            })
        
        # 标准流动性区域 (1.5%-3%) - 标准间隔
        standard_offsets = [
            Decimal('-0.03'), Decimal('-0.025'), Decimal('-0.02'),    # 买单
            Decimal('0.02'), Decimal('0.025'), Decimal('0.03')        # 卖单
        ]
        
        for offset in standard_offsets:
            grid_config.append({
                'offset': offset,
                'side': 'buy' if offset < 0 else 'sell',
                'budget_ratio': Decimal('0.08'),  # 标准区域占8%预算
                'sell_ratio': Decimal('0.3'),     # 卖单30%
                'layer_type': 'standard'
            })
        
        # 外围流动性区域 (3%-5%) - 稀疏覆盖
        outer_offsets = [
            Decimal('-0.05'), Decimal('-0.04'),                       # 买单
            Decimal('0.04'), Decimal('0.05')                          # 卖单
        ]
        
        for offset in outer_offsets:
            grid_config.append({
                'offset': offset,
                'side': 'buy' if offset < 0 else 'sell',
                'budget_ratio': Decimal('0.05'),  # 外围区域占5%预算
                'sell_ratio': Decimal('0.4'),     # 卖单40%
                'layer_type': 'outer'
            })
        
        return grid_config
    
    def _add_grid_randomization(self, base_quantity: Decimal) -> Decimal:
        """为网格订单添加随机化，避免机器人识别"""
        import random
        
        # ±3%的随机变化
        randomization_factor = Decimal(str(random.uniform(0.97, 1.03)))
        randomized_quantity = base_quantity * randomization_factor
        
        return randomized_quantity
    
    def _get_pump_sell_adjustment(self) -> Decimal:
        """根据拉盘状态动态调整卖单比例"""
        pump_phase = self.state.get('pump_phase', 'waiting')
        
        if pump_phase == 'waiting':
            return Decimal('1.0')  # 正常比例
        elif pump_phase == 'preparing':
            return Decimal('0.5')  # 准备期减少50%卖单
        elif pump_phase == 'pumping':
            # 拉盘期根据涨幅动态调整
            current_price = self._get_current_estimated_price()
            start_price = Decimal(str(self.state.get('pump_start_price', current_price)))
            
            if current_price and start_price:
                gain = (current_price / start_price) - 1
                if gain < Decimal('0.5'):    # 涨幅<50%，大幅减少卖单
                    return Decimal('0.2')
                elif gain < Decimal('1.0'):  # 涨幅<100%，中度减少
                    return Decimal('0.4')
                else:                        # 涨幅>100%，允许部分卖单
                    return Decimal('0.6')
            return Decimal('0.3')  # 默认拉盘期减少70%
        elif pump_phase == 'dumping':
            return Decimal('2.0')  # 出货期增加100%卖单
        elif pump_phase == 'cooling_down':
            # 降温期：根据降温阶段调整卖单
            cooling_sell_ratio = self.state.get('cooling_sell_ratio', Decimal('1.0'))
            return cooling_sell_ratio
        else:
            return Decimal('1.0')  # 默认正常比例
    
    def _get_current_estimated_price(self) -> Optional[Decimal]:
        """获取当前估算价格（多种方式综合）"""
        try:
            # 方法1: 从状态中获取最近的价格信息
            if hasattr(self, '_last_market_price') and self._last_market_price:
                return self._last_market_price
            
            # 方法2: 使用拉盘开始价格 + 估算涨幅
            if self.state.get('pump_start_price'):
                start_price = Decimal(str(self.state['pump_start_price']))
                pump_buy_count = self.state.get('pump_buy_count', 0)
                
                # 基于买入次数估算价格涨幅
                estimated_gain = pump_buy_count * self.pump_buy_step_ratio * Decimal('0.8')  # 80%效率估算
                estimated_price = start_price * (1 + estimated_gain)
                
                # 缓存估算价格
                self._last_market_price = estimated_price
                return estimated_price
            
            # 方法3: 使用基础价格作为兜底
            if hasattr(self, 'base_price') and self.base_price:
                return self.base_price
            
            return None
            
        except Exception as e:
            logger.debug(f"获取当前价格失败: {e}")
            return None
    
    def _get_pump_buy_enhancement(self) -> Decimal:
        """根据拉盘状态动态增强买单"""
        pump_phase = self.state.get('pump_phase', 'waiting')
        
        if pump_phase == 'waiting':
            return Decimal('1.0')  # 正常比例
        elif pump_phase == 'preparing':
            return Decimal('1.5')  # 准备期增强50%买单
        elif pump_phase == 'pumping':
            # 拉盘期大幅增强买单支撑
            return Decimal('2.0')  # 增强100%
        elif pump_phase == 'dumping':
            # 出货期保持强买单支撑，确保出货顺利
            return Decimal('1.8')  # 增强80%
        elif pump_phase == 'cooling_down':
            # 降温期：根据降温阶段逐步减少买单
            cooling_buy_ratio = self.state.get('cooling_buy_ratio', Decimal('1.0'))
            return cooling_buy_ratio
        else:
            return Decimal('1.0')  # 默认正常比例
    
    def _create_pump_channel(self, pair: str, current_price: Decimal) -> List[Dict]:
        """第三层：创建拉盘通道 (平衡出货与买单支撑)"""
        channel_actions = []
        
        try:
            target_price = Decimal(str(self.state['pump_target_price']))
            
            # 重新设计出货位：确保买单能承受卖出压力
            dump_positions = [
                {'price_ratio': Decimal('1.3'), 'dump_ratio': Decimal('0.05'), 'support_buffer': Decimal('200')},  # 1.3x: 5%出货+200u支撑
                {'price_ratio': Decimal('1.8'), 'dump_ratio': Decimal('0.08'), 'support_buffer': Decimal('300')},  # 1.8x: 8%出货+300u支撑
                {'price_ratio': Decimal('2.5'), 'dump_ratio': Decimal('0.12'), 'support_buffer': Decimal('400')},  # 2.5x: 12%出货+400u支撑
                {'price_ratio': Decimal('3.5'), 'dump_ratio': Decimal('0.15'), 'support_buffer': Decimal('500')},  # 3.5x: 15%出货+500u支撑
                {'price_ratio': Decimal('4.5'), 'dump_ratio': Decimal('0.10'), 'support_buffer': Decimal('300')},  # 4.5x: 10%出货+300u支撑
            ]
            
            start_price = Decimal(str(self.state['pump_start_price']))
            estimated_tokens = self.state['total_token_holdings']
            
            # 为每个出货位预设支撑买单，确保出货时不砸盘
            for position in dump_positions:
                dump_price = start_price * position['price_ratio']
                dump_quantity = estimated_tokens * position['dump_ratio']
                support_budget = position['support_buffer']
                
                # 1. 先建立支撑买单（在出货价格下方）
                support_orders = self._create_dump_support_orders(
                    pair, dump_price, support_budget
                )
                channel_actions.extend(support_orders)
                
                # 2. 然后设置小量出货单（确保有买单承接）
                small_orders = 5  # 增加到5个小单，减少单笔冲击
                quantity_per_order = dump_quantity / small_orders
                
                for i in range(small_orders):
                    adjusted_price = dump_price * (1 + Decimal('0.002') * i)  # 价格微调缩小到0.2%
                    
                    dump_order = {
                        'accountType': self.account_type,
                        'tradePairName': pair,
                        'orderDirection': self.order_dir_sell,
                        'orderType': self.order_type_limit,
                        'orderQuantity': str(self._format_quantity(quantity_per_order)),
                        'orderPrice': str(self._format_price(adjusted_price)),
                        'action': 'pump_channel_dump',
                        'dump_stage': str(position['price_ratio']),
                        'order_layer': i + 1,
                        'support_budget': str(support_budget)
                    }
                    
                    channel_actions.append(dump_order)
            
            # 提交通道出货单
            if channel_actions:
                trade_operations = self._build_batch_trade_request(channel_actions, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"🚀 拉盘通道已建立: {pair}, {len(channel_actions)} 个出货位")
            
        except Exception as e:
            logger.error(f"创建拉盘通道失败: {e}")
        
        return channel_actions
    
    def _create_dump_support_orders(self, pair: str, dump_price: Decimal, support_budget: Decimal) -> List[Dict]:
        """为出货位创建支撑买单，确保出货时不砸盘"""
        support_orders = []
        
        try:
            # 在出货价格下方建立分层买单支撑
            support_levels = [
                {'offset': Decimal('-0.02'), 'ratio': Decimal('0.4')},  # 出货价-2%，40%资金
                {'offset': Decimal('-0.01'), 'ratio': Decimal('0.35')}, # 出货价-1%，35%资金  
                {'offset': Decimal('-0.005'), 'ratio': Decimal('0.25')} # 出货价-0.5%，25%资金
            ]
            
            for level in support_levels:
                support_price = dump_price * (1 + level['offset'])
                support_amount = support_budget * level['ratio']
                support_quantity = support_amount / support_price
                
                support_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(support_quantity)),
                    'orderPrice': str(self._format_price(support_price)),
                    'action': 'dump_support',
                    'target_dump_price': str(dump_price),
                    'support_offset': str(level['offset'])
                }
                
                support_orders.append(support_order)
            
            logger.debug(f"为出货价 {dump_price} 创建支撑: {support_budget} USDT, {len(support_orders)} 层支撑")
            
        except Exception as e:
            logger.error(f"创建出货支撑失败: {e}")
        
        return support_orders
    
    def _aggressive_sell_wall_removal(self, pair: str, current_price: Decimal) -> List[Dict]:
        """优雅的卖单重构 - 迁移而非删除"""
        migration_actions = []
        
        try:
            # 核心策略：卖单向上迁移，而不是删除
            target_price = Decimal(str(self.state['pump_target_price']))
            
            # 定义迁移映射：原价位 -> 新价位
            migration_map = self._calculate_sell_order_migration(current_price, target_price)
            
            # 执行卖单迁移
            migrated_orders = []
            for original_price, new_price_info in migration_map.items():
                migration_result = self._migrate_sell_orders(
                    pair, original_price, new_price_info
                )
                migrated_orders.extend(migration_result)
            
            # 提交迁移后的新卖单
            if migrated_orders:
                trade_operations = self._build_batch_trade_request(migrated_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"🎨 卖单优雅迁移完成: {pair}, 迁移 {len(migrated_orders)} 个卖单")
                    migration_actions.append({
                        'action': 'elegant_migration',
                        'orders_migrated': len(migrated_orders),
                        'visual_effect': '保持盘面深度美感'
                    })
            
        except Exception as e:
            logger.error(f"卖单迁移失败: {e}")
        
        return migration_actions
    
    def _calculate_sell_order_migration(self, current_price: Decimal, target_price: Decimal) -> Dict:
        """计算卖单迁移映射表"""
        migration_map = {}
        
        try:
            # 定义原始阻力位 (需要清理的区域)
            resistance_levels = [
                current_price * Decimal('1.1'),   # +10%
                current_price * Decimal('1.2'),   # +20%
                current_price * Decimal('1.3'),   # +30%
                current_price * Decimal('1.5'),   # +50%
            ]
            
            # 定义新的卖单放置位置 (高位获利区)
            new_placement_levels = [
                target_price * Decimal('1.1'),    # 目标价+10%
                target_price * Decimal('1.3'),    # 目标价+30%
                target_price * Decimal('1.5'),    # 目标价+50%
                target_price * Decimal('1.8'),    # 目标价+80%
            ]
            
            # 建立迁移映射
            for i, original_price in enumerate(resistance_levels):
                if i < len(new_placement_levels):
                    migration_map[original_price] = {
                        'new_price': new_placement_levels[i],
                        'quantity_ratio': Decimal('0.6'),  # 只迁移60%，其余20%删除，20%保留营造自然感
                        'split_orders': 2,  # 分成2个订单，增加深度层次
                        'price_spread': Decimal('0.01')  # 1%的价格分散
                    }
            
        except Exception as e:
            logger.error(f"计算迁移映射失败: {e}")
        
        return migration_map
    
    def _migrate_sell_orders(self, pair: str, original_price: Decimal, new_price_info: Dict) -> List[Dict]:
        """执行单个价位的卖单迁移"""
        migrated_orders = []
        
        try:
            new_price = new_price_info['new_price']
            quantity_ratio = new_price_info['quantity_ratio']
            split_orders = new_price_info['split_orders']
            price_spread = new_price_info['price_spread']
            
            # 计算原始订单的总量 (简化估算)
            original_total_quantity = self.fund_per_order / original_price
            migrated_quantity = original_total_quantity * quantity_ratio
            
            # 分拆成多个订单增加深度美感
            quantity_per_order = migrated_quantity / split_orders
            
            for i in range(split_orders):
                # 价格微调，制造自然分布
                adjusted_price = new_price * (1 + price_spread * i)
                
                migrated_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_sell,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(quantity_per_order)),
                    'orderPrice': str(self._format_price(adjusted_price)),
                    'action': 'elegant_migration',
                    'original_price': str(original_price),
                    'migration_layer': i + 1
                }
                
                migrated_orders.append(migrated_order)
            
            logger.debug(f"迁移订单: {original_price} -> {new_price}, 数量: {migrated_quantity}, 分拆: {split_orders}")
            
        except Exception as e:
            logger.error(f"执行订单迁移失败: {e}")
        
        return migrated_orders
    
    def _create_critical_support(self, pair: str, current_price: Decimal) -> List[Dict]:
        """创建关键支撑 - 最小化资金占用"""
        support_orders = []
        
        try:
            # 只在关键价位设置少量强支撑
            start_price = Decimal(str(self.state['pump_start_price']))
            critical_levels = [
                start_price * Decimal('0.9'),   # 起始价格的90% - 防止大幅回调
                current_price * Decimal('0.95')  # 当前价格的95% - 防止小幅回调
            ]
            
            for support_price in critical_levels:
                # 只设置一个大额支撑订单，而不是多个小订单
                support_amount = self.pump_max_investment * Decimal('0.1')  # 用最大投入的10%做支撑
                support_quantity = support_amount / support_price
                
                support_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(support_quantity)),
                    'orderPrice': str(self._format_price(support_price)),
                    'action': 'critical_support'
                }
                
                support_orders.append(support_order)
            
            # 提交关键支撑订单
            if support_orders:
                trade_operations = self._build_batch_trade_request(support_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"关键支撑已设置: {pair}, {len(support_orders)} 个关键支撑点")
            
        except Exception as e:
            logger.error(f"创建关键支撑失败: {e}")
        
        return support_orders
    
    def _boost_buy_walls(self, pair: str, current_price: Decimal) -> List[Dict]:
        """增强买单深度制造支撑"""
        boosted_orders = []
        
        try:
            # 在当前价格下方增加买单支撑
            support_levels = [Decimal('0.95'), Decimal('0.9'), Decimal('0.85')]  # 5%, 10%, 15% 支撑位
            
            for level in support_levels:
                support_price = current_price * level
                
                # 每个支撑位增加多个买单
                for i in range(int(self.orders_per_level * self.pump_buy_wall_boost)):
                    buy_price = support_price * (1 - Decimal('0.001') * i)
                    buy_quantity = (self.fund_per_order * self.pump_buy_wall_boost) / buy_price
                    
                    buy_order = {
                        'accountType': self.account_type,
                        'tradePairName': pair,
                        'orderDirection': self.order_dir_buy,
                        'orderType': self.order_type_limit,
                        'orderQuantity': str(self._format_quantity(buy_quantity)),
                        'orderPrice': str(self._format_price(buy_price)),
                        'action': 'support_boost'
                    }
                    
                    boosted_orders.append(buy_order)
            
            # 提交增强买单
            if boosted_orders:
                trade_operations = self._build_batch_trade_request(boosted_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"买单支撑增强完成: {pair}, 增加 {len(boosted_orders)} 个买单")
                else:
                    logger.warning(f"买单支撑增强失败: {pair}")
            
        except Exception as e:
            logger.error(f"增强买单支撑失败: {e}")
        
        return boosted_orders
    
    def _estimate_token_holdings(self, pair: str, current_price: Decimal):
        """估算代币持仓"""
        try:
            # 基于历史订单和当前价格估算持仓
            # 简化处理：假设我们有一定的代币余额用于出货
            estimated_holdings = self.total_usdt * Decimal('0.3') / current_price  # 假设30%资金已转换为代币
            self.state['total_token_holdings'] = estimated_holdings
            
            logger.info(f"估算代币持仓: {pair}, {estimated_holdings} 代币")
            
        except Exception as e:
            logger.error(f"估算代币持仓失败: {e}")
    
    def _execute_pump_phase(self, pair: str, current_price: Decimal, current_time: datetime) -> Dict[str, Any]:
        """精简拉盘：利用深度控制优势"""
        try:
            start_price = Decimal(str(self.state['pump_start_price']))
            current_gain = (current_price / start_price) - 1
            
            # 检查是否达到5x目标
            if current_gain >= Decimal('4.0'):
                logger.info(f"🎉 5x目标达成！涨幅: {current_gain * 100:.1f}%")
                return {'status': 'target_achieved', 'gain': current_gain}
            
            # 简单推进：小额买入利用薄深度
            return self._execute_lightweight_pump(pair, current_price, start_price)
            
        except Exception as e:
            logger.error(f"拉盘执行失败: {e}")
            return {'error': str(e)}
    
    def _execute_lightweight_pump(self, pair: str, current_price: Decimal, start_price: Decimal) -> Dict[str, Any]:
        """轻量级拉盘：利用薄深度优势"""
        try:
            # 检查买入间隔（添加随机性）
            current_time = datetime.now()
            last_buy_time = self.state.get('last_pump_buy_time')
            if last_buy_time:
                time_passed = (current_time - datetime.fromisoformat(last_buy_time)).total_seconds()
                # 随机间隔：基础间隔±50%
                import random
                min_interval = self.pump_buy_interval * random.uniform(0.5, 1.5)
                if time_passed < min_interval:
                    return {'status': 'waiting_interval'}
            
            # 检查投入限制
            if self.state['total_pump_invested'] >= self.pump_max_investment:
                return {'status': 'investment_limit_reached'}
            
            # 根据模式计算买入金额
            buy_amount = self._calculate_buy_amount_by_mode(current_price, start_price)
            buy_price = current_price * (1 + self.pump_buy_step_ratio)
            buy_quantity = buy_amount / buy_price
            
            # 提交买单
            buy_order = {
                'accountType': self.account_type,
                'tradePairName': pair,
                'orderDirection': self.order_dir_buy,
                'orderType': self.order_type_limit,
                'orderQuantity': str(self._format_quantity(buy_quantity)),
                'orderPrice': str(self._format_price(buy_price)),
                'action': 'lightweight_pump'
            }
            
            trade_operations = self._build_batch_trade_request([buy_order], pair)
            result = self.nine_client.batch_trade(trade_operations)
            
            if result:
                # 更新状态
                self.state['last_pump_buy_time'] = current_time.isoformat()
                self.state['pump_buy_count'] += 1
                self.state['total_pump_invested'] += buy_amount
                
                current_gain = (current_price / start_price) - 1
                
                logger.info(f"💰 {self.pump_buy_mode.upper()}拉盘: {buy_amount}u推高至{buy_price}, 涨幅: {current_gain * 100:.1f}%, 第{self.state['pump_buy_count']}次买入")
                
                return {
                    'status': 'pump_executed',
                    'amount': str(buy_amount),
                    'price': str(buy_price),
                    'gain': str(current_gain * 100) + '%',
                    'total_invested': str(self.state['total_pump_invested'])
                }
            else:
                return {'status': 'pump_failed'}
                
        except Exception as e:
            logger.error(f"轻量拉盘失败: {e}")
            return {'error': str(e)}
    
    def _calculate_natural_buy_amount(self, current_price: Decimal, start_price: Decimal) -> Decimal:
        """计算自然化的买入金额，模拟真实用户行为"""
        import random
        
        # 根据涨幅调整买入金额范围
        gain = (current_price / start_price) - 1
        
        if gain < Decimal('0.2'):  # 涨幅小于20%，较大买入
            base_amount = random.uniform(40, 80)
        elif gain < Decimal('0.5'):  # 涨幅20%-50%，中等买入  
            base_amount = random.uniform(30, 60)
        elif gain < Decimal('1.0'):  # 涨幅50%-100%，小额买入
            base_amount = random.uniform(20, 50)
        else:  # 涨幅超过100%，很小额买入
            base_amount = random.uniform(15, 35)
        
        # 添加买入次数的影响（越买越小，模拟资金递减）
        buy_count = self.state.get('pump_buy_count', 0)
        if buy_count > 10:
            base_amount *= 0.8  # 买入超过10次后减少金额
        elif buy_count > 20:
            base_amount *= 0.6  # 买入超过20次后大幅减少
            
        # 添加随机波动（±20%）
        variation = random.uniform(0.8, 1.2)
        final_amount = base_amount * variation
        
        return Decimal(str(round(final_amount, 2)))
    
    def _calculate_buy_amount_by_mode(self, current_price: Decimal, start_price: Decimal) -> Decimal:
        """根据模式计算买入金额"""
        if self.pump_buy_mode == 'aggressive':
            return self._calculate_aggressive_buy_amount(current_price, start_price)
        elif self.pump_buy_mode == 'balanced':
            return self._calculate_balanced_buy_amount(current_price, start_price)
        else:  # natural
            return self._calculate_natural_buy_amount(current_price, start_price)
    
    def _calculate_aggressive_buy_amount(self, current_price: Decimal, start_price: Decimal) -> Decimal:
        """激进模式：固定金额，追求最快拉盘"""
        # 根据拉盘进度动态调整
        gain = (current_price / start_price) - 1
        
        if gain < Decimal('1.0'):  # 前期加大投入
            return Decimal('80')
        elif gain < Decimal('2.0'):  # 中期维持
            return Decimal('60')
        elif gain < Decimal('3.0'):  # 后期减少
            return Decimal('40')
        else:  # 最后冲刺
            return Decimal('30')
    
    def _calculate_balanced_buy_amount(self, current_price: Decimal, start_price: Decimal) -> Decimal:
        """平衡模式：固定+少量随机，兼顾效率和隐蔽"""
        import random
        
        gain = (current_price / start_price) - 1
        
        if gain < Decimal('0.5'):
            base = 65
        elif gain < Decimal('1.0'):
            base = 50
        elif gain < Decimal('2.0'):
            base = 40
        else:
            base = 30
        
        # 只添加±15%的小幅随机
        variation = random.uniform(0.85, 1.15)
        return Decimal(str(round(base * variation, 2)))
    
    def _execute_staged_pump(self, pair: str, current_price: Decimal, current_time: datetime, start_price: Decimal) -> Dict[str, Any]:
        """执行分阶段拉盘策略"""
        try:
            current_stage = self.state['current_pump_stage']
            current_gain = (current_price / start_price) - 1
            
            # 检查是否需要进入下一阶段
            if current_stage < len(self.pump_stage_targets):
                stage_target = self.pump_stage_targets[current_stage] - 1  # 转换为涨幅比例
                
                if current_gain >= stage_target:
                    # 进入下一阶段
                    self.state['current_pump_stage'] += 1
                    self.state['stage_start_time'] = current_time.isoformat()
                    self.state['stage_investment_used'] = Decimal('0')
                    
                    current_stage += 1
                    logger.info(f"🚀 进入拉盘第{current_stage}阶段，当前涨幅: {current_gain * 100:.1f}%")
                    
                    # 阶段间暂停，制造期待感
                    if current_stage < len(self.pump_stage_targets):
                        return {'status': 'stage_transition', 'new_stage': current_stage, 'current_gain': current_gain}
            
            # 执行当前阶段的拉盘操作
            if current_stage < len(self.pump_stage_targets):
                return self._execute_stage_pump(pair, current_price, current_time, start_price, current_stage)
            else:
                # 所有阶段完成，进入出货
                self.state['pump_phase'] = 'dumping'
                return self._start_dump_phase(pair, current_price)
                
        except Exception as e:
            logger.error(f"分阶段拉盘执行失败: {e}")
            return {'error': str(e)}
    
    def _execute_stage_pump(self, pair: str, current_price: Decimal, current_time: datetime, 
                           start_price: Decimal, stage: int) -> Dict[str, Any]:
        """执行具体阶段的拉盘"""
        try:
            stage_target_gain = self.pump_stage_targets[stage] - 1
            current_gain = (current_price / start_price) - 1
            
            # 检测外部交易量
            external_volume = self._detect_external_volume(pair, current_price)
            if external_volume:
                self.state['external_volume_detected'] = True
                logger.info(f"💡 检测到外部买盘！FOMO效应启动")
            
            # 计算阶段资金分配
            stage_budget = self._calculate_stage_budget(stage, external_volume)
            
            # 检查阶段超时或预算用完
            stage_start = datetime.fromisoformat(self.state['stage_start_time'])
            stage_duration = (current_time - stage_start).total_seconds()
            
            if (self.state['stage_investment_used'] >= stage_budget or 
                stage_duration > self.pump_stage_pause * 2):
                # 强制进入下一阶段
                self.state['current_pump_stage'] += 1
                logger.info(f"阶段{stage}资金用尽或超时，强制进入下一阶段")
                return {'status': 'stage_forced_transition', 'stage': stage}
            
            # 执行当前阶段的买入
            return self._execute_stage_buy(pair, current_price, stage_target_gain, stage_budget, external_volume)
            
        except Exception as e:
            logger.error(f"执行阶段{stage}拉盘失败: {e}")
            return {'error': str(e)}
    
    def _detect_external_volume(self, pair: str, current_price: Decimal) -> bool:
        """检测外部交易量 - 简化实现"""
        # 简化检测：如果价格涨幅超出我们的预期推进速度，说明有外部买盘
        try:
            expected_progress = self.state['pump_buy_count'] * self.pump_buy_step_ratio
            start_price = Decimal(str(self.state['pump_start_price']))
            actual_progress = (current_price / start_price) - 1
            
            # 如果实际涨幅比预期快50%以上，认为有外部买盘
            return actual_progress > expected_progress * Decimal('1.5')
            
        except:
            return False
    
    def _calculate_stage_budget(self, stage: int, has_external_volume: bool) -> Decimal:
        """计算阶段预算"""
        # 基础预算：总投入除以阶段数
        base_budget = self.pump_max_investment / len(self.pump_stage_targets)
        
        # 早期阶段用更多资金建立势头
        if stage == 0:
            stage_multiplier = Decimal('1.5')  # 第一阶段多用50%
        elif stage == 1:
            stage_multiplier = Decimal('1.2')  # 第二阶段多用20%
        else:
            stage_multiplier = Decimal('0.8')  # 后期阶段依赖FOMO
        
        # 如果检测到外部买盘，减少投入
        if has_external_volume:
            stage_multiplier *= Decimal('0.7')
            
        return base_budget * stage_multiplier
    
    def _execute_stage_buy(self, pair: str, current_price: Decimal, target_gain: Decimal, 
                          budget: Decimal, has_external_volume: bool) -> Dict[str, Any]:
        """执行阶段性买入"""
        try:
            # 计算买入金额
            remaining_budget = budget - self.state['stage_investment_used']
            if remaining_budget <= 0:
                return {'status': 'stage_budget_exhausted'}
            
            # 基于目标和外部情况调整买入强度
            buy_intensity = Decimal('0.1')  # 基础强度10%
            if has_external_volume:
                buy_intensity *= self.pump_fomo_multiplier  # FOMO阶段加强
            
            buy_amount = min(remaining_budget * buy_intensity, remaining_budget)
            buy_price = current_price * (1 + self.pump_buy_step_ratio)
            buy_quantity = buy_amount / buy_price
            
            # 提交买单
            active_buy_order = {
                'accountType': self.account_type,
                'tradePairName': pair,
                'orderDirection': self.order_dir_buy,
                'orderType': self.order_type_limit,
                'orderQuantity': str(self._format_quantity(buy_quantity)),
                'orderPrice': str(self._format_price(buy_price)),
                'action': f'stage_{self.state["current_pump_stage"]}_pump'
            }
            
            trade_operations = self._build_batch_trade_request([active_buy_order], pair)
            result = self.nine_client.batch_trade(trade_operations)
            
            if result:
                # 更新状态
                self.state['last_pump_buy_time'] = datetime.now().isoformat()
                self.state['pump_buy_count'] += 1
                self.state['total_pump_invested'] += buy_amount
                self.state['stage_investment_used'] += buy_amount
                
                start_price = Decimal(str(self.state['pump_start_price']))
                current_gain = (current_price / start_price) - 1
                
                logger.info(f"🔥 阶段{self.state['current_pump_stage']}买入: {buy_amount} USDT, "
                          f"当前涨幅: {current_gain * 100:.1f}%, 外部买盘: {has_external_volume}")
                
                return {
                    'status': 'stage_buy_executed',
                    'stage': self.state['current_pump_stage'],
                    'amount': str(buy_amount),
                    'current_gain': str(current_gain * 100) + '%',
                    'external_volume': has_external_volume,
                    'remaining_budget': str(budget - self.state['stage_investment_used'])
                }
            else:
                return {'status': 'stage_buy_failed'}
                
        except Exception as e:
            logger.error(f"阶段买入失败: {e}")
            return {'error': str(e)}
    
    def _adjust_pump_depth(self, pair: str, current_price: Decimal, target_price: Decimal) -> Dict[str, Any]:
        """动态调整订单深度 + 主动买入推高价格"""
        try:
            current_time = datetime.now()
            actions_taken = []
            
            # 1. 继续移除更多卖单，减少阻力
            additional_removal = self._progressive_sell_wall_removal(pair, current_price)
            if additional_removal > 0:
                actions_taken.append(f"移除{additional_removal}个卖单")
            
            # 2. 检查是否需要主动买入推高价格
            last_buy_time = self.state.get('last_pump_buy_time')
            if (not last_buy_time or 
                (current_time - datetime.fromisoformat(last_buy_time)).total_seconds() >= self.pump_buy_interval):
                
                # 执行主动买入推高价格
                buy_result = self._execute_active_pump_buy(pair, current_price, target_price)
                if buy_result.get('success'):
                    actions_taken.append(f"主动买入推高: {buy_result.get('price')}")
            
            # 3. 在更高价位增加限价买单支撑
            support_orders = self._create_pump_support_orders(pair, current_price)
            if support_orders:
                trade_operations = self._build_batch_trade_request(support_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                if result:
                    actions_taken.append(f"增加{len(support_orders)}个支撑单")
            
            progress_pct = (current_price / target_price) * 100
            
            logger.info(f"拉盘推进: {pair}, 当前价格 {current_price}, 目标 {target_price}, 进度 {progress_pct:.1f}%, 动作: {actions_taken}")
            
            return {
                'status': 'pumping_progress',
                'current_price': current_price,
                'target_price': target_price,
                'progress': progress_pct,
                'actions': actions_taken,
                'buy_count': self.state['pump_buy_count'],
                'total_invested': str(self.state['total_pump_invested'])
            }
            
        except Exception as e:
            logger.error(f"调整拉盘深度失败: {e}")
            return {'error': str(e)}
    
    def _progressive_sell_wall_removal(self, pair: str, current_price: Decimal) -> int:
        """渐进式移除卖单墙"""
        # 继续移除当前价格以上的卖单
        # 实际实现中这里会调用撤单API
        return int(self.orders_per_level * 0.2)  # 每次移除20%
    
    def _execute_active_pump_buy(self, pair: str, current_price: Decimal, target_price: Decimal) -> Dict[str, Any]:
        """智能主动买入 - 考虑成本效益"""
        try:
            # 检查投入上限
            if self.state['total_pump_invested'] >= self.pump_max_investment:
                logger.info(f"已达到拉盘投入上限: {self.pump_max_investment} USDT")
                return {'success': False, 'reason': 'investment_limit_reached'}
            
            # 检查拉盘效率
            if self.state['pump_efficiency_score'] < self.pump_efficiency_threshold:
                logger.info(f"拉盘效率过低: {self.state['pump_efficiency_score']}, 暂停买入")
                return {'success': False, 'reason': 'low_efficiency'}
            
            # 动态计算买入金额 - 基于剩余目标和效率
            remaining_gain_needed = (target_price / current_price) - 1
            base_amount = min(
                self.pump_max_investment / 10,  # 最大投入的1/10
                remaining_gain_needed * 500     # 基于剩余涨幅动态调整
            )
            
            # 效率调整
            buy_amount = base_amount * self.state['pump_efficiency_score']
            buy_price = current_price * (1 + self.pump_buy_step_ratio)
            buy_quantity = buy_amount / buy_price
            
            # 构造买单
            active_buy_order = {
                'accountType': self.account_type,
                'tradePairName': pair,
                'orderDirection': self.order_dir_buy,
                'orderType': self.order_type_limit,
                'orderQuantity': str(self._format_quantity(buy_quantity)),
                'orderPrice': str(self._format_price(buy_price)),
                'action': 'smart_pump_buy'
            }
            
            # 提交买单
            trade_operations = self._build_batch_trade_request([active_buy_order], pair)
            result = self.nine_client.batch_trade(trade_operations)
            
            if result:
                # 更新状态
                self.state['last_pump_buy_time'] = datetime.now().isoformat()
                self.state['pump_buy_count'] += 1
                self.state['total_pump_invested'] += buy_amount
                
                # 评估拉盘效率 - 关键优化
                self._evaluate_pump_efficiency(current_price, buy_amount)
                
                logger.info(f"智能拉盘买入: {pair}, 价格: {buy_price}, 金额: {buy_amount}, 效率: {self.state['pump_efficiency_score']:.2f}")
                
                return {
                    'success': True,
                    'price': buy_price,
                    'quantity': buy_quantity,
                    'amount': buy_amount,
                    'efficiency': str(self.state['pump_efficiency_score']),
                    'remaining_budget': str(self.pump_max_investment - self.state['total_pump_invested'])
                }
            else:
                return {'success': False, 'error': '买单提交失败'}
                
        except Exception as e:
            logger.error(f"智能拉盘买入失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _evaluate_pump_efficiency(self, current_price: Decimal, invested_amount: Decimal):
        """评估拉盘效率 - 防止资金浪费"""
        try:
            start_price = Decimal(str(self.state['pump_start_price']))
            price_gain = (current_price / start_price) - 1
            
            if self.state['total_pump_invested'] > 0:
                # 效率 = 价格涨幅 / 投入资金比例
                investment_ratio = self.state['total_pump_invested'] / self.pump_max_investment
                efficiency = price_gain / investment_ratio if investment_ratio > 0 else Decimal('0')
                
                # 更新效率评分 (平滑更新)
                self.state['pump_efficiency_score'] = (self.state['pump_efficiency_score'] * Decimal('0.7') + 
                                                     efficiency * Decimal('0.3'))
                
                logger.debug(f"拉盘效率评估: 价格涨幅 {price_gain:.3f}, 投入比例 {investment_ratio:.3f}, 效率 {efficiency:.3f}")
                
        except Exception as e:
            logger.error(f"评估拉盘效率失败: {e}")
    
    def _create_pump_support_orders(self, pair: str, current_price: Decimal) -> List[Dict]:
        """创建支撑价格的限价买单"""
        support_orders = []
        
        try:
            # 在当前价格下方建立支撑买单
            support_levels = [Decimal('0.98'), Decimal('0.95'), Decimal('0.92')]  # 2%, 5%, 8% 支撑位
            
            for level in support_levels:
                support_price = current_price * level
                support_amount = self.fund_per_order * Decimal('0.5')  # 每个支撑订单金额
                support_quantity = support_amount / support_price
                
                support_order = {
                    'accountType': self.account_type,
                    'tradePairName': pair,
                    'orderDirection': self.order_dir_buy,
                    'orderType': self.order_type_limit,
                    'orderQuantity': str(self._format_quantity(support_quantity)),
                    'orderPrice': str(self._format_price(support_price)),
                    'action': 'pump_support'
                }
                
                support_orders.append(support_order)
            
        except Exception as e:
            logger.error(f"创建支撑订单失败: {e}")
        
        return support_orders
    
    def _start_dump_phase(self, pair: str, current_price: Decimal) -> Dict[str, Any]:
        """开始出货阶段"""
        try:
            # 恢复卖单墙，但价格更高
            self._restore_enhanced_sell_walls(pair, current_price)
            
            # 开始第一批出货
            return self._execute_batch_dump(pair, current_price)
            
        except Exception as e:
            logger.error(f"开始出货阶段失败: {e}")
            return {'error': str(e)}
    
    def _execute_dump_phase(self, pair: str, current_price: Decimal, current_time: datetime) -> Dict[str, Any]:
        """执行出货阶段：分批出货"""
        try:
            # 检查出货进度
            if self.state['dump_progress'] >= Decimal('1.0'):  # 100% 出货完成
                self._restore_normal_operations(pair)
                self.state['pump_phase'] = 'completed'
                logger.info(f"出货完成: {pair}")
                return {'status': 'dump_completed', 'pair': pair}
            
            # 执行分批出货
            return self._execute_batch_dump(pair, current_price)
            
        except Exception as e:
            logger.error(f"出货阶段执行失败: {e}")
            return {'error': str(e)}
    
    def _restore_enhanced_sell_walls(self, pair: str, current_price: Decimal):
        """恢复增强的卖单墙"""
        try:
            sell_orders = []
            
            # 在当前价格以上建立更强的卖单墙
            price_levels = [Decimal('1.02'), Decimal('1.05'), Decimal('1.1'), Decimal('1.15')]  # 2%, 5%, 10%, 15%
            
            for level in price_levels:
                sell_price = current_price * level
                
                # 每个价格层级多个卖单
                for i in range(int(self.orders_per_level * self.dump_sell_wall_restore)):
                    adjusted_price = sell_price * (1 + Decimal('0.001') * i)
                    sell_quantity = (self.fund_per_order * self.dump_sell_wall_restore) / adjusted_price
                    
                    sell_order = {
                        'accountType': self.account_type,
                        'tradePairName': pair,
                        'orderDirection': self.order_dir_sell,
                        'orderType': self.order_type_limit,
                        'orderQuantity': str(self._format_quantity(sell_quantity)),
                        'orderPrice': str(self._format_price(adjusted_price)),
                        'action': 'dump_wall'
                    }
                    
                    sell_orders.append(sell_order)
            
            # 提交增强卖单墙
            if sell_orders:
                trade_operations = self._build_batch_trade_request(sell_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    logger.info(f"增强卖单墙已建立: {pair}, {len(sell_orders)} 个卖单")
                
        except Exception as e:
            logger.error(f"恢复增强卖单墙失败: {e}")
    
    def _execute_batch_dump(self, pair: str, current_price: Decimal) -> Dict[str, Any]:
        """执行分批出货"""
        try:
            # 计算本批次出货数量
            total_holdings = self.state['total_token_holdings']
            batch_quantity = total_holdings * self.dump_batch_size
            
            # 分多个价格出货，避免冲击市场
            dump_orders = []
            dump_prices = [
                current_price * Decimal('0.995'),  # -0.5%
                current_price * Decimal('0.99'),   # -1%
                current_price * Decimal('0.985')   # -1.5%
            ]
            
            quantity_per_order = batch_quantity / len(dump_prices)
            
            for dump_price in dump_prices:
                if quantity_per_order > 0:
                    dump_order = {
                        'accountType': self.account_type,
                        'tradePairName': pair,
                        'orderDirection': self.order_dir_sell,
                        'orderType': self.order_type_limit,
                        'orderQuantity': str(self._format_quantity(quantity_per_order)),
                        'orderPrice': str(self._format_price(dump_price)),
                        'action': 'batch_dump'
                    }
                    
                    dump_orders.append(dump_order)
            
            # 提交出货订单
            if dump_orders:
                trade_operations = self._build_batch_trade_request(dump_orders, pair)
                result = self.nine_client.batch_trade(trade_operations)
                
                if result:
                    # 更新出货进度
                    self.state['dump_progress'] += self.dump_batch_size
                    
                    logger.info(f"分批出货执行: {pair}, 数量: {batch_quantity}, 进度: {self.state['dump_progress'] * 100:.1f}%")
                    
                    return {
                        'status': 'batch_dump_executed',
                        'quantity': str(batch_quantity),
                        'progress': str(self.state['dump_progress'] * 100),
                        'orders_count': len(dump_orders)
                    }
                else:
                    return {'status': 'batch_dump_failed'}
            
            return {'status': 'no_dump_orders'}
            
        except Exception as e:
            logger.error(f"分批出货失败: {e}")
            return {'error': str(e)}
    
    def _restore_normal_operations(self, pair: str):
        """智能恢复正常做市操作 - 分阶段降温"""
        try:
            logger.info(f"🌡️ 开始出货后降温程序: {pair}")
            
            # 第一步：进入降温期，逐步减少买单支撑
            self.state['pump_phase'] = 'cooling_down'
            self.state['cooling_start_time'] = datetime.now().isoformat()
            self.state['cooling_stage'] = 0  # 降温阶段：0=高温，1=中温，2=低温，3=正常
            
            # 执行第一阶段降温
            self._execute_cooling_stage(pair, stage=0)
            
            logger.info(f"✅ 降温程序启动: {pair}")
            
        except Exception as e:
            logger.error(f"启动降温程序失败: {e}")
    
    def _execute_cooling_stage(self, pair: str, stage: int):
        """执行分阶段降温"""
        try:
            cooling_configs = [
                {'buy_ratio': Decimal('1.2'), 'sell_ratio': Decimal('1.5'), 'duration': 60, 'name': '高温降温'},   # 60秒
                {'buy_ratio': Decimal('0.9'), 'sell_ratio': Decimal('1.2'), 'duration': 90, 'name': '中温降温'},   # 90秒
                {'buy_ratio': Decimal('0.7'), 'sell_ratio': Decimal('1.0'), 'duration': 120, 'name': '低温降温'},  # 120秒
                {'buy_ratio': Decimal('1.0'), 'sell_ratio': Decimal('1.0'), 'duration': 0, 'name': '完全恢复'}     # 恢复正常
            ]
            
            if stage >= len(cooling_configs):
                # 完全恢复正常
                self._complete_cooling_process(pair)
                return
            
            config = cooling_configs[stage]
            
            # 设置降温阶段参数
            self.state['cooling_stage'] = stage
            self.state['cooling_buy_ratio'] = config['buy_ratio']
            self.state['cooling_sell_ratio'] = config['sell_ratio']
            self.state['cooling_stage_start'] = datetime.now().isoformat()
            
            logger.info(f"🌡️ {config['name']}阶段: {pair}, 买单{config['buy_ratio']}x, 卖单{config['sell_ratio']}x, 持续{config['duration']}秒")
            
        except Exception as e:
            logger.error(f"执行降温阶段失败: {e}")
    
    def _complete_cooling_process(self, pair: str):
        """完成降温过程，完全恢复正常"""
        try:
            # 清理所有拉盘出货状态
            self.state['pump_phase'] = 'waiting'
            self.state['pump_start_price'] = None
            self.state['pump_target_price'] = None
            self.state['dump_progress'] = Decimal('0')
            self.state['original_orders_backup'] = {}
            self.state['removed_sell_orders'] = []
            
            # 清理降温状态
            self.state['cooling_start_time'] = None
            self.state['cooling_stage'] = None
            self.state['cooling_buy_ratio'] = None
            self.state['cooling_sell_ratio'] = None
            self.state['cooling_stage_start'] = None
            
            # 重置其他拉盘相关状态
            self.state['pump_buy_count'] = 0
            self.state['total_pump_invested'] = Decimal('0')
            self.state['pump_efficiency_score'] = Decimal('1.0')
            
            logger.info(f"🎉 降温完成，已完全恢复正常做市: {pair}")
            
        except Exception as e:
            logger.error(f"完成降温过程失败: {e}")
    
    def _execute_cooling_phase(self, pair: str, current_time: datetime) -> Dict[str, Any]:
        """执行降温阶段逻辑"""
        try:
            current_stage = self.state.get('cooling_stage', 0)
            stage_start = self.state.get('cooling_stage_start')
            
            if not stage_start:
                logger.warning("降温阶段开始时间未找到，重新启动降温")
                self._execute_cooling_stage(pair, current_stage)
                return {'status': 'cooling_restarted', 'stage': current_stage}
            
            # 计算当前阶段已持续时间
            stage_start_time = datetime.fromisoformat(stage_start)
            stage_duration = (current_time - stage_start_time).total_seconds()
            
            # 降温阶段配置
            cooling_durations = [60, 90, 120, 0]  # 对应各阶段持续时间
            
            if current_stage < len(cooling_durations) - 1:  # 还未到最后阶段
                stage_target_duration = cooling_durations[current_stage]
                
                if stage_duration >= stage_target_duration:
                    # 进入下一阶段
                    next_stage = current_stage + 1
                    logger.info(f"🌡️ 降温进入下一阶段: {current_stage} → {next_stage}")
                    self._execute_cooling_stage(pair, next_stage)
                    
                    return {
                        'status': 'cooling_stage_advanced',
                        'from_stage': current_stage,
                        'to_stage': next_stage,
                        'stage_duration': stage_duration
                    }
                else:
                    # 继续当前阶段
                    remaining_time = stage_target_duration - stage_duration
                    return {
                        'status': 'cooling_in_progress',
                        'stage': current_stage,
                        'remaining_time': remaining_time,
                        'buy_ratio': str(self.state.get('cooling_buy_ratio', '1.0')),
                        'sell_ratio': str(self.state.get('cooling_sell_ratio', '1.0'))
                    }
            else:
                # 已到最后阶段，完成降温
                self._complete_cooling_process(pair)
                return {'status': 'cooling_completed', 'pair': pair}
                
        except Exception as e:
            logger.error(f"执行降温阶段失败: {e}")
            return {'error': str(e)}

    
    def _build_batch_trade_request(self, orders: List[Dict], pair: str) -> List[Dict]:
        """构建批量交易请求"""
        trade_operations = [{
            "userBean": {
                "apiKey": self.api_key,
                "secret": self.api_secret
            },
            "tradeBean": []
        }]
        
        for order in orders:
            trade_detail = {
                "accountType": order['accountType'],
                "tradePairName": order['tradePairName'], 
                "orderDirection": order['orderDirection'],
                "orderType": order['orderType'],
                "orderQuantity": order['orderQuantity'],
                "orderPrice": order['orderPrice']
            }
            trade_operations[0]["tradeBean"].append(trade_detail)
        
        return trade_operations
    
    def _cancel_existing_orders(self, pair: str):
        """撤销现有订单"""
        try:
            # 使用批量撤单接口撤销所有订单
            result = self.nine_client.cancel_all_orders(
                user_api_key=self.api_key,
                user_api_secret=self.api_secret
            )
            
            if result:
                logger.info(f"已撤销 {pair} 的所有现有订单")
            
            # 清空当前订单记录
            if pair in self.state['current_orders']:
                del self.state['current_orders'][pair]
                
        except Exception as e:
            logger.error(f"撤销现有订单失败: {e}")
    
    def _execute_periodic_selling(self):
        """执行定期卖出"""
        try:
            now = datetime.now()
            last_sell = self.state.get('last_sell_time')
            
            if (not last_sell or 
                (now - datetime.fromisoformat(last_sell)).total_seconds() >= self.sell_frequency):
                
                logger.info("定期卖出时间到达，需要手动配置代币余额进行卖出")
                
                # 更新最后卖出时间
                self.state['last_sell_time'] = now.isoformat()
                
        except Exception as e:
            logger.error(f"执行定期卖出失败: {e}")
    
    def _get_next_sell_time(self) -> Optional[str]:
        """获取下次卖出时间"""
        last_sell = self.state.get('last_sell_time')
        if last_sell:
            next_sell = datetime.fromisoformat(last_sell) + timedelta(seconds=self.sell_frequency)
            return next_sell.isoformat()
        return None
    
    def _format_price(self, price: Decimal) -> Decimal:
        """格式化价格精度"""
        return price.quantize(Decimal('0.' + '0' * self.price_precision), rounding=ROUND_DOWN)
    
    def _format_quantity(self, quantity: Decimal) -> Decimal:
        """格式化数量精度"""
        return quantity.quantize(Decimal('0.' + '0' * self.quantity_precision), rounding=ROUND_DOWN)
    
    def save_state(self, filepath: str):
        """保存策略状态"""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.state, f, indent=2, default=str)
            logger.info(f"策略状态已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存策略状态失败: {e}")
    
    def load_state(self, filepath: str):
        """加载策略状态"""
        try:
            with open(filepath, 'r') as f:
                self.state = json.load(f)
            logger.info(f"策略状态已从 {filepath} 加载")
        except FileNotFoundError:
            logger.info("状态文件不存在，使用默认状态")
        except Exception as e:
            logger.error(f"加载策略状态失败: {e}")

    def get_status_report(self) -> Dict[str, Any]:
        """获取策略状态报告"""
        report = {
            'strategy_name': self.strategy_name,
            'strategy_mode': self.operating_mode,
            'price_mode': self.price_mode,
            'active_pairs': len(self.state['current_orders']),
            'total_usdt': str(self.total_usdt),
            'base_price': str(self.base_price),
            'price_multipliers': [str(m) for m in self.price_multipliers],
            'total_sold': {k: str(v) for k, v in self.state['current_orders'].items()},
            'last_updated': time.time()
        }
        
        # 添加拉盘出货状态
        if self.enable_pump_dump:
            report['pump_dump'] = {
                'enabled': True,
                'phase': self.state['pump_phase'],
                'start_price': str(self.state['pump_start_price']) if self.state['pump_start_price'] else None,
                'target_price': str(self.state['pump_target_price']) if self.state['pump_target_price'] else None,
                'trigger_price': str(self.pump_trigger_price),
                'target_ratio': str(self.pump_target_ratio),
                'token_holdings': str(self.state['total_token_holdings']),
                'dump_progress': str(self.state['dump_progress'] * 100) + '%' if self.state['dump_progress'] > 0 else '0%',
                'removed_sell_orders': len(self.state['removed_sell_orders']),
                'pump_buy_count': self.state['pump_buy_count'],
                'total_pump_invested': str(self.state['total_pump_invested'])
            }
        else:
            report['pump_dump'] = {'enabled': False}
            
        return report 