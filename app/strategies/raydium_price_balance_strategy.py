"""
Raydium价格平衡策略

保持CEX价格与Raydium DEX链上价格同步，通过做市提供流动性
"""

from typing import List, Dict, Optional, NamedTuple
import logging
from decimal import Decimal
from dataclasses import dataclass
from app.strategies.base_strategy import BaseStrategy
from app.services.raydium_client import RaydiumClient


class TradingConstants:
    """交易常量类 - 统一管理硬编码数值"""
    
    # 数量相关常量
    MIN_REQUIRED_QTY = Decimal("1")  # 最小订单数量要求
    CEX_MIN_ORDER_AMOUNT = Decimal("0.1")  # CEX最小订单金额
    
    # 价格限制常量
    PRICE_LIMIT_FACTOR = Decimal("9.9")  # 990%价格限制（符合Nine系统限制）
    BUFFER_FACTOR = Decimal("0.95")  # 缓冲因子
    BUFFER_FACTOR_UP = Decimal("1.05")  # 向上缓冲
    
    # 价格调整常量
    MAX_STEP_RATIO = Decimal("5.0")  # 保守策略，最多500%推动（避免Nine动态基准问题）
    AGGRESSIVE_PUSH_FACTOR = Decimal("2.5")  # 250%突破力
    AGGRESSIVE_PUMP_FACTOR = Decimal("4.0")  # 400%拉升力  
    MIN_PUSH_FACTOR = Decimal("1.8")  # 180%基础力
    
    # 价差相关常量
    AGGRESSIVE_THRESHOLD = Decimal("1.0")  # 激进调整阈值(100%偏差)
    PRICE_ADJUSTMENT_THRESHOLD = Decimal("0.05")  # 价格调整阈值(5%偏差)
    
    # 支撑相关常量
    SUPPORT_SPREAD_5PCT = Decimal("0.05")  # 5%支撑价差
    SUPPORT_SPREAD_10PCT = Decimal("0.10")  # 10%支撑价差
    
    # 订单创建相关常量
    PRICE_ADJUSTMENT_DOWN = Decimal("0.99")  # 下调价格因子(1%下调)
    PRICE_ADJUSTMENT_UP = Decimal("1.01")  # 上调价格因子(1%上调)
    
    # 流动性相关常量
    MIN_ORDERS_PER_SIDE = 3  # 每边最少订单数


@dataclass
class PriceState:
    """价格状态管理"""
    last_trade_price: Optional[Decimal]  # 最新成交价
    mid_price: Optional[Decimal]         # 订单簿中间价
    target_price: Decimal                # 目标价格
    reference_price: Decimal             # 参考价格
    
    def get_limit_base(self) -> Optional[Decimal]:
        """990%限制基准价格（适配Nine系统）"""
        return self.last_trade_price
    
    def get_spread_base(self) -> Decimal:
        """价差计算基准价格"""
        return self.reference_price
    
    def is_market_empty(self) -> bool:
        """市场是否为空"""
        return self.last_trade_price is None and self.mid_price is None


class ValidationResult(NamedTuple):
    """验证结果"""
    valid: bool
    reason: str = ""


class OrderValidator:
    """订单价格验证器"""
    
    def __init__(self, price_state: PriceState):
        self.price_state = price_state
        self.limit_base = price_state.get_limit_base()
    
    def validate_order_price(self, price: Decimal) -> ValidationResult:
        """验证订单价格950%限制（适配Nine系统1000%限制）"""
        if not self.limit_base:
            return ValidationResult(valid=True, reason="无基准价格 - 市场初始化模式")
        
        # 智能价差检测
        price_ratio = price / self.limit_base if self.limit_base > 0 else Decimal("1")
        
        # 极端价差：使用Nine系统限制
        if price_ratio > 10 or price_ratio < Decimal("0.1"):
            nine_max_price = self.limit_base * Decimal("9.9")
            nine_min_price = self.limit_base / Decimal("9.9")
            
            if nine_min_price <= price <= nine_max_price:
                return ValidationResult(valid=True, reason="Nine限制内")
            else:
                try:
                    reason = f"超出Nine限制[${nine_min_price:.8f}, ${nine_max_price:.8f}]"
                except Exception as e:
                    reason = f"验证失败: {e}"
                return ValidationResult(valid=False, reason=reason)
        
        # 标准验证逻辑
        min_price = self.limit_base / TradingConstants.PRICE_LIMIT_FACTOR
        max_price = self.limit_base * TradingConstants.PRICE_LIMIT_FACTOR
        
        if min_price <= price <= max_price:
            return ValidationResult(valid=True)
        else:
            try:
                reason = f"价格${price:.8f}超出限制[${min_price:.8f}, ${max_price:.8f}] (基准:${self.limit_base:.8f})"
            except Exception as e:
                reason = f"价格验证失败: price={price}, min_price={min_price}, max_price={max_price}, limit_base={self.limit_base}, error={e}"
            
            return ValidationResult(
                valid=False, 
                reason=reason
            )


class RaydiumPriceBalanceStrategy(BaseStrategy):
    """Raydium价格平衡策略"""
    
    def __init__(self, config: dict, nine_client, logger: Optional[logging.Logger] = None):
        # API凭证 - 必须在super().__init__()之前设置
        self.config = config  # 临时设置，供_get_config_value使用
        # 优先使用通用API密钥，如果没有则使用RPB专用密钥
        self.api_key = config.get("NINE_API_KEY") or config.get("RPB_NINE_API_KEY")
        self.api_secret = config.get("NINE_API_SECRET") or config.get("RPB_NINE_API_SECRET")
        
        super().__init__(config, nine_client, logger)
        
        # 验证API密钥
        if not self.api_key or not self.api_secret:
            self.logger.error(f"❌ API密钥获取失败 - NINE_API_KEY: {'✅' if config.get('NINE_API_KEY') else '❌'}, RPB_NINE_API_KEY: {'✅' if config.get('RPB_NINE_API_KEY') else '❌'}")
            raise ValueError("NINE_API_KEY 和 NINE_API_SECRET 必须在配置中设置")
            
        self.logger.info(f"🔑 API配置完成: {self.api_key[:8]}...")
        
        # 初始化Raydium客户端
        self.raydium_client = RaydiumClient()
        
        # 从配置中获取参数
        self.trading_pair = self._get_config_value("RPB_TRADING_PAIR", expected_type=str)  # 如 "SEPBTC/SEPUSDT"
        
        # 从 trading_pair 解析出 base 和 quote 符号
        if "/" in self.trading_pair:
            self.base_symbol, self.quote_symbol = self.trading_pair.split("/")
        else:
            self.logger.error(f"❌ 交易对格式错误，需要包含'/'分隔符: {self.trading_pair}")
            raise ValueError(f"Invalid trading pair format: {self.trading_pair}")
        # 合约地址（可选）
        self.contract_address = self._get_config_value("RPB_CONTRACT_ADDRESS", default_value=None, expected_type=str)
        
        # 精度配置（从环境变量）
        self.price_precision = self._get_config_value("RPB_PRICE_PRECISION", 8, int)
        self.qty_precision = self._get_config_value("RPB_QTY_PRECISION", 2, int)
        
        # 订单金额配置
        self.base_order_amount = self._get_config_value("RPB_BASE_ORDER_AMOUNT", Decimal("100"), expected_type=Decimal)
        self.min_order_amount = self._get_config_value("RPB_MIN_ORDER_AMOUNT", Decimal("0.1"), expected_type=Decimal)
        self.max_order_amount = self._get_config_value("RPB_MAX_ORDER_AMOUNT", Decimal("1000"), expected_type=Decimal)
        
        # 价格策略参数
        self.liquidity_levels = self._get_config_value("RPB_LIQUIDITY_LEVELS", 5, expected_type=int)
        self.liquidity_amount_per_level = self._get_config_value("RPB_LIQUIDITY_AMOUNT_PER_LEVEL", Decimal("5"), expected_type=Decimal)
        
        # CEX价格偏差设置（相对于DEX）
        # -0.05 = CEX比DEX低5%（吸引套利者从CEX买入）
        # 0.05 = CEX比DEX高5%（吸引套利者向CEX卖出）
        self.target_price_offset = self._get_config_value("RPB_TARGET_PRICE_OFFSET", Decimal("0"), expected_type=Decimal)
        
        # 价格容差配置
        self.price_tolerance = self._get_config_value("RPB_PRICE_TOLERANCE", Decimal("0.002"), expected_type=Decimal)  # 0.2%容差
        self.aggressive_threshold = self._get_config_value("RPB_AGGRESSIVE_THRESHOLD", Decimal("0.01"), expected_type=Decimal)  # 1%激进阈值
        
        # 添加配置常量
        self.price_limit_factor = self._get_config_value("RPB_PRICE_LIMIT_FACTOR", Decimal("2.9"), Decimal)  # 价格限制因子(300%限制内)
        self.progressive_pump_threshold = self._get_config_value("RPB_PROGRESSIVE_PUMP_THRESHOLD", Decimal("0.5"), Decimal)  # 渐进式拉盘阈值
        
        # 解析数组配置
        initial_spreads_str = self._get_config_value("RPB_INITIAL_SPREADS", "0.001,0.002,0.005,0.01,0.02")
        self.initial_spreads = [Decimal(s.strip()) for s in initial_spreads_str.split(',')]
        
        level_multipliers_str = self._get_config_value("RPB_LEVEL_MULTIPLIERS", "1.0,1.2,1.5,2.0,3.0")
        self.level_multipliers = [Decimal(m.strip()) for m in level_multipliers_str.split(',')]
        
        pump_step_factors_str = self._get_config_value("RPB_PUMP_STEP_FACTORS", "1.5,2.0,2.3,2.6,2.9")
        self.pump_step_factors = [Decimal(f.strip()) for f in pump_step_factors_str.split(',')]
        
        # 小额交易金额（用于建立成交价）
        self.small_trade_amount = self._get_config_value("RPB_SMALL_TRADE_AMOUNT", Decimal("1"), Decimal)  # 1 USDT
        self.medium_trade_amount = self._get_config_value("RPB_MEDIUM_TRADE_AMOUNT", Decimal("5"), Decimal)  # 5 USDT
        
        # 分步推动策略配置
        self.max_step_multiplier = self._get_config_value("RPB_MAX_STEP_MULTIPLIER", Decimal("8.0"), Decimal)  # 8倍步长
        self.max_push_steps = self._get_config_value("RPB_MAX_PUSH_STEPS", 10, int)  # 最大10步
        
        
        self.logger.info(f"策略启动: {self.trading_pair}")
        
        # 查询交易对余额（可选功能，不影响策略运行）
        self._log_trading_pair_balance()
        
        # 订单簿获取配置 - 为 MarketMakerService 提供
        self.fetch_nine_book_symbol = self.trading_pair
        # 使用最高精度确保价格信息准确，基于RPB_PRICE_PRECISION配置
        precision_decimals = 10 ** -self.price_precision  # 8位精度 -> 0.00000001
        self.fetch_nine_book_precision = f"{precision_decimals:.8f}"
        self.fetch_nine_book_depth = 10
        
        # 首次运行标记 - 用于启动时撤销所有订单
        self.is_first_run = True
        
        # 设置策略更新间隔
        self.update_interval = self._get_config_value("RPB_UPDATE_INTERVAL", 30, int)
        
    
    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict]:
        """获取策略动作"""
        actions = []
        
        try:
            # 首次运行清理逻辑（一次性执行）
            first_run_action = self._handle_first_run_cleanup(current_active_orders)
            if first_run_action:
                return first_run_action
            
            # 计算策略状态
            strategy_state = self._calculate_strategy_state(nine_cex_order_book_data, current_active_orders)
            if not strategy_state:
                return actions  # 策略状态计算失败，返回空动作
            
            chain_price, price_state, asks, bids = strategy_state
            
            # 处理空市场
            if price_state.is_market_empty():
                # 完全空的市场，需要初始化
                self.logger.info("📭 市场为空，开始初始化")
                init_actions = self._initialize_empty_market(chain_price, current_active_orders)
                actions.extend(init_actions)
                return actions
            
            # FIFO订单清理：保留最新的，清理最旧的
            if current_active_orders:
                order_count = len(current_active_orders)
                max_orders = self._get_config_value("RPB_MAX_ORDERS", 30, int)
                keep_orders = self._get_config_value("RPB_KEEP_ORDERS", 20, int)
                
                if order_count > max_orders:
                    # 使用订单ID作为时间代理（ID通常按时间递增）
                    sorted_orders = sorted(current_active_orders, 
                                         key=lambda x: x.get('orderId', ''))
                    
                    # 保留最新的keep_orders个，清理最旧的
                    orders_to_cancel = sorted_orders[:order_count - keep_orders]
                    
                    if orders_to_cancel:
                        cancel_ids = [o.get('orderId') for o in orders_to_cancel]
                        self.logger.info(f"📊 FIFO清理: 保留最新{keep_orders}个，撤销最旧{len(cancel_ids)}个")
                        
                        actions.append({
                            "action_type": "cancel",
                            "reason": "FIFO清理",
                            "order_ids": cancel_ids
                        })
                        # 重要：继续执行下面的逻辑创建新订单，避免流动性断层
            
            # 执行核心交易逻辑
            trading_orders = self._execute_trading_logic(price_state, asks, bids, chain_price)
            if trading_orders:
                actions.extend(trading_orders)
            
                
        except Exception as e:
            self.logger.error(f"❌ 策略执行异常: {e}")
        
        return actions
    
    # 价格状态管理
    
    def _calculate_price_state(self, asks: List, bids: List, quote_info: Dict, chain_price: Decimal) -> PriceState:
        """
        基于已解析的订单簿数据计算价格状态 - 性能优化版本
        避免重复解析订单簿数据
        
        Args:
            asks: 已解析的卖单列表
            bids: 已解析的买单列表  
            quote_info: 已解析的报价信息
            chain_price: 链上价格
            
        Returns:
            PriceState: 价格状态对象
        """
        # 从已解析的quote_info提取最新成交价
        last_trade_price = None
        if quote_info and isinstance(quote_info, dict):
            latest_price_str = quote_info.get("n")
            if latest_price_str and latest_price_str != "0" and latest_price_str != "0.00000000":
                try:
                    last_trade_price = Decimal(str(latest_price_str))
                except (ValueError, TypeError):
                    pass
        
        # 从已解析的订单簿计算中间价
        mid_price = None
        if asks and bids:
            try:
                best_ask = Decimal(str(asks[0][0]))
                best_bid = Decimal(str(bids[0][0]))
                if best_ask > 0 and best_bid > 0:
                    mid_price = (best_ask + best_bid) / Decimal("2")
            except (ValueError, TypeError, IndexError, Exception):
                self.logger.warning(f"⚠️ 计算中间价失败: asks[0]={asks[0][0] if asks else 'N/A'}, bids[0]={bids[0][0] if bids else 'N/A'}")
                pass
        
        # 计算目标价格（链上价格 + 配置的偏差）
        target_price = chain_price * (Decimal("1") + self.target_price_offset)
        
        # 确定参考价格
        reference_price = last_trade_price or mid_price or target_price
        
        # 验证价格数据完整性
        price_state = PriceState(
            last_trade_price=last_trade_price,
            mid_price=mid_price,
            reference_price=reference_price,
            target_price=target_price,
        )
        
        if not self._validate_price_data_integrity(price_state):
            self.logger.warning("⚠️ 价格数据存在问题，但继续执行")
        
        return price_state
    
    def _extract_last_trade_price(self, order_book_data: Optional[Dict]) -> Optional[Decimal]:
        """提取最新成交价 - 简化版本"""
        try:
            if not order_book_data:
                return None
            
            _, _, quote_info = self._parse_order_book(order_book_data)
            
            if quote_info and "n" in quote_info:
                latest_price_str = quote_info.get("n")
                if latest_price_str and latest_price_str != "0":
                    price = Decimal(str(latest_price_str))
                    if price > 0:
                        return price
            
            return None
            
        except Exception as e:
            self._handle_price_extraction_error(e, "成交价", "订单簿数据")
            return None
    
    def _calculate_mid_price(self, order_book_data: Optional[Dict]) -> Optional[Decimal]:
        """计算订单簿中间价 - 简化版本"""
        try:
            if not order_book_data:
                return None
            
            asks, bids, _ = self._parse_order_book(order_book_data)
            
            if not asks and not bids:
                return None
            
            # 安全地转换价格，预防无效数据
            best_ask = None
            best_bid = None
            
            if asks:
                try:
                    best_ask = Decimal(str(asks[0][0]))
                except (ValueError, TypeError, IndexError):
                    self.logger.warning(f"⚠️ 无效的ask价格: {asks[0][0] if asks else 'N/A'}")
                    
            if bids:
                try:
                    best_bid = Decimal(str(bids[0][0]))
                except (ValueError, TypeError, IndexError):
                    self.logger.warning(f"⚠️ 无效的bid价格: {bids[0][0] if bids else 'N/A'}")
            
            if best_ask and best_bid:
                return (best_ask + best_bid) / 2
            
            return best_ask or best_bid
            
        except Exception as e:
            self._handle_price_extraction_error(e, "中间价", "订单簿数据")
            return None
    
    def _create_validated_order(self, trading_pair: str, direction: int, quantity: str, price: str, 
                              validator: OrderValidator, reason: str = "") -> Optional[Dict]:
        """创建经过验证的订单"""
        try:
            price_decimal = Decimal(str(price))
            validation_result = validator.validate_order_price(price_decimal)
            
            if not validation_result.valid:
                self.logger.warning(f"🚫 订单创建失败: {validation_result.reason} (原因: {reason})")
                return None
            
            # 创建订单
            order = self.create_trade_bean(
                trading_pair=trading_pair,
                order_direction=direction,
                order_quantity=quantity,
                order_price=price
            )
            
            return order
            
        except (ValueError, TypeError) as e:
            self.logger.warning(f"⚠️ 创建订单失败: 价格={price}, 错误: {e}")
            return None
    
    def _should_maintain_liquidity(self, asks: list, bids: list) -> bool:
        """
        检查是否需要维护流动性
        
        Returns:
            bool: 如果买卖单不平衡或数量不足，返回True
        """
        min_orders_per_side = TradingConstants.MIN_ORDERS_PER_SIDE  # 每边至少保持的订单数
        
        # 检查买卖单数量
        if len(asks) < min_orders_per_side or len(bids) < min_orders_per_side:
            self.logger.info(f"⚠️ 流动性不足: asks={len(asks)}, bids={len(bids)}, 需要补充")
            return True
        
        return False
    
    def _create_liquidity_orders(self, price_state: PriceState, validator: OrderValidator, reduced: bool = False) -> List[Dict]:
        """创建流动性订单 - 统一的买卖墙创建方法"""
        orders = []
        spread_base = price_state.get_spread_base()
        
        if not spread_base:
            self.logger.warning("⚠️ 无法获取价差基准价格，跳过流动性订单创建")
            return orders
        
        # 使用配置的价差和层数
        if reduced:
            # 简化模式：使用配置层数的一半
            num_levels = max(3, self.liquidity_levels // 2)
            spreads = self.initial_spreads[:num_levels]
        else:
            # 标准模式：使用配置的完整层数
            spreads = self.initial_spreads[:self.liquidity_levels]
        
        # 创建买卖单
        for i, spread in enumerate(spreads):
            # 计算层级倍数
            multiplier = self._get_level_multiplier(i)
            
            # 买单 - 使用USDT金额计算数量
            buy_price = spread_base * (Decimal("1") - spread)
            buy_qty = (self.base_order_amount * multiplier) / buy_price
            # 确保满足最小数量要求
            buy_qty = self._ensure_min_quantity(buy_qty)
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"买墙-{float(spread*100):.1f}%"
            )
            if buy_order:
                orders.append(buy_order)
            
            # 卖单 - 使用金额计算数量（与买单保持一致）
            sell_price = spread_base * (Decimal("1") + spread)
            sell_amount = self.liquidity_amount_per_level * multiplier
            sell_qty = sell_amount / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"卖墙-{float(spread*100):.1f}%"
            )
            if sell_order:
                orders.append(sell_order)
        
        if orders:
            mode = "基础" if reduced else "完整"
            self.logger.info(f"💧 {mode}流动性: {len(orders)}单@${spread_base:.8f}")
        
        return orders
    

    
    def _log_price_state(self, price_state: PriceState, chain_price: Decimal):
        """记录价格状态信息 - 简化版本"""
        try:
            last_trade_str = f"${price_state.last_trade_price:.8f}" if price_state.last_trade_price else "N/A"
            mid_price_str = f"${price_state.mid_price:.8f}" if price_state.mid_price else "N/A"
            
            self.logger.info(f"📊 价格: 成交={last_trade_str} 中间={mid_price_str} DEX=${chain_price:.8f} 目标=${price_state.target_price:.8f}")
        except Exception as e:
            self.logger.error(f"❌ 记录价格状态异常: {e}")

    def _handle_price_extraction_error(self, error: Exception, price_type: str, data_source: str):
        """统一处理价格提取错误"""
        self.logger.warning(f"⚠️ 提取{price_type}失败 ({data_source}): {error}")
    
    def _validate_price_data_integrity(self, price_state: PriceState) -> bool:
        """验证价格数据的完整性 - 简化版本"""
        return (price_state.target_price > 0 and price_state.reference_price > 0)
    
    # 辅助方法
    
    def _ensure_min_quantity(self, qty: Decimal) -> Decimal:
        """确保数量满足最小要求"""
        return max(qty, TradingConstants.MIN_REQUIRED_QTY)
    
    def _get_level_multiplier(self, index: int) -> Decimal:
        """获取层级倍数，安全处理索引越界"""
        if index < len(self.level_multipliers):
            return self.level_multipliers[index]
        return self.level_multipliers[-1]
    
    def _initialize_empty_market(self, chain_price: Decimal, current_orders: List[Dict]) -> List[Dict]:
        """
        初始化完全空的市场 - 改进版本，使用价格状态系统
        
        Args:
            chain_price: 链上目标价格
            current_orders: 当前活跃订单
            
        Returns:
            List[Dict]: 初始化动作列表
        """
        actions = []
        
        # 清理现有订单
        if current_orders:
            order_ids = [order.get('orderId') for order in current_orders if order.get('orderId')]
            if order_ids:
                actions.append({
                    "action_type": "cancel",
                    "reason": "清理现有订单",
                    "orders": order_ids
                })
        
        if chain_price:
            self.logger.info(f"🚀 初始化@${chain_price:.8f}")
        else:
            self.logger.error("❌ 初始化失败: 无链上价格")
        
        orders = []
        
        # 使用链上价格作为初始参考价格
        init_price = chain_price
        
        # 创建空市场的价格状态（无成交价和中间价）
        init_price_state = PriceState(
            last_trade_price=None,
            mid_price=None,
            target_price=chain_price,
            reference_price=init_price
        )
        
        # 创建验证器（空市场模式，无300%限制）
        validator = OrderValidator(init_price_state)
        
        # 在目标价格周围创建初始订单
        # 使用配置的价差创建买卖单
        initial_spreads = self.initial_spreads[:self.liquidity_levels]  # 使用配置的价差级别
        
        # 创建买单
        for spread in initial_spreads:
            buy_price = init_price * (Decimal("1") - spread)
            qty = self.base_order_amount / buy_price
            
            # 确保满足最小数量要求
            qty = self._ensure_min_quantity(qty)
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"初始买单-{float(spread*100):.1f}%"
            )
            if buy_order:
                orders.append(buy_order)
        
        # 创建卖单
        for spread in initial_spreads:
            sell_price = init_price * (Decimal("1") + spread)
            qty = self.base_order_amount / sell_price
            
            # 确保满足最小数量要求
            qty = self._ensure_min_quantity(qty)
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"初始卖单-{float(spread*100):.1f}%"
            )
            if sell_order:
                orders.append(sell_order)
        
        if orders:
            actions.append({
                "action_type": "place",
                "reason": f"初始化至${chain_price:.8f}" if chain_price else "初始化至N/A",
                "orders": orders
            })
            self.logger.info(f"📊 创建{len(orders)}个初始订单 (无300%限制)")
        
        return actions
    
    def _get_chain_price(self) -> Optional[Decimal]:
        """获取链上价格"""
        try:
            # 优先使用合约地址，如果没有则使用代币符号
            token_identifier = self.contract_address if self.contract_address else self.base_symbol
            self.logger.info(f"🔍 获取价格: {token_identifier[:8]}.../{self.quote_symbol}")
            
            # 直接调用，RaydiumClient内部已经有超时处理
            price = self.raydium_client.get_token_price(token_identifier, self.quote_symbol)
            
            if price:
                self.logger.info(f"✅ 链上价格: ${price:.8f}")
                return price
            else:
                self.logger.warning(f"⚠️ 无法获取价格: {token_identifier}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 获取链上价格异常: {e}")
            return None
    

    def _parse_order_book(self, order_book_data: Optional[Dict]) -> tuple:
        """解析订单簿数据 - 简化版本"""
        asks, bids, quote_info = [], [], {}
        
        if not order_book_data:
            return asks, bids, quote_info
        
        try:
            # 多层嵌套数据查找
            current_data = order_book_data
            for _ in range(3):  # 最多3层深度
                if "quote" in current_data:
                    quote_info = current_data["quote"]
                
                if "orderDepth" in current_data:
                    order_depth = current_data["orderDepth"]
                    asks = order_depth.get("asks", [])
                    bids = order_depth.get("bids", [])
                    break
                elif "asks" in current_data:
                    asks = current_data.get("asks", [])
                    bids = current_data.get("bids", [])
                    break
                elif "data" in current_data:
                    current_data = current_data["data"]
                else:
                    break
            
            # 简单验证 - 过滤掉无效价格
            def is_valid_order(order):
                """验证订单是否有效"""
                try:
                    return len(order) >= 2 and float(order[0]) > 0 and float(order[1]) >= 0
                except (ValueError, TypeError, IndexError):
                    return False
            
            asks = [order for order in asks if is_valid_order(order)]
            bids = [order for order in bids if is_valid_order(order)]
            
        except Exception as e:
            self.logger.error(f"❌ 解析订单簿数据异常: {e}")
                
        return asks, bids, quote_info
    
    def _handle_first_run_cleanup(self, current_active_orders: List[Dict]) -> Optional[List[Dict]]:
        """
        处理首次运行清理逻辑 - 只清理本策略的订单
        
        Args:
            current_active_orders: 当前活跃订单列表 (来自OMS，已过滤到本策略)
            
        Returns:
            Optional[List[Dict]]: 如果需要返回清理动作则返回动作列表，否则返回None
        """
        if not self.is_first_run:
            return None
            
        self.logger.info("🚀 首次运行，初始化策略状态")
        actions = []
        
        # 1. 清理本策略在OMS中的缓存状态
        if hasattr(self, 'order_management_service') and self.order_management_service:
            try:
                cleared_count = self.order_management_service.clear_strategy_active_orders(self.strategy_id)
                if cleared_count > 0:
                    self.logger.info(f"📚 清理本策略历史缓存: {cleared_count}个订单")
            except Exception as e:
                self.logger.warning(f"⚠️ OMS缓存清理失败: {e}")
        
        # 2. 移除 cancel_all_orders 调用 - 不再撤销API端所有订单
        # 注释：之前的cancel_all_orders会影响其他策略，已移除
        
        # 3. 只处理OMS识别的本策略订单
        # current_active_orders 来自 OrderStatusMonitor，已经过滤到本策略
        if current_active_orders:
            # 这些订单应该都是本策略的（通过OMS strategy_id 过滤）
            order_ids = [order.get("orderId") for order in current_active_orders if order.get("orderId")]
            if order_ids:
                actions.append({
                    "action_type": "cancel",
                    "reason": f"清理本策略({self.strategy_id[:8]})历史订单",
                    "orders": order_ids
                })
                self.logger.info(f"📝 准备撤销本策略的 {len(order_ids)} 个历史订单")
        else:
            self.logger.info("✨ 本策略无历史订单，开始全新运行")
        
        self.is_first_run = False
        return actions if actions else None
    
    def _calculate_strategy_state(self, nine_cex_order_book_data: Optional[Dict], current_active_orders: List[Dict]) -> Optional[tuple]:
        """
        计算策略状态 - 包括链上价格、价格状态和订单簿数据
        
        Args:
            nine_cex_order_book_data: Nine CEX订单簿数据
            current_active_orders: 当前活跃订单列表
            
        Returns:
            Optional[tuple]: (chain_price, price_state, asks, bids) 或 None(如果计算失败)
        """
        # 验证交易对
        if not self.resolve_trading_pair_id(self.trading_pair):
            self.logger.error(f"❌ 交易对 {self.trading_pair} 无法解析")
            return None
        
        # 获取链上价格
        chain_price = self._get_chain_price()
        if not chain_price:
            self.logger.warning("⚠️ 无法获取链上价格")
            return None
        
        # 解析订单簿和计算价格状态
        self.logger.info("📊 解析订单簿...")
        asks, bids, quote_info = self._parse_order_book(nine_cex_order_book_data)
        price_state = self._calculate_price_state(asks, bids, quote_info, chain_price)
        self.logger.info("✅ 价格状态完成")
        
        # 记录价格状态
        self._log_price_state(price_state, chain_price)
        
        return chain_price, price_state, asks, bids
    
    def _execute_trading_logic(self, price_state: PriceState, asks: List, bids: List, chain_price: Decimal) -> List[Dict]:
        """
        执行核心交易逻辑 - 先挂单后成交
        
        Args:
            price_state: 价格状态
            asks: 卖单列表
            bids: 买单列表
            chain_price: 链上价格
            
        Returns:
            List[Dict]: 交易动作列表
        """
        actions = []
        validator = OrderValidator(price_state)
        
        # 计算价格调整需求
        target_price = price_state.target_price
        # 使用成交价作为判断基准，因为这是实际的市场价格
        current_price = price_state.last_trade_price or price_state.mid_price or price_state.reference_price
        
        if not current_price:
            # 无当前价格，创建基础流动性
            liquidity_actions = self._create_simple_liquidity(target_price, validator)
            return liquidity_actions
        
        # 计算价格偏差
        price_diff = abs(target_price - current_price) / current_price
        needs_price_push = price_diff > self.aggressive_threshold
        
        # 智能流动性策略：只在非推动模式下创建独立流动性
        
        # 智能策略决策：根据价格偏差选择合适的行为
        if needs_price_push:
            # 推动阶段：专注价格推动
            self.logger.info(f"🎯 偏差{price_diff*100:.1f}%，推动模式")
            
            # 推动+提供新深度（策略内部已包含深度订单）
            push_orders = self._create_precise_push_orders(asks, bids, target_price, current_price, validator)
            if push_orders:
                actions.append({
                    "action_type": "place", 
                    "reason": f"推动+新深度: ${current_price:.8f} → ${target_price:.8f}",
                    "orders": push_orders
                })
                self.logger.info(f"🚀 推动+深度: {len(push_orders)}单")
                return actions
                
        else:
            # 支撑阶段：在当前价格提供支撑性流动性
            self.logger.info(f"💪 价格接近目标，支撑模式")
            
            support_orders = self._create_supportive_liquidity(current_price, target_price, validator)
            if support_orders:
                actions.append({
                    "action_type": "place",
                    "reason": f"支撑性流动性 (巩固${current_price:.8f})",
                    "orders": support_orders
                })
                self.logger.info(f"💧 支撑: {len(support_orders)}单")
                return actions
                
        # 保底：只在没有其他行动时提供基础流动性
        if not actions:
            basic_orders = self._create_simple_liquidity(current_price, validator)
            if basic_orders:
                actions.extend(basic_orders)
                self.logger.info(f"💧 保底流动性")
        
        return actions
    
    def _create_simple_liquidity(self, base_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """创建简单流动性订单"""
        actions = []
        orders = []
        
        # 使用配置的价差层数
        spreads = self.initial_spreads[:self.liquidity_levels]
        
        for i, spread in enumerate(spreads):
            # 计算层级倍数
            multiplier = self._get_level_multiplier(i)
            
            # 买单 - 使用USDT金额计算数量
            buy_price = base_price * (Decimal("1") - spread)
            buy_qty = (self.base_order_amount * multiplier) / buy_price
            # 确保满足最小数量要求
            buy_qty = self._ensure_min_quantity(buy_qty)
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason="流动性买单"
            )
            if buy_order:
                orders.append(buy_order)
            
            # 卖单 - 使用固定数量（代币数量）
            sell_price = base_price * (Decimal("1") + spread)
            sell_amount = self.liquidity_amount_per_level * multiplier
            sell_qty = sell_amount / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason="流动性卖单"
            )
            if sell_order:
                orders.append(sell_order)
        
        if orders:
            actions.append({
                "action_type": "place",
                "reason": f"维护流动性 (基准: ${base_price:.8f})",
                "orders": orders
            })
        
        return actions

    def _create_precise_push_orders(self, asks: List, bids: List, target_price: Decimal, current_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """
        精准价格推动策略 - 根据用户描述的正确逻辑实现
        
        逻辑：
        1. 分析orderbook，计算需要消耗多少对手盘才能达到目标价格
        2. 精准下单吃掉这些订单，推动价格到目标位置
        3. 不同时下相反方向的订单造成干扰
        """
        orders = []
        
        if target_price > current_price:
            # 需要推高价格：计算需要吃掉的卖单
            orders = self._calculate_buy_orders_to_target(asks, target_price, validator)
            if orders:
                self.logger.info(f"📈 精准拉升: 吃{len(asks[:3])}档→${target_price:.8f}")
        elif target_price < current_price:
            # 需要压低价格：计算需要吃掉的买单  
            orders = self._calculate_sell_orders_to_target(bids, target_price, validator)
            if orders:
                self.logger.info(f"📉 精准下压: 吃{len(bids[:3])}档→${target_price:.8f}")
                
        return orders
    
    def _calculate_push_steps(self, current_price: Decimal, target_price: Decimal) -> List[Decimal]:
        """计算分步推动路径 - 使用配置的步长参数"""
        steps = []
        current = current_price
        
        while current < target_price:
            next_step = min(current * self.max_step_multiplier, target_price)
            steps.append(next_step)
            current = next_step
            
            # 防止无限循环
            if len(steps) > self.max_push_steps:
                self.logger.warning(f"⚠️ 推动步数过多({len(steps)})，中止计算")
                break
        
        self.logger.info(f"📊 推动路径: {len(steps)}步 ${current_price:.8f} → ${target_price:.8f}")
        for i, step in enumerate(steps, 1):
            multiplier = step / current_price
            self.logger.info(f"  步骤{i}: ${step:.8f} ({multiplier:.1f}倍)")
        
        return steps
    
    def _create_push_order_pairs(self, push_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """创建推动订单对：先卖单后买单，确保成交"""
        orders = []
        
        # 确保订单金额满足最小要求（0.1 USDT）
        min_order_value = max(self.min_order_amount, Decimal("0.1"))  # 至少0.1 USDT
        
        # 步骤1：创建卖单在推动价格（用于成交）
        sell_amount = max(self.small_trade_amount, min_order_value)
        sell_qty = sell_amount / push_price
        sell_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=2,  # 卖出
            quantity=self._format_quantity(sell_qty, self.qty_precision),
            price=self._format_price(push_price, self.price_precision),
            validator=validator,
            reason=f"推动卖单@${push_price:.8f}"
        )
        if sell_order:
            orders.append(sell_order)
        
        # 步骤2：创建买单略高于卖单（确保成交推动价格）
        buy_price = push_price * Decimal("1.001")  # 高0.1%确保成交
        buy_amount = max(self.small_trade_amount, min_order_value) 
        buy_qty = buy_amount / buy_price
        
        # 再次验证买单价格（应该也在990%限制内）
        buy_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=1,  # 买入
            quantity=self._format_quantity(buy_qty, self.qty_precision),
            price=self._format_price(buy_price, self.price_precision),
            validator=validator,
            reason=f"推动买单@${buy_price:.8f}"
        )
        if buy_order:
            orders.append(buy_order)
        
        if len(orders) == 2:
            order_value = float(min_order_value)
            self.logger.info(f"🎯 推动订单对: 卖@${push_price:.8f} + 买@${buy_price:.8f} (${order_value:.2f})")
        
        return orders

    def _calculate_buy_orders_to_target(self, asks: List, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """计算推高价格需要的精准买单 - 使用分步推动策略"""
        orders = []
        current_base = validator.limit_base or Decimal("0.00006111")
        
        # 分步推动：计算推动路径
        push_steps = self._calculate_push_steps(current_base, target_price)
        
        if not push_steps:
            self.logger.warning("⚠️ 无法计算推动路径")
            return orders
        
        # 第一步：推动到第一个阶段
        first_step = push_steps[0]
        
        if not asks:
            # 无卖单深度，使用订单对策略
            self.logger.info(f"📈 无阻力，分步拉升第1步")
            push_orders = self._create_push_order_pairs(first_step, validator)
            orders.extend(push_orders)
        else:
            # 有卖单深度，先吃单推动
            self.logger.info(f"📈 有阻力，吃单推动第1步")
            eat_orders = self._create_fallback_eat_orders(asks[:3], validator)  # 只吃前3档
            orders.extend(eat_orders)
        
        # 在第一步推动价格附近创建深度
        if orders and first_step < target_price:
            # 如果还需要继续推动，为下一步做准备
            next_step = push_steps[1] if len(push_steps) > 1 else target_price
            prep_orders = self._create_next_step_preparation(first_step, next_step, validator)
            orders.extend(prep_orders)
        
        if orders:
            total_steps = len(push_steps)
            current_step_ratio = float(first_step / current_base)
            self.logger.info(f"🚀 分步推动第1/{total_steps}步: ${current_base:.8f}→${first_step:.8f} ({current_step_ratio:.1f}倍)")
        
        return orders
    
    def _create_next_step_preparation(self, _current_step: Decimal, next_step: Decimal, validator: OrderValidator) -> List[Dict]:
        """为下一步推动做准备：在下一个目标价格挂少量卖单"""
        orders = []
        
        # 检查下一步是否在990%限制内
        limit_base = validator.limit_base
        if not limit_base:
            self.logger.warning("⚠️ 无基准价格，跳过下步准备")
            return orders
        
        max_allowed_price = limit_base * TradingConstants.PRICE_LIMIT_FACTOR  # 990%限制
        
        if next_step > max_allowed_price:
            self.logger.info(f"⚠️ 跳过下步准备: ${next_step:.8f} > ${max_allowed_price:.8f} (990%限制)")
            return orders
        
        # 确保订单金额满足最小要求
        min_order_value = max(self.min_order_amount, Decimal("0.1"))
        sell_amount = max(self.small_trade_amount, min_order_value)
        sell_qty = sell_amount / next_step
        
        sell_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=2,  # 卖出
            quantity=self._format_quantity(sell_qty, self.qty_precision),
            price=self._format_price(next_step, self.price_precision),
            validator=validator,
            reason=f"下步准备@${next_step:.8f}"
        )
        if sell_order:
            orders.append(sell_order)
            order_value = float(sell_amount)
            self.logger.info(f"🎯 下步准备: ${next_step:.8f} 卖单就位 (${order_value:.2f})")
        
        return orders
    
    def _create_fallback_eat_orders(self, asks: List, validator: OrderValidator) -> List[Dict]:
        """回退的吃单策略"""
        orders = []
        total_amount = Decimal("0")
        
        # 只吃前几档，避免价格跳跃过大
        for ask_price_str, ask_qty_str in asks[:3]:  # 只吃前3档
            try:
                ask_price = Decimal(str(ask_price_str))
                ask_qty = Decimal(str(ask_qty_str))
            except (ValueError, TypeError) as e:
                self.logger.warning(f"⚠️ 跳过无效ask订单: price={ask_price_str}, qty={ask_qty_str}, error={e}")
                continue
                
            # 需要吃掉这一档，确保最小金额0.1 USDT
            min_order_value = max(self.min_order_amount, Decimal("0.1"))
            order_amount = max(min_order_value, min(self.medium_trade_amount, ask_qty * ask_price))
            buy_qty = order_amount / ask_price
            
            # 买单价格设置得略高于卖价，确保成交
            buy_price = ask_price * Decimal("1.001")  # 高0.1%确保成交
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,  # 买入
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"吃单推价"
            )
            
            if buy_order:
                orders.append(buy_order)
                total_amount += order_amount
                self.logger.info(f"🎯 吃单计划: ${buy_price:.8f} x {buy_qty:.2f} = ${order_amount:.2f}")
                
                # 防止单次推动金额过大
                if total_amount >= self.medium_trade_amount:
                    break
                    
        return orders
    
    def _calculate_sell_orders_to_target(self, bids: List, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """计算压低价格需要的精准卖单"""
        if not bids:
            # 无买单深度，需要主动创建卖单压低价格
            self.logger.info("📉 无阻力，主动下压")
            return self._create_aggressive_sell_order(target_price, validator)
            
        orders = []
        total_amount = Decimal("0")
        
        for bid_price_str, bid_qty_str in bids:
            try:
                bid_price = Decimal(str(bid_price_str))
                bid_qty = Decimal(str(bid_qty_str))
            except (ValueError, TypeError) as e:
                self.logger.warning(f"⚠️ 跳过无效bid订单: price={bid_price_str}, qty={bid_qty_str}, error={e}")
                continue
            
            if bid_price <= target_price:
                break
                
            # 需要吃掉这一档买单
            order_amount = min(self.medium_trade_amount, bid_qty * bid_price)
            sell_qty = order_amount / bid_price
            
            # 卖单价格略低于买价确保成交
            sell_price = bid_price * Decimal("0.999")
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,  # 卖出
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"精准砸盘至${target_price:.8f}"
            )
            
            if sell_order:
                orders.append(sell_order)
                total_amount += order_amount
                
                if total_amount >= self.medium_trade_amount:
                    break
                
        return orders
    
    def _create_aggressive_buy_order(self, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """做市商推动策略"""
        orders = []
        
        current_base = validator.limit_base
        if not current_base:
            self.logger.warning("⚠️ 无基准价格")
            return orders
        
        # Nine系统990%限制
        nine_max_price = current_base * Decimal("9.9")
        
        if target_price > nine_max_price:
            push_target = nine_max_price
            self.logger.info(f"🚀 最大推动至${push_target:.8f}")
        else:
            push_target = target_price  
            self.logger.info(f"🎯 推动至${push_target:.8f}")
        
        # 既然深度都是我们控制，就用小额推动即可
        push_amount = self.small_trade_amount  # 只需要小额推动我们自己的卖单
        buy_qty = push_amount / push_target
        
        buy_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=1,
            quantity=self._format_quantity(buy_qty, self.qty_precision),
            price=self._format_price(push_target, self.price_precision),
            validator=validator,
            reason=f"推动至${push_target:.8f}"
        )
        
        if buy_order:
            orders.append(buy_order)
            push_ratio = float(push_target / current_base)
            self.logger.info(f"📈 推动: ${current_base:.8f}→${push_target:.8f} ({push_ratio:.1f}x)")
            
            # 推动后立即在新价位提供卖单深度
            new_depth_orders = self._create_depth_after_push(push_target, validator)
            orders.extend(new_depth_orders)
        else:
            self.logger.warning(f"⚠️ 推动失败")
        
        return orders
    
    def _create_depth_after_push(self, new_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """推动后立即在新价位提供深度 - 确保不超出990%限制"""
        orders = []
        
        # 获取原始基准价格和990%限制
        limit_base = validator.limit_base
        if not limit_base:
            self.logger.warning("⚠️ 无基准价格，跳过推动后深度创建")
            return orders
        
        max_allowed_price = limit_base * TradingConstants.PRICE_LIMIT_FACTOR  # 990%限制
        
        # 在新价位上方提供卖单深度，为下次推动做准备
        sell_spreads = [Decimal("0.01"), Decimal("0.02"), Decimal("0.05")]  # 1%, 2%, 5%
        
        for spread in sell_spreads:
            sell_price = new_price * (Decimal("1") + spread)
            
            # 检查是否超出990%限制
            if sell_price > max_allowed_price:
                self.logger.info(f"⚠️ 跳过推动后深度+{float(spread*100):.0f}%: ${sell_price:.8f} > ${max_allowed_price:.8f}")
                continue
                
            sell_qty = self.liquidity_amount_per_level / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,  # 卖出
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"推动后深度+{float(spread*100):.0f}%"
            )
            if sell_order:
                orders.append(sell_order)
        
        # 注意：不在新价位下方创建买单，避免与现有买单冲突
        # 旧的买单仍然提供支撑，无需重复创建
        
        if orders:
            self.logger.info(f"💧 新价位深度: {len(orders)}单@${new_price:.8f} (基准限制: ${max_allowed_price:.8f})")
        
        return orders
    
    def _calculate_step_lift_orders(self, base_price: Decimal, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """计算分步拉升订单路径，每步不超过500%，避免Nine动态基准问题"""
        orders = []
        current_price = base_price
        max_step_factor = Decimal("5.0")  # 保守策略：500%每步（避免Nine动态基准问题）
        max_steps = 12  # 增加步数，每步更保守
        step_count = 0
        min_price_increment = Decimal("0.00000001")  # 最小价格增量，防止精度问题
        
        while current_price < target_price and step_count < max_steps:
            step_count += 1
            
            # 计算下一步价格（不超过290%或目标价格）
            next_price = min(
                current_price * max_step_factor,
                target_price
            )
            
            # 检查价格增量是否足够，防止精度问题导致死循环
            if next_price - current_price < min_price_increment:
                self.logger.warning(f"⚠️ 价格增量过小({next_price - current_price:.10f})，停止拉升")
                break
            
            # 检查是否已有相同价格的拉升订单，避免重复
            price_formatted = self._format_price(next_price, self.price_precision)
            duplicate_found = any(
                order.get('reason', '').startswith('分步拉升') and 
                order.get('orderPrice') == price_formatted 
                for order in orders
            )
            
            if duplicate_found:
                self.logger.info(f"⏭️ 跳过重复拉升价格: ${next_price:.8f}")
                current_price = next_price
                validator.limit_base = next_price
                continue
            
            # 创建拉升买单
            buy_qty = self.medium_trade_amount / next_price
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=price_formatted,
                validator=validator,
                reason=f"分步拉升${next_price:.8f}"
            )
            
            if buy_order:
                orders.append(buy_order)
                
                # 关键：在拉升价格附近创建卖单提供流动性
                # 限制：只在最后一步或每3步创建一次流动性，避免订单过多
                if step_count >= max_steps or step_count % 3 == 0 or next_price == target_price:
                    liquidity_orders = self._create_liquidity_around_price(next_price, validator)
                    orders.extend(liquidity_orders)
                
                change_pct = ((next_price/current_price-1)*100) if current_price > 0 else 0
                self.logger.info(f"📈 ${current_price:.8f}→${next_price:.8f} +{change_pct:.1f}%")
                
                # 更新当前价格和验证器基准（关键修复）
                current_price = next_price
                # 更新验证器基准价格，使下一步能通过300%限制
                validator.limit_base = next_price
            else:
                self.logger.warning(f"⚠️ 拉升步骤失败: 无法创建${next_price:.8f}买单")
                break
                
        return orders
    
    def _create_liquidity_around_price(self, center_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """在指定价格周围创建流动性订单"""
        orders = []
        
        # 安全检查：价格必须大于0
        if center_price <= 0:
            self.logger.warning(f"⚠️ 中心价格异常({center_price})，跳过流动性订单创建")
            return orders
            
        spread = Decimal("0.001")  # 0.1%价差
        
        # 创建卖单（略高于中心价格）
        sell_price = center_price * (1 + spread)
        if sell_price <= 0:
            self.logger.warning(f"⚠️ 卖单价格异常({sell_price})，跳过卖单创建")
            return orders
        sell_qty = self.small_trade_amount / sell_price
        
        sell_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=2,
            quantity=self._format_quantity(sell_qty, self.qty_precision),
            price=self._format_price(sell_price, self.price_precision),
            validator=validator,
            reason=f"拉升后流动性${sell_price:.8f}"
        )
        
        if sell_order:
            orders.append(sell_order)
            
        return orders
    
    def _create_aggressive_sell_order(self, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """分步下压机制：避开CEX限制，确保流动性"""
        orders = []
        
        # 计算分步下压路径
        current_base = validator.limit_base or target_price * Decimal("2")  # 使用验证器基准或目标价格两倍作为起点
        step_orders = self._calculate_step_drop_orders(current_base, target_price, validator)
        
        if step_orders:
            actual_steps = sum(1 for order in step_orders if "分步下压" in order.get('reason', ''))
            self.logger.info(f"📉 分步下压: ${current_base:.8f}→${target_price:.8f} {actual_steps}步/{len(step_orders)}单")
            orders.extend(step_orders)
        else:
            self.logger.warning(f"⚠️ 下压失败: ${current_base:.8f}→${target_price:.8f}")
        
        return orders
    
    def _calculate_step_drop_orders(self, base_price: Decimal, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """计算分步下压订单路径，每步不超过500%价差，避免Nine动态基准问题"""
        orders = []
        current_price = base_price
        max_step_factor = Decimal("5.0")  # 保守策略：500%每步（避免Nine动态基准问题）
        max_steps = 12  # 增加步数，每步更保守
        step_count = 0
        min_price_decrement = Decimal("0.00000001")  # 最小价格减量
        
        while current_price > target_price and step_count < max_steps:
            step_count += 1
            
            # 计算下一步价格（不超过290%价差）
            next_price = max(
                current_price / max_step_factor,
                target_price
            )
            
            # 检查价格减量是否足够
            if current_price - next_price < min_price_decrement:
                self.logger.warning(f"⚠️ 价格减量过小({current_price - next_price:.10f})，停止下压")
                break
            
            # 创建下压卖单
            sell_qty = self.medium_trade_amount / next_price
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(next_price, self.price_precision),
                validator=validator,
                reason=f"分步下压${next_price:.8f}"
            )
            
            if sell_order:
                orders.append(sell_order)
                
                # 在下压价格附近创建买单提供流动性
                # 限制：只在最后一步或每3步创建一次流动性，避免订单过多
                if step_count >= max_steps or step_count % 3 == 0 or next_price == target_price:
                    liquidity_orders = self._create_buy_liquidity_around_price(next_price, validator)
                    orders.extend(liquidity_orders)
                
                change_pct = ((next_price/current_price-1)*100) if current_price > 0 else 0
                self.logger.info(f"📉 ${current_price:.8f}→${next_price:.8f} {change_pct:.1f}%")
                
                # 更新当前价格和验证器基准（关键修复）
                current_price = next_price
                # 更新验证器基准价格，使下一步能通过300%限制
                validator.limit_base = next_price
            else:
                self.logger.warning(f"⚠️ 下压步骤失败: 无法创建${next_price:.8f}卖单")
                break
                
        return orders
    
    def _create_buy_liquidity_around_price(self, center_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """在指定价格周围创建买单流动性"""
        orders = []
        
        # 安全检查：价格必须大于0
        if center_price <= 0:
            self.logger.warning(f"⚠️ 中心价格异常({center_price})，跳过买单流动性创建")
            return orders
            
        spread = Decimal("0.001")  # 0.1%价差
        
        # 创建买单（略低于中心价格）
        buy_price = center_price * (1 - spread)
        if buy_price <= 0:
            self.logger.warning(f"⚠️ 买单价格异常({buy_price})，跳过买单创建")
            return orders
        buy_qty = self.small_trade_amount / buy_price
        
        buy_order = self._create_validated_order(
            trading_pair=self.trading_pair,
            direction=1,
            quantity=self._format_quantity(buy_qty, self.qty_precision),
            price=self._format_price(buy_price, self.price_precision),
            validator=validator,
            reason=f"下压后流动性${buy_price:.8f}"
        )
        
        if buy_order:
            orders.append(buy_order)
            
        return orders
    
    def _optimize_liquidity_orders(self, liquidity_orders: List[Dict], current_price: Decimal) -> List[Dict]:
        """优化流动性订单排列"""
        if not liquidity_orders:
            return []
            
        # 按方向分类并排序
        buy_orders = [o for o in liquidity_orders if o.get("orderDirection") == 1]
        sell_orders = [o for o in liquidity_orders if o.get("orderDirection") == 2]
        
        # 买单按价格从高到低排序
        buy_orders.sort(key=lambda x: Decimal(x.get("orderPrice", "0")), reverse=True)
        # 卖单按价格从低到高排序
        sell_orders.sort(key=lambda x: Decimal(x.get("orderPrice", "0")))
        
        # 合并订单，优先放置买单（支撑价格）
        final_orders = buy_orders + sell_orders
        
        self.logger.info(f"💧 流动性: {len(buy_orders)}买+{len(sell_orders)}卖@${current_price:.8f}")
        
        return final_orders
    
    def _create_supportive_liquidity(self, current_price: Decimal, target_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """
        创建支撑性流动性 - 巩固推动成果，避免与推动方向冲突
        
        原则：
        1. 主要在推动方向提供支撑
        2. 反方向提供少量流动性，不形成阻力
        """
        orders = []
        
        if target_price > current_price:
            # 向上推动场景：主要提供买单支撑，少量高价卖单
            orders.extend(self._create_upward_support(current_price, validator))
        elif target_price < current_price:
            # 向下推动场景：主要提供卖单压力，少量低价买单
            orders.extend(self._create_downward_support(current_price, validator))
        else:
            # 价格已到位：提供平衡的双向流动性，重点解决深度不足问题
            self.logger.info(f"🎯 价格到位，加强深度建设")
            orders.extend(self._create_enhanced_balanced_liquidity(current_price, validator))
            
        return orders
    
    def _create_upward_support(self, current_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """向上推动后的支撑性订单：强买单支撑 + 弱卖单流动性"""
        orders = []
        
        # 主力：在当前价格下方创建买单支撑（防止回落）
        # 根据配置动态生成支撑层级
        support_levels = []
        for i in range(self.liquidity_levels):
            level = 1 - (0.02 + i * 0.03)  # 0.98, 0.95, 0.92, 0.89, 0.86...
            support_levels.append(level)
        
        for i, level in enumerate(support_levels):
            buy_price = current_price * Decimal(str(level))
            buy_amount = self.liquidity_amount_per_level * Decimal(str(2 - i * 0.3))  # 递减金额
            buy_qty = buy_amount / buy_price
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"向上支撑位{level*100:.0f}%"
            )
            if buy_order:
                orders.append(buy_order)
        
        # 辅助：在更高价格提供少量卖单流动性（不形成阻力）
        # 根据配置动态生成卖单层级
        sell_levels = []
        for i in range(self.liquidity_levels):
            level = 1.15 + (i * 0.05)  # 1.15, 1.20, 1.25, 1.30, 1.35...
            sell_levels.append(level)
            
        for level in sell_levels:
            sell_price = current_price * Decimal(str(level))
            sell_amount = self.liquidity_amount_per_level * Decimal("0.3")  # 较小金额
            sell_qty = sell_amount / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"高价流动性{level*100:.0f}%"
            )
            if sell_order:
                orders.append(sell_order)
                
        return orders
    
    def _create_downward_support(self, current_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """向下推动后的支撑性订单：强卖单压力 + 弱买单流动性"""
        orders = []
        
        # 主力：在当前价格上方创建卖单压力（维持下压）
        # 根据配置动态生成压力层级
        pressure_levels = []
        for i in range(self.liquidity_levels):
            level = 1 + (0.02 + i * 0.03)  # 1.02, 1.05, 1.08, 1.11, 1.14...
            pressure_levels.append(level)
            
        for i, level in enumerate(pressure_levels):
            sell_price = current_price * Decimal(str(level))
            sell_amount = self.liquidity_amount_per_level * Decimal(str(2 - i * 0.3))
            sell_qty = sell_amount / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"向下压力位{level*100:.0f}%"
            )
            if sell_order:
                orders.append(sell_order)
        
        # 辅助：在更低价格提供少量买单流动性
        # 根据配置动态生成买单层级
        buy_levels = []
        for i in range(self.liquidity_levels):
            level = 0.85 - (i * 0.05)  # 0.85, 0.80, 0.75, 0.70, 0.65...
            buy_levels.append(level)
            
        for level in buy_levels:
            buy_price = current_price * Decimal(str(level))
            buy_amount = self.liquidity_amount_per_level * Decimal("0.3")
            buy_qty = buy_amount / buy_price
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"低价流动性{level*100:.0f}%"
            )
            if buy_order:
                orders.append(buy_order)
                
        return orders
    
    def _create_enhanced_balanced_liquidity(self, current_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """增强的平衡流动性：专门解决深度不足问题"""
        orders = []
        
        # 增强版流动性：更多层级，更大数量
        buy_levels = [0.99, 0.98, 0.96, 0.94, 0.92]  # 5层买单
        sell_levels = [1.01, 1.02, 1.04, 1.06, 1.08]  # 5层卖单
        
        # 确保订单金额满足最小要求
        min_order_value = max(self.min_order_amount, Decimal("0.1"))
        base_amount = max(self.liquidity_amount_per_level, min_order_value)
        
        for i, buy_level in enumerate(buy_levels):
            buy_price = current_price * Decimal(str(buy_level))
            # 层级越接近当前价格，数量越大
            level_multiplier = Decimal(str(2.0 - i * 0.2))  # 2.0, 1.8, 1.6, 1.4, 1.2
            buy_amount = base_amount * level_multiplier
            buy_qty = buy_amount / buy_price
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"增强买单{buy_level*100:.0f}%"
            )
            if buy_order:
                orders.append(buy_order)
        
        for i, sell_level in enumerate(sell_levels):
            sell_price = current_price * Decimal(str(sell_level))
            # 层级越接近当前价格，数量越大  
            level_multiplier = Decimal(str(2.0 - i * 0.2))  # 2.0, 1.8, 1.6, 1.4, 1.2
            sell_amount = base_amount * level_multiplier
            sell_qty = sell_amount / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"增强卖单{sell_level*100:.0f}%"
            )
            if sell_order:
                orders.append(sell_order)
        
        if orders:
            buy_count = sum(1 for o in orders if o.get('orderDirection') == 1)
            sell_count = sum(1 for o in orders if o.get('orderDirection') == 2)
            self.logger.info(f"🏗️ 增强深度: {buy_count}买+{sell_count}卖@${current_price:.8f}")
        
        return orders

    def _create_balanced_liquidity(self, current_price: Decimal, validator: OrderValidator) -> List[Dict]:
        """价格到位后的平衡流动性：标准双向订单"""
        orders = []
        
        # 根据配置动态生成价格层级
        levels = []
        for i in range(self.liquidity_levels):
            # 每层递增的价差：2%, 5%, 8%, 11%, 14%...
            spread = 0.02 + (i * 0.03)  # 2%, 5%, 8%...
            buy_level = 1 - spread
            sell_level = 1 + spread
            levels.append((buy_level, sell_level))
        
        for buy_level, sell_level in levels:
            # 买单
            buy_price = current_price * Decimal(str(buy_level))
            buy_qty = self.liquidity_amount_per_level / buy_price
            
            buy_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=1,
                quantity=self._format_quantity(buy_qty, self.qty_precision),
                price=self._format_price(buy_price, self.price_precision),
                validator=validator,
                reason=f"平衡买单{buy_level*100:.0f}%"
            )
            if buy_order:
                orders.append(buy_order)
            
            # 卖单
            sell_price = current_price * Decimal(str(sell_level))
            sell_qty = self.liquidity_amount_per_level / sell_price
            
            sell_order = self._create_validated_order(
                trading_pair=self.trading_pair,
                direction=2,
                quantity=self._format_quantity(sell_qty, self.qty_precision),
                price=self._format_price(sell_price, self.price_precision),
                validator=validator,
                reason=f"平衡卖单{sell_level*100:.0f}%"
            )
            if sell_order:
                orders.append(sell_order)
                
        return orders

    def _log_trading_pair_balance(self):
        """显示交易对相关的余额信息"""
        try:
            balances = self.get_trading_pair_balances()
            
            if balances:
                base_asset, quote_asset = self.trading_pair.split('/')
                
                # 获取基础资产余额
                base_info = balances.get(base_asset, {})
                base_total = base_info.get('total', 0)
                base_available = base_info.get('available', 0) 
                base_locked = base_info.get('locked', 0)
                
                # 获取计价资产余额
                quote_info = balances.get(quote_asset, {})
                quote_total = quote_info.get('total', 0)
                quote_available = quote_info.get('available', 0)
                quote_locked = quote_info.get('locked', 0)
                
                # 显示余额信息
                if base_locked > 0:
                    base_str = f"{base_asset}={base_total:,.0f}(可用{base_available:,.0f})"
                else:
                    base_str = f"{base_asset}={base_total:,.0f}"
                    
                if quote_locked > 0:
                    quote_str = f"{quote_asset}={quote_total:,.0f}(可用{quote_available:,.0f})"
                else:
                    quote_str = f"{quote_asset}={quote_total:,.0f}"
                
                self.logger.info(f"余额: {base_str}, {quote_str}")
            else:
                self.logger.info("余额查询失败")
                
        except Exception as e:
            self.logger.debug(f"余额查询异常: {e}")


