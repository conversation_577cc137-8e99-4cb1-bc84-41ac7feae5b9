import time
import decimal
from typing import List, Dict, Any, Optional
from decimal import InvalidOperation
import logging

from .base_strategy import BaseStrategy
from app.services.nine_client import NineClient
# BinanceClient is no longer directly needed for this strategy's core logic
# from app.services.binance_client import BinanceClient 

class VolumeKlineStrategy(BaseStrategy):
    """
    成交量K线策略 - 定时定量刷量策略
    
    核心逻辑：
    1. 获取Nine CEX盘口最佳买卖价格（仅第一档）
    2. 在最佳买价下买单，在最佳卖价下卖单
    3. 定时执行，增加交易对成交量
    """
    def __init__(
        self,
        config: Dict[str, Any],
        nine_client: NineClient,
        logger: logging.Logger
    ):
        super().__init__(config, nine_client, logger)
        # self.binance_client = binance_client # Removed
        # if not self.binance_client: # Removed
        #     raise ValueError("Binance client is required for VolumeKlineStrategy.")
        self.last_execution_time: float = 0.0
        # VK_INITIAL_DIRECTION is no longer needed as we place both buy and sell
        # initial_direction_str = str(self._get_config_value("VK_INITIAL_DIRECTION", "buy", str)).lower()
        # if initial_direction_str not in ["buy", "sell"]:
        #     raise ValueError("VK_INITIAL_DIRECTION must be 'buy' or 'sell'")
        # self.next_direction: str = initial_direction_str

        # Load VolumeKlineStrategy specific API keys. These are now mandatory.
        # BaseStrategy._get_config_value will raise ValueError if these are not found or are empty.
        self.api_key = self._get_config_value("VK_NINE_API_KEY")
        self.api_secret = self._get_config_value("VK_NINE_API_SECRET")
        
        # 初始化日志已在 BaseStrategy 中统一处理
        
        # 新增：声明订单簿获取参数
        self.fetch_nine_book_symbol = self._get_config_value("VK_TRADING_PAIR", expected_type=str)
        self.fetch_nine_book_precision = float(self._get_config_value("VK_NINE_POOL_PRECISION_API_PARAM", "0.01", str))
        self.fetch_nine_book_depth = 5  # Nine CEX API要求最小值为5

        self.last_execution_time = 0
        self.last_api_error_time = 0
        self.api_error_cooldown = 60  # 60秒冷却时间

        # 配置验证和调试信息
        self._validate_configuration()

    def _validate_configuration(self):
        """验证 VolumeKlineStrategy 配置"""
        # 验证关键配置项
        trading_pair = self._get_config_value("VK_TRADING_PAIR", expected_type=str)
        order_amount = self._get_config_value("VK_ORDER_AMOUNT", expected_type=str)
        # 验证 API 凭证存在性
        self._get_config_value("VK_NINE_API_KEY", expected_type=str)

        self.logger.info(f"✅ [VK策略] 配置验证完成 - 交易对: {trading_pair}, 订单数量: {order_amount}")

    def get_actions(self,
                    current_active_orders: Optional[List[Dict]] = None, 
                    nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        刷量策略核心逻辑：
        1. 检查执行间隔
        2. 撤销旧订单
        3. 在盘口最佳价格下单（买单+卖单）
        """
        actions: List[Dict[str, Any]] = []
        current_time = time.time()

        # 获取策略配置
        trading_pair = str(self._get_config_value("VK_TRADING_PAIR", expected_type=str))
        
        # 固定下单数量（基础资产）
        order_quantity_base_str = str(self._get_config_value("VK_ORDER_AMOUNT", expected_type=str))
        desired_order_quantity_base = decimal.Decimal(order_quantity_base_str)
        
        # 最小交易数量阈值
        min_trade_qty_asset_str = str(self._get_config_value("VK_MIN_TRADE_QTY_ASSET", expected_type=str))
        min_trade_qty_asset = decimal.Decimal(min_trade_qty_asset_str)
        if min_trade_qty_asset <= decimal.Decimal("0"):
            raise ValueError("VK_MIN_TRADE_QTY_ASSET must be positive.")

        # Nine CEX订单参数
        account_type = self._get_config_value("NINE_ACCOUNT_TYPE", expected_type=int)
        order_direction_buy = self._get_config_value("NINE_ORDER_DIR_BUY", expected_type=int)
        order_direction_sell = self._get_config_value("NINE_ORDER_DIR_SELL", expected_type=int)
        order_type_limit = self._get_config_value("NINE_ORDER_TYPE_LIMIT", expected_type=int)

        # 精度配置
        price_precision = self._get_config_value("VK_PRICE_PRECISION", expected_type=int)
        qty_precision = self._get_config_value("VK_QTY_PRECISION", expected_type=int)
        
        # 2. 撤销旧订单（刷量策略每次都重新下单）
        if current_active_orders:
            order_ids_to_cancel = [order['orderId'] for order in current_active_orders if order.get('orderId')]
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "orders": order_ids_to_cancel,
                    "reason": "刷量策略撤销旧订单"
                })

        # 3. 检查订单簿数据
        if not nine_cex_order_book_data:
            self.logger.error(f"❌ Nine CEX订单簿数据缺失")
            self.last_execution_time = current_time  # 更新时间避免频繁重试
            return actions

        # 4. 获取盘口最佳价格（仅第一档）
        nine_asks = nine_cex_order_book_data.get("asks", [])
        nine_bids = nine_cex_order_book_data.get("bids", [])

        if not nine_asks or not nine_bids:
            self.logger.warning(f"⚠️ Nine CEX {trading_pair}盘口数据不全")
            self.last_execution_time = current_time
            return actions

        orders_to_place = []

        # 5. 同时准备买单和卖单（真正的做市刷量策略）
        # 注意：需要账户中同时有SEPUSDT和SEPBTC余额
        
        # 准备买单（使用卖一价立即成交）
        if nine_asks and isinstance(nine_asks[0], list) and len(nine_asks[0]) >= 2:
            try:
                best_ask_price = decimal.Decimal(str(nine_asks[0][0]))  # 使用卖一价
                pool_best_ask_amount = decimal.Decimal(str(nine_asks[0][1]))

                if best_ask_price > decimal.Decimal("0") and pool_best_ask_amount > decimal.Decimal("0"):
                    # 下单数量：取固定数量和盘口数量的较小值
                    buy_qty_candidate = min(desired_order_quantity_base, pool_best_ask_amount)
                    
                    if buy_qty_candidate >= min_trade_qty_asset:
                        formatted_qty = self._format_quantity(buy_qty_candidate, qty_precision)
                        formatted_price = self._format_price(best_ask_price, price_precision)
                        
                        # 使用新的 create_trade_bean 方法，自动处理交易对ID转换
                        buy_order_data = self.create_trade_bean(
                            trading_pair=trading_pair,
                            order_direction=order_direction_buy,
                            order_quantity=formatted_qty,
                            order_price=formatted_price,
                            account_type=account_type,
                            order_type=order_type_limit
                        )
                        orders_to_place.append(buy_order_data)
            except (InvalidOperation, TypeError, IndexError) as e:
                self.logger.warning(f"⚠️ 处理买单数据异常: {e}")

        # 准备卖单（使用买一价立即成交）
        if nine_bids and isinstance(nine_bids[0], list) and len(nine_bids[0]) >= 2:
            try:
                best_bid_price = decimal.Decimal(str(nine_bids[0][0]))  # 使用买一价
                pool_best_bid_amount = decimal.Decimal(str(nine_bids[0][1]))

                if best_bid_price > decimal.Decimal("0") and pool_best_bid_amount > decimal.Decimal("0"):
                    # 下单数量：取固定数量和盘口数量的较小值
                    sell_qty_candidate = min(desired_order_quantity_base, pool_best_bid_amount)

                    if sell_qty_candidate >= min_trade_qty_asset:
                        formatted_qty = self._format_quantity(sell_qty_candidate, qty_precision)
                        formatted_price = self._format_price(best_bid_price, price_precision)
                        
                        # 使用新的 create_trade_bean 方法，自动处理交易对ID转换
                        sell_order_data = self.create_trade_bean(
                            trading_pair=trading_pair,
                            order_direction=order_direction_sell,
                            order_quantity=formatted_qty,
                            order_price=formatted_price,
                            account_type=account_type,
                            order_type=order_type_limit
                        )
                        orders_to_place.append(sell_order_data)
            except (InvalidOperation, TypeError, IndexError) as e:
                self.logger.warning(f"⚠️ 处理卖单数据异常: {e}")
        
        # 7. 提交订单
        if orders_to_place:
            actions.append({
                "action_type": "place",
                "orders": orders_to_place,
                "reason": f"刷量策略: 在盘口最佳价格下{len(orders_to_place)}个订单"
            })
            self.logger.info(f"🎯 [VK策略] 准备下单: {len(orders_to_place)} 个订单")

        # 8. 更新执行时间
        self.last_execution_time = current_time
        # self.next_direction is no longer needed

        return actions 