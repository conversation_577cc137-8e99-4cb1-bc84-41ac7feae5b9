"""
自适应K线操盘策略

核心功能：
1. 支持拉盘(bull)、砸盘(bear)、横盘(sideways)三种操盘模式
2. 通过随机化时间、数量、价格，模拟真实交易者行为
3. 根据选定模式执行有方向性的交易，自然地影响价格走势
4. 避免机械化交易模式，增强交易行为的自然性
"""

import time
import random
import decimal
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import logging

from .base_strategy import BaseStrategy


class TradingMode(Enum):
    """交易模式枚举"""
    BULL = "bull"          # 拉盘模式
    BEAR = "bear"          # 砸盘模式  
    SIDEWAYS = "sideways"  # 横盘模式


class OrderSize(Enum):
    """订单规模枚举"""
    SMALL = "small"        # 小订单
    LARGE = "large"        # 大订单


@dataclass
class TradingAction:
    """交易动作数据结构"""
    is_buy: bool                    # 是否买单
    target_price: Decimal           # 目标价格
    quantity: Decimal               # 订单数量
    order_size_type: OrderSize      # 订单规模类型
    reason: str                     # 执行原因


@dataclass
class ModeConfig:
    """模式配置数据结构"""
    buy_ratio: float               # 买单比例
    sell_ratio: float              # 卖单比例
    buy_price_offset_range: Tuple[float, float]   # 买单价格偏移范围
    sell_price_offset_range: Tuple[float, float]  # 卖单价格偏移范围
    quantity_multiplier_range: Tuple[float, float]  # 数量乘数范围


class AdaptiveKlineStrategy(BaseStrategy):
    """自适应K线操盘策略"""
    
    def __init__(self, config: Dict[str, Any], nine_client, logger: logging.Logger):
        super().__init__(config, nine_client, logger)
        
        # 基础配置
        self.trading_pair = self._get_config_value("AK_TRADING_PAIR", "TST/USDT", str)
        self.mode = TradingMode(self._get_config_value("AK_MODE", "bull", str))
        
        # 订单金额配置
        self.base_order_value = Decimal(self._get_config_value("AK_ORDER_VALUE", "0.5", str))  # USDT价值
        self.min_trade_value = Decimal(self._get_config_value("AK_MIN_TRADE_VALUE", "0.1", str))  # USDT价值
        
        # 随机化配置
        self.time_randomness = float(self._get_config_value("AK_TIME_RANDOMNESS", "0.3", str))
        self.quantity_randomness = float(self._get_config_value("AK_QUANTITY_RANDOMNESS", "0.4", str))
        self.price_offset_max = float(self._get_config_value("AK_PRICE_OFFSET_MAX", "0.005", str))
        
        # 自然交易模拟配置
        self.rest_probability = float(self._get_config_value("AK_REST_PROBABILITY", "0.1", str))
        self.burst_probability = float(self._get_config_value("AK_BURST_PROBABILITY", "0.05", str))
        self.burst_count_min = int(self._get_config_value("AK_BURST_COUNT_MIN", "2", str))
        self.burst_count_max = int(self._get_config_value("AK_BURST_COUNT_MAX", "4", str))
        self.large_order_ratio = float(self._get_config_value("AK_LARGE_ORDER_RATIO", "0.3", str))
        self.large_order_multiplier_min = float(self._get_config_value("AK_LARGE_ORDER_MULTIPLIER_MIN", "1.5", str))
        self.large_order_multiplier_max = float(self._get_config_value("AK_LARGE_ORDER_MULTIPLIER_MAX", "2.5", str))
        
        # 时间配置
        self.base_interval = float(self._get_config_value("AK_BASE_INTERVAL", "45", str))
        self.min_interval = float(self._get_config_value("AK_MIN_INTERVAL", "15", str))
        self.max_interval = float(self._get_config_value("AK_MAX_INTERVAL", "120", str))
        
        # 风险控制配置
        self.max_single_order_value = Decimal(self._get_config_value("AK_MAX_SINGLE_ORDER_VALUE", "1000", str))
        self.max_trades_per_hour = int(self._get_config_value("AK_MAX_TRADES_PER_HOUR", "60", str))
        self.price_deviation_alert = float(self._get_config_value("AK_PRICE_DEVIATION_ALERT", "0.05", str))
        
        # 订单管理配置
        self.cancel_all_orders = self._get_config_value("AK_CANCEL_ALL_ORDERS", "true", str).lower() == "true"
        self.order_max_age = float(self._get_config_value("AK_ORDER_MAX_AGE", "300", str))
        self.price_tolerance = float(self._get_config_value("AK_PRICE_TOLERANCE", "0.03", str))
        
        
        # 精度配置（系统规定）
        self.price_precision = int(self._get_config_value("AK_PRICE_PRECISION", "8", str))
        self.qty_precision = int(self._get_config_value("AK_QTY_PRECISION", "2", str))
        
        # API凭证（使用通用配置）
        self.api_key = self._get_config_value("NINE_API_KEY")
        self.api_secret = self._get_config_value("NINE_API_SECRET")
        
        # 状态管理
        self.last_execution_time: float = 0.0
        self.burst_remaining: int = 0  # 剩余连续执行次数
        self.trades_this_hour: int = 0
        self.hour_start_time: float = time.time()
        self.last_price: Optional[Decimal] = None
        self.order_creation_times: Dict[str, float] = {}  # 跟踪订单创建时间
        
        # 配置框架订单簿获取参数（让框架自动获取Nine订单簿数据）
        self.fetch_nine_book_symbol = self.trading_pair
        self.fetch_nine_book_precision = self._get_config_value("AK_NINE_POOL_PRECISION_API_PARAM", "0.01", str)
        self.fetch_nine_book_depth = 5  # Nine API要求最小值为5
        
        # 初始化模式配置
        self._init_mode_configs()
        
        self.logger.info(f"✅ AdaptiveKlineStrategy初始化完成")
        self.logger.info(f"📈 交易对: {self.trading_pair}, 模式: {self.mode.value}")
        self.logger.info(f"💰 基础订单: {self.base_order_value}U, 最小金额: {self.min_trade_value}U")
        if self.cancel_all_orders:
            self.logger.info(f"🗑️ 订单管理: 传统模式 (每次全量撤单)")
        else:
            self.logger.info(f"🧠 订单管理: 智能模式 (订单寿命{self.order_max_age:.0f}s, 价格容忍度{self.price_tolerance:.1%})")
        
        # 配置验证
        self._validate_configuration()
    
    def _init_mode_configs(self):
        """初始化各种模式的配置"""
        self.mode_configs = {
            TradingMode.BULL: ModeConfig(
                buy_ratio=float(self._get_config_value("AK_BULL_BUY_RATIO", "0.7", str)),
                sell_ratio=0.3,
                buy_price_offset_range=(0.0, 0.003),   # 买单: 0-0.3%上浮（推高价格）
                sell_price_offset_range=(0.005, 0.015), # 卖单: 0.5-1.5%上浮（高价卖出）
                quantity_multiplier_range=(1.2, 1.8)   # 120-180%
            ),
            TradingMode.BEAR: ModeConfig(
                buy_ratio=0.3,
                sell_ratio=float(self._get_config_value("AK_BEAR_SELL_RATIO", "0.7", str)),
                buy_price_offset_range=(-0.015, -0.005), # 买单: 0.5-1.5%下浮（低价买入）
                sell_price_offset_range=(0.0, 0.003),   # 卖单: 0-0.3%上浮（略高挂单但施压）
                quantity_multiplier_range=(1.2, 1.8)    # 120-180%
            ),
            TradingMode.SIDEWAYS: ModeConfig(
                buy_ratio=float(self._get_config_value("AK_SIDEWAYS_BALANCE", "0.5", str)),
                sell_ratio=0.5,
                buy_price_offset_range=(-0.008, -0.002),  # 买单: 0.2-0.8%下浮
                sell_price_offset_range=(0.002, 0.008),   # 卖单: 0.2-0.8%上浮
                quantity_multiplier_range=(0.8, 1.2)      # 平衡: 80-120%
            )
        }
    
    def _validate_configuration(self):
        """验证策略配置"""
        if self.mode not in self.mode_configs:
            raise ValueError(f"不支持的交易模式: {self.mode}")
        
        if self.base_order_value <= 0:
            raise ValueError("基础订单金额必须大于0")
        
        if not (0 <= self.rest_probability <= 1):
            raise ValueError("休息概率必须在0-1之间")
        
        if not (0 <= self.burst_probability <= 1):
            raise ValueError("爆发概率必须在0-1之间")
        
        self.logger.info("✅ 配置验证通过")
    
    def get_actions(self,
                    current_active_orders: Optional[List[Dict]] = None,
                    nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """生成自适应K线操盘动作"""
        actions = []
        current_time = time.time()
        
        # 更新每小时交易计数
        self._update_hourly_trade_count(current_time)
        
        # 检查是否超过每小时交易限制
        if self.trades_this_hour >= self.max_trades_per_hour:
            self.logger.info(f"⏱️ 已达到每小时最大交易次数限制: {self.max_trades_per_hour}")
            return actions
        
        # 智能订单管理：根据配置决定撤单策略
        orders_to_cancel = self._get_orders_to_cancel(current_active_orders, current_time)
        if orders_to_cancel:
            actions.append({
                "action_type": "cancel",
                "order_ids": orders_to_cancel,
                "reason": f"{self.mode.value}模式智能撤单: {len(orders_to_cancel)}个订单"
            })
        
        # 检查执行时机
        if not self._should_execute_now(current_time):
            elapsed = current_time - self.last_execution_time if self.last_execution_time > 0 else 0
            random_interval = self._calculate_random_interval()
            self.logger.debug(f"⏰ 时间间隔未到: 已过{elapsed:.1f}s, 需要{random_interval:.1f}s")
            return actions
        
        # 检查是否应该休息
        if self._should_rest():
            self.logger.info(f"😴 随机休息，跳过本次执行")
            self._update_execution_time(current_time)
            return actions
        
        # 获取市场数据
        if not nine_cex_order_book_data:
            self.logger.warning("⚠️ 订单簿数据缺失")
            self._update_execution_time(current_time)
            return actions
        
        # 解析市场价格
        current_price = self._get_current_price(nine_cex_order_book_data)
        if not current_price:
            self.logger.warning("⚠️ 无法获取当前价格")
            self._update_execution_time(current_time)
            return actions
        
        self.logger.info(f"💰 [{self.mode.value}] 当前价格: {current_price}")
        
        # 价格异常检查
        if not self._is_price_normal(current_price):
            self.logger.warning(f"⚠️ 价格异常，暂停交易: {current_price}")
            self._update_execution_time(current_time)
            return actions
        
        # 确定交易动作
        trading_actions = self._determine_trading_actions(current_price, nine_cex_order_book_data)
        
        # 生成订单
        if trading_actions:
            orders_to_place = self._create_orders(trading_actions)
            if orders_to_place:
                action_count = len(orders_to_place)
                actions.append({
                    "action_type": "place",
                    "orders": orders_to_place,
                    "reason": f"{self.mode.value}模式自适应交易: {action_count}个订单"
                })
                
                # 记录新订单的创建时间（使用订单特征作为临时标识）
                self._record_order_creation_by_signature(orders_to_place, current_time)
                
                self.trades_this_hour += action_count
                self.logger.info(f"🎯 [{self.mode.value}] 生成{action_count}个订单")
        
        # 处理爆发模式
        if self._should_burst():
            self.burst_remaining = random.randint(self.burst_count_min, self.burst_count_max) - 1
            self.logger.info(f"💥 触发爆发模式，剩余{self.burst_remaining}次连续执行")
        
        # 更新执行时间
        self._update_execution_time(current_time)
        self.last_price = current_price
        
        return actions
    
    def _get_orders_to_cancel(self, current_active_orders: Optional[List[Dict]], current_time: float) -> List[str]:
        """智能撤单：根据配置决定哪些订单需要撤销"""
        if not current_active_orders:
            return []
        
        # 更新订单创建时间映射（从签名转换为orderId）
        self._update_order_creation_times(current_active_orders)
        
        # 如果配置为全量撤单，直接返回所有订单ID
        if self.cancel_all_orders:
            order_ids = [order['orderId'] for order in current_active_orders if order.get('orderId')]
            self.logger.debug(f"🗑️ 全量撤单模式: 撤销{len(order_ids)}个订单")
            # 清理被撤销订单的时间记录
            self._cleanup_cancelled_orders(order_ids)
            return order_ids
        
        # 智能撤单模式：分析哪些订单需要撤销
        orders_to_cancel = []
        
        for order in current_active_orders:
            order_id = order.get('orderId')
            if not order_id:
                continue
                
            should_cancel = False
            cancel_reason = ""
            
            # 检查1：订单存活时间
            order_create_time = self.order_creation_times.get(order_id)
            if order_create_time:
                order_age = current_time - order_create_time
                if order_age > self.order_max_age:
                    should_cancel = True
                    cancel_reason = f"订单过期({order_age:.0f}s>{self.order_max_age:.0f}s)"
            
            # 检查2：价格偏离度（如果有当前价格）
            if not should_cancel and self.last_price:
                try:
                    order_price = Decimal(str(order.get('price', 0)))
                    order_direction = order.get('direction', 0)  # 1=买, 2=卖
                    
                    if order_price > 0:
                        price_deviation = abs(order_price - self.last_price) / self.last_price
                        
                        # 检查价格是否偏离过大
                        if price_deviation > Decimal(str(self.price_tolerance)):
                            should_cancel = True
                            cancel_reason = f"价格偏离过大({price_deviation:.2%}>{self.price_tolerance:.2%})"
                        
                        # 检查买卖方向是否与当前模式冲突
                        elif not self._is_order_aligned_with_mode(order_direction, order_price):
                            should_cancel = True
                            cancel_reason = "与当前模式冲突"
                            
                except (ValueError, TypeError, decimal.DecimalException):
                    # 订单数据异常，撤销
                    should_cancel = True
                    cancel_reason = "订单数据异常"
            
            if should_cancel:
                orders_to_cancel.append(order_id)
                self.logger.debug(f"📋 撤销订单 {order_id}: {cancel_reason}")
        
        if orders_to_cancel:
            self.logger.info(f"🧹 智能撤单: 撤销{len(orders_to_cancel)}/{len(current_active_orders)}个订单")
            # 清理被撤销订单的时间记录
            self._cleanup_cancelled_orders(orders_to_cancel)
        else:
            self.logger.debug(f"✅ 智能撤单: 所有{len(current_active_orders)}个订单均符合策略，保留")
            
        return orders_to_cancel
    
    def _cleanup_cancelled_orders(self, cancelled_order_ids: List[str]):
        """清理被撤销订单的时间记录，防止内存泄漏"""
        for order_id in cancelled_order_ids:
            if order_id in self.order_creation_times:
                del self.order_creation_times[order_id]
                self.logger.debug(f"🧹 清理撤销订单时间记录: {order_id}")
    
    def _is_order_aligned_with_mode(self, order_direction: int, order_price: Decimal) -> bool:
        """检查订单是否与当前模式对齐"""
        if not self.last_price:
            return True
            
        is_buy_order = (order_direction == 1)
        price_ratio = order_price / self.last_price
        
        # 根据不同模式检查订单是否合理
        if self.mode == TradingMode.BULL:
            # 拉盘模式：偏好买单，买单价格不应过低，卖单价格不应过高
            if is_buy_order:
                return price_ratio >= Decimal("0.98")  # 买单价格不低于当前价格2%
            else:
                return price_ratio <= Decimal("1.05")  # 卖单价格不高于当前价格5%
                
        elif self.mode == TradingMode.BEAR:
            # 砸盘模式：偏好卖单，卖单价格不应过高，买单价格不应过低
            if is_buy_order:
                return price_ratio >= Decimal("0.95")  # 买单价格不低于当前价格5%
            else:
                return price_ratio <= Decimal("1.02")  # 卖单价格不高于当前价格2%
                
        else:  # SIDEWAYS
            # 横盘模式：买卖单价格都应该在合理范围内
            return Decimal("0.95") <= price_ratio <= Decimal("1.05")
    
    
    def _record_order_creation_by_signature(self, orders: List[Dict], current_time: float):
        """通过订单特征记录创建时间（订单创建时还没有orderId）"""
        for order in orders:
            try:
                # 使用订单的核心特征生成临时标识
                signature = self._generate_order_signature(order)
                if signature:
                    self.order_creation_times[signature] = current_time
                    self.logger.debug(f"📝 记录订单创建时间: {signature}")
            except Exception as e:
                self.logger.error(f"❌ 记录订单创建时间失败: {e}")
    
    def _generate_order_signature(self, order: Dict) -> Optional[str]:
        """生成订单特征签名"""
        try:
            # 从 TradeBean 中提取关键信息
            trading_pair = order.get('tradePairName', '')
            direction = order.get('orderDirection', 0)
            quantity = order.get('orderQuantity', '0')
            price = order.get('orderPrice', '0')
            
            if not all([trading_pair, direction, quantity != '0', price != '0']):
                return None
                
            # 生成唯一签名（精度处理避免浮点误差）
            signature = f"{trading_pair}_{direction}_{quantity}_{price}"
            return signature
        except Exception as e:
            self.logger.error(f"❌ 生成订单签名失败: {e}")
            return None
    
    def _update_order_creation_times(self, current_active_orders: Optional[List[Dict]]):
        """将签名映射转换为orderId映射，并清理过期记录"""
        if not current_active_orders:
            return
            
        # 创建新的orderId到时间的映射
        new_order_times = {}
        matched_signatures = set()
        
        for order in current_active_orders:
            order_id = order.get('orderId')
            if not order_id:
                continue
                
            # 尝试通过订单特征匹配创建时间
            signature = self._generate_order_signature_from_active_order(order)
            if signature and signature in self.order_creation_times:
                new_order_times[order_id] = self.order_creation_times[signature]
                matched_signatures.add(signature)
                self.logger.debug(f"🔗 匹配订单: {signature} -> {order_id}")
        
        # 清理已匹配的签名记录
        for signature in matched_signatures:
            del self.order_creation_times[signature]
            
        # 更新为orderId映射
        for order_id, create_time in new_order_times.items():
            self.order_creation_times[order_id] = create_time
            
        # 清理无法匹配的旧签名记录（防止内存泄漏）
        current_time = time.time()
        signatures_to_remove = []
        for signature, create_time in self.order_creation_times.items():
            if current_time - create_time > self.order_max_age * 2:  # 超过两倍最大年龄就清理
                signatures_to_remove.append(signature)
                
        for signature in signatures_to_remove:
            del self.order_creation_times[signature]
            self.logger.debug(f"🧹 清理过期签名记录: {signature}")
    
    def _generate_order_signature_from_active_order(self, order: Dict) -> Optional[str]:
        """从活跃订单生成签名（用于匹配）"""
        try:
            # 从活跃订单中提取信息，字段可能不同
            trading_pair = order.get('tradePairName') or order.get('symbol', '')
            direction = order.get('direction') or order.get('orderDirection', 0)
            quantity = str(order.get('quantity', '0')) or str(order.get('orderQuantity', '0'))
            price = str(order.get('price', '0')) or str(order.get('orderPrice', '0'))
            
            if not all([trading_pair, direction, quantity != '0', price != '0']):
                return None
                
            signature = f"{trading_pair}_{direction}_{quantity}_{price}"
            return signature
        except Exception as e:
            self.logger.error(f"❌ 从活跃订单生成签名失败: {e}")
            return None
    
    def _should_execute_now(self, current_time: float) -> bool:
        """检查是否应该立即执行"""
        # 爆发模式：立即执行
        if self.burst_remaining > 0:
            self.burst_remaining -= 1
            return True
        
        # 正常模式：检查时间间隔
        if self.last_execution_time == 0:
            return True
        
        # 计算随机化的执行间隔
        random_interval = self._calculate_random_interval()
        elapsed_time = current_time - self.last_execution_time
        
        return elapsed_time >= random_interval
    
    def _calculate_random_interval(self) -> float:
        """计算随机化的执行间隔"""
        # 基础间隔 ± 随机性
        randomness_range = self.base_interval * self.time_randomness
        random_offset = random.uniform(-randomness_range, randomness_range)
        random_interval = self.base_interval + random_offset
        
        # 应用最小和最大间隔限制
        return max(self.min_interval, min(self.max_interval, random_interval))
    
    def _should_rest(self) -> bool:
        """检查是否应该休息（概率性跳过）"""
        return random.random() < self.rest_probability
    
    def _should_burst(self) -> bool:
        """检查是否应该爆发（连续执行）"""
        return self.burst_remaining == 0 and random.random() < self.burst_probability
    
    def _update_execution_time(self, current_time: float):
        """更新执行时间"""
        self.last_execution_time = current_time
    
    def _update_hourly_trade_count(self, current_time: float):
        """更新每小时交易计数"""
        if current_time - self.hour_start_time >= 3600:  # 1小时
            self.trades_this_hour = 0
            self.hour_start_time = current_time
    
    def _get_current_price(self, order_book_data: Optional[Dict]) -> Optional[Decimal]:
        """获取当前市场价格（基于框架传递的完整订单簿数据）"""
        try:
            if not order_book_data:
                self.logger.debug("📊 未收到订单簿数据")
                return None
            
            self.logger.debug(f"📊 收到订单簿数据，顶层键: {list(order_book_data.keys())}")
            
            # 检查是否为完整的business_data格式（包含orderDepth和quote）
            if "orderDepth" in order_book_data:
                order_depth = order_book_data["orderDepth"]
                quote_info = order_book_data.get("quote", {})
                
                # 优先使用quote中的最新成交价
                if quote_info and isinstance(quote_info, dict):
                    latest_price_str = quote_info.get("n")  # n字段是最新成交价
                    if latest_price_str and latest_price_str != "0" and latest_price_str != "0.00000000":
                        price = Decimal(str(latest_price_str))
                        self.logger.info(f"✅ 从quote.n获取最新成交价: {price}")
                        return price
                    
                    # 备用：使用quote中的其他价格字段
                    for price_field in ['price', 'p', 'last_price']:
                        if quote_info.get(price_field):
                            price = Decimal(str(quote_info[price_field]))
                            self.logger.info(f"✅ 从quote.{price_field}获取价格: {price}")
                            return price
                
                # 使用orderDepth计算中间价
                if order_depth and isinstance(order_depth, dict):
                    asks = order_depth.get('asks', [])
                    bids = order_depth.get('bids', [])
                    
                    if asks and bids and len(asks) > 0 and len(bids) > 0:
                        best_ask = Decimal(str(asks[0][0]))
                        best_bid = Decimal(str(bids[0][0]))
                        mid_price = (best_ask + best_bid) / Decimal("2")
                        self.logger.info(f"✅ 从orderDepth中间价获取: {mid_price} (买1:{best_bid}, 卖1:{best_ask})")
                        return mid_price
                
                self.logger.info(f"📊 orderDepth结构: asks={len(asks) if asks else 0}, bids={len(bids) if bids else 0}")
                self.logger.info(f"📊 quote信息: {quote_info}")
            
            # 兼容旧格式：直接包含asks/bids的数据
            elif "asks" in order_book_data and "bids" in order_book_data:
                asks = order_book_data.get('asks', [])
                bids = order_book_data.get('bids', [])
                
                if asks and bids and len(asks) > 0 and len(bids) > 0:
                    best_ask = Decimal(str(asks[0][0]))
                    best_bid = Decimal(str(bids[0][0]))
                    mid_price = (best_ask + best_bid) / Decimal("2")
                    self.logger.info(f"✅ 从旧格式中间价获取: {mid_price} (买1:{best_bid}, 卖1:{best_ask})")
                    return mid_price
                
                self.logger.info(f"📊 旧格式结构: asks={len(asks)}, bids={len(bids)}")
            
            else:
                self.logger.warning(f"📊 未知的订单簿数据格式: {list(order_book_data.keys())}")
        
        except Exception as e:
            self.logger.warning(f"⚠️ 价格获取失败: {e}")
        
        self.logger.warning("⚠️ 无法从订单簿数据获取价格")
        return None
    
    def _is_price_normal(self, current_price: Decimal) -> bool:
        """检查价格是否正常（防止异常波动）"""
        if not self.last_price:
            return True
        
        price_change_ratio = abs(current_price - self.last_price) / self.last_price
        return price_change_ratio <= Decimal(str(self.price_deviation_alert))
    
    def _determine_trading_actions(self, current_price: Decimal, order_book_data: Dict) -> List[TradingAction]:
        """根据模式确定交易动作"""
        actions = []
        mode_config = self.mode_configs[self.mode]
        
        # 决定是否下买单
        if random.random() < mode_config.buy_ratio:
            buy_action = self._create_buy_action(current_price, order_book_data, mode_config)
            if buy_action:
                actions.append(buy_action)
        
        # 决定是否下卖单
        if random.random() < mode_config.sell_ratio:
            sell_action = self._create_sell_action(current_price, order_book_data, mode_config)
            if sell_action:
                actions.append(sell_action)
        
        return actions
    
    def _create_buy_action(self, current_price: Decimal, order_book_data: Dict, mode_config: ModeConfig) -> Optional[TradingAction]:
        """创建买单动作"""
        return self._create_trading_action(current_price, order_book_data, mode_config, is_buy=True)
    
    def _create_sell_action(self, current_price: Decimal, order_book_data: Dict, mode_config: ModeConfig) -> Optional[TradingAction]:
        """创建卖单动作"""
        return self._create_trading_action(current_price, order_book_data, mode_config, is_buy=False)
    
    def _create_trading_action(self, current_price: Decimal, order_book_data: Dict, mode_config: ModeConfig, is_buy: bool) -> Optional[TradingAction]:
        """创建交易动作的通用方法"""
        try:
            # 确定订单规模类型
            is_large_order = random.random() < self.large_order_ratio
            order_size_type = OrderSize.LARGE if is_large_order else OrderSize.SMALL
            
            # 计算目标价格（根据买卖方向使用相应的偏移范围）
            price_offset_range = mode_config.buy_price_offset_range if is_buy else mode_config.sell_price_offset_range
            price_offset = random.uniform(price_offset_range[0], price_offset_range[1])
            target_price = current_price * (Decimal("1") + Decimal(str(price_offset)))
            
            # 确保价格为正数
            if target_price <= 0:
                order_type = "买单" if is_buy else "卖单"
                self.logger.warning(f"计算出的{order_type}价格无效: {target_price}")
                return None
            
            # 计算订单数量
            quantity = self._calculate_order_quantity(target_price, mode_config, is_large_order)
            
            # 确保数量为正数
            if quantity <= 0:
                order_type = "买单" if is_buy else "卖单"
                self.logger.warning(f"计算出的{order_type}数量无效: {quantity}")
                return None
            
            # 检查订单价值是否满足最小要求
            order_value = quantity * target_price
            if order_value >= self.min_trade_value:
                order_type = "买单" if is_buy else "卖单"
                self.logger.debug(f"创建{order_type}: 价格{target_price}（偏移{price_offset:+.3%}），数量{quantity}，价值{order_value:.2f}U")
                return TradingAction(
                    is_buy=is_buy,
                    target_price=target_price,
                    quantity=quantity,
                    order_size_type=order_size_type,
                    reason=f"{self.mode.value}模式{'买单' if is_buy else '卖单'}"
                )
            else:
                order_type = "买单" if is_buy else "卖单"
                self.logger.debug(f"{order_type}价值过小: {order_value:.3f}U < {self.min_trade_value}U")
        
        except Exception as e:
            order_type = "买单" if is_buy else "卖单"
            self.logger.error(f"创建{order_type}动作失败: {e}")
        
        return None
    
    def _calculate_order_quantity(self, target_price: Decimal, mode_config: ModeConfig, is_large_order: bool) -> Decimal:
        """计算订单数量（基于USDT价值）"""
        # 基础订单价值（USDT）
        base_value = self.base_order_value
        
        # 应用模式配置的价值乘数
        mode_multiplier = random.uniform(mode_config.quantity_multiplier_range[0], mode_config.quantity_multiplier_range[1])
        order_value = base_value * Decimal(str(mode_multiplier))
        
        # 如果是大订单，再次放大
        if is_large_order:
            large_multiplier = random.uniform(self.large_order_multiplier_min, self.large_order_multiplier_max)
            order_value *= Decimal(str(large_multiplier))
        
        # 应用随机性
        randomness_factor = random.uniform(1 - self.quantity_randomness, 1 + self.quantity_randomness)
        order_value *= Decimal(str(randomness_factor))
        
        # 根据目标价格计算基础资产数量
        quantity = order_value / target_price
        
        return quantity
    
    def _create_orders(self, trading_actions: List[TradingAction]) -> List[Dict]:
        """根据交易动作创建订单"""
        orders = []
        
        for action in trading_actions:
            try:
                # 应用精度格式化
                formatted_price = self._format_price(action.target_price, self.price_precision)
                formatted_quantity = self._format_quantity(action.quantity, self.qty_precision)
                
                # 验证格式化后的值
                if not formatted_price or not formatted_quantity or formatted_price == "0" or formatted_quantity == "0":
                    self.logger.error(f"格式化后的值无效: 价格={formatted_price}, 数量={formatted_quantity}")
                    continue
                
                # 风险检查：单笔订单价值不能过大
                order_value = action.target_price * action.quantity
                if order_value > self.max_single_order_value:
                    # 缩小订单到最大允许价值
                    max_quantity = self.max_single_order_value / action.target_price
                    formatted_quantity = self._format_quantity(max_quantity, self.qty_precision)
                    self.logger.debug(f"订单价值过大，调整数量: {action.quantity} -> {max_quantity}")
                
                # 最终验证：确保调整后的数量仍然有效
                if not formatted_quantity or formatted_quantity == "0":
                    self.logger.error(f"调整后的数量无效: {formatted_quantity}")
                    continue
                
                # 创建订单Bean
                order_direction = 1 if action.is_buy else 2  # 1=买入, 2=卖出
                
                trade_bean = self.create_trade_bean(
                    trading_pair=self.trading_pair,
                    order_direction=order_direction,
                    order_quantity=formatted_quantity,
                    order_price=formatted_price
                )
                
                orders.append(trade_bean)
                
                self.logger.info(f"✅ 订单详情: {'买' if action.is_buy else '卖'} {formatted_quantity} @ {formatted_price} ({action.order_size_type.value}) = {order_value:.2f}U")
                self.logger.debug(f"📋 TradeBean: {trade_bean}")
            
            except Exception as e:
                self.logger.error(f"❌ 创建订单失败: {e}")
        
        return orders
    
    def _pre_execution_checks(self, current_time: float, actions: List[Dict[str, Any]]) -> bool:
        """执行前置检查"""
        # 更新每小时交易计数
        self._update_hourly_trade_count(current_time)
        
        # 检查是否超过每小时交易限制
        if self.trades_this_hour >= self.max_trades_per_hour:
            self.logger.info(f"⏱️ 已达到每小时最大交易次数限制: {self.max_trades_per_hour}")
            return False
        
        return True
    
    def _handle_order_cancellation(self, current_active_orders: Optional[List[Dict]], actions: List[Dict[str, Any]]):
        """处理现有订单撤销"""
        if current_active_orders:
            order_ids_to_cancel = [order['orderId'] for order in current_active_orders if order.get('orderId')]
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids_to_cancel,
                    "reason": f"{self.mode.value}模式动态调整撤单"
                })
    
    def _timing_checks(self, current_time: float) -> bool:
        """执行时机检查"""
        # 检查执行时机
        if not self._should_execute_now(current_time):
            elapsed = current_time - self.last_execution_time if self.last_execution_time > 0 else 0
            random_interval = self._calculate_random_interval()
            self.logger.debug(f"⏰ 时间间隔未到: 已过{elapsed:.1f}s, 需要{random_interval:.1f}s")
            return False
        
        # 检查是否应该休息
        if self._should_rest():
            self.logger.info(f"😴 随机休息，跳过本次执行")
            self._update_execution_time(current_time)
            return False
        
        return True
    
    def _validate_market_data(self, nine_cex_order_book_data: Optional[Dict], current_time: float) -> Optional[Decimal]:
        """获取和验证市场数据"""
        # 获取市场数据
        if not nine_cex_order_book_data:
            self.logger.warning("⚠️ 订单簿数据缺失")
            self._update_execution_time(current_time)
            return None
        
        # 解析市场价格
        current_price = self._get_current_price(nine_cex_order_book_data)
        if not current_price:
            self.logger.warning("⚠️ 无法获取当前价格")
            self._update_execution_time(current_time)
            return None
        
        self.logger.info(f"💰 [{self.mode.value}] 当前价格: {current_price}")
        
        # 价格异常检查
        if not self._is_price_normal(current_price):
            self.logger.warning(f"⚠️ 价格异常，暂停交易: {current_price}")
            self._update_execution_time(current_time)
            return None
        
        return current_price
    
