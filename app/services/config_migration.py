"""
配置迁移工具
帮助用户从大型env文件迁移到分层配置
"""

import os
import shutil
from typing import Dict, List


class ConfigMigrationTool:
    """配置迁移工具"""
    
    def __init__(self, project_dir: str):
        self.project_dir = project_dir
        self.old_env_path = os.path.join(project_dir, 'env.example')
        self.new_env_path = os.path.join(project_dir, '.env.new')
        self.user_env_path = os.path.join(project_dir, '.env')
        self.strategy_dir = os.path.join(project_dir, 'config', 'strategies')
        
    def check_migration_needed(self) -> bool:
        """检查是否需要迁移"""
        # 如果用户正在使用旧的大型配置文件
        if os.path.exists(self.user_env_path):
            with open(self.user_env_path, 'r') as f:
                content = f.read()
                # 如果包含大量策略配置，建议迁移
                if len(content.split('\n')) > 100:
                    return True
        return False
    
    def backup_current_config(self) -> str:
        """备份当前配置"""
        if not os.path.exists(self.user_env_path):
            return ""
            
        backup_path = f"{self.user_env_path}.backup"
        shutil.copy2(self.user_env_path, backup_path)
        return backup_path
    
    def migrate_to_layered_config(self) -> Dict[str, str]:
        """执行迁移到分层配置"""
        result = {
            "status": "success",
            "message": "配置迁移完成",
            "backup_path": "",
            "changes": []
        }
        
        try:
            # 1. 备份现有配置
            if os.path.exists(self.user_env_path):
                result["backup_path"] = self.backup_current_config()
                result["changes"].append(f"已备份现有配置到: {result['backup_path']}")
            
            # 2. 使用新的精简配置
            if os.path.exists(self.new_env_path):
                shutil.copy2(self.new_env_path, self.user_env_path)
                result["changes"].append("已应用精简版主配置文件")
            
            # 3. 创建策略配置目录
            os.makedirs(self.strategy_dir, exist_ok=True)
            result["changes"].append(f"已创建策略配置目录: {self.strategy_dir}")
            
            # 4. 提示用户下一步操作
            result["next_steps"] = [
                "1. 编辑 .env 文件，配置你的API密钥",
                "2. 如需使用其他策略，查看 config/strategies/ 目录",
                "3. 使用命令：flask start-trading-bot --strategy self_managed_kline --dry-run 测试"
            ]
            
        except Exception as e:
            result["status"] = "error"
            result["message"] = f"迁移失败: {str(e)}"
        
        return result
    
    def show_migration_guide(self) -> str:
        """显示迁移指南"""
        guide = """
# 配置迁移指南

## 当前问题
- env.example 文件过长 (591行, 197个参数)
- 新用户配置复杂，容易出错
- 维护困难

## 解决方案：分层配置
- 主配置 (.env): 仅包含基础配置 + 推荐策略 (<50行)
- 策略配置 (config/strategies/): 各策略独立文件
- 自动加载: 程序根据策略选择自动加载相关配置

## 迁移步骤
1. 运行: python -c "from app.services.config_migration import ConfigMigrationTool; tool = ConfigMigrationTool('.'); result = tool.migrate_to_layered_config(); print(result)"
2. 编辑 .env 文件，配置API密钥
3. 测试运行: flask start-trading-bot --strategy self_managed_kline --dry-run

## 优势
- 新手友好: 只需关注4个核心参数
- 维护简单: 策略配置独立管理
- 向后兼容: 支持现有配置方式
- 扩展性好: 新策略只需添加独立配置文件
"""
        return guide


def migrate_config(project_dir: str = ".") -> Dict[str, str]:
    """便捷函数：执行配置迁移"""
    tool = ConfigMigrationTool(project_dir)
    return tool.migrate_to_layered_config()


if __name__ == "__main__":
    # 可以直接运行此脚本进行迁移
    import sys
    project_dir = sys.argv[1] if len(sys.argv) > 1 else "."
    
    tool = ConfigMigrationTool(project_dir)
    
    if tool.check_migration_needed():
        print("检测到需要配置迁移...")
        result = tool.migrate_to_layered_config()
        print(f"迁移结果: {result}")
    else:
        print("当前配置无需迁移")
        print(tool.show_migration_guide())