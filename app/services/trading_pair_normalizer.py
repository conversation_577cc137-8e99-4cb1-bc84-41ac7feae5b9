#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易对格式标准化器 - KISS原则实现
统一处理所有交易对格式转换，消除代码重复
"""

import re
import logging
from typing import Optional, Tuple, Dict, Any
from dataclasses import dataclass

@dataclass
class NormalizedPair:
    """标准化后的交易对信息"""
    base: str           # 基础币：BTC, ETH, 二哈
    quote: str          # 计价币：USDT, SEPUSDT  
    standard: str       # 标准格式：BTC/USDT
    api_base: str       # API用基础币名：BTC, 二哈
    api_quote: str      # API用计价币：USDT
    pair_id: Optional[str] = None  # Nine CEX交易对ID

class TradingPairNormalizer:
    """
    交易对格式标准化器
    
    功能：
    1. 统一解析各种交易对格式
    2. 标准化为 BASE/USDT 格式
    3. 处理API调用所需的格式转换
    4. 维护一致的命名规范
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # 计价币映射：统一到USDT
        self.quote_mapping = {
            'USDT': 'USDT',
            'SEPUSDT': 'USDT',  # Nine CEX特有计价币统一到USDT
            'USD': 'USDT',
            'BUSD': 'USDT'
        }
        
        # 反向映射：Nine CEX API实际使用的计价币
        self.api_quote_mapping = {
            'USDT': 'USDT',
            'SEPUSDT': 'SEPUSDT'
        }
    
    def normalize(self, trading_pair: str) -> Optional[NormalizedPair]:
        """
        标准化交易对格式
        
        Args:
            trading_pair: 原始交易对 (如 "BTC/USDT", "NINE/SEPUSDT", "二哈/USDT")
            
        Returns:
            NormalizedPair: 标准化后的交易对信息
        """
        if not trading_pair or not isinstance(trading_pair, str):
            self.logger.warning(f"⚠️ [交易对标准化] 无效输入: {trading_pair}")
            return None
        
        # 清理输入
        trading_pair = trading_pair.strip()
        
        # 解析格式
        if '/' in trading_pair:
            parts = trading_pair.split('/')
            if len(parts) != 2:
                self.logger.warning(f"⚠️ [交易对标准化] 格式错误: {trading_pair}")
                return None
            base, quote = parts[0].strip(), parts[1].strip()
        else:
            # 假设是基础币，默认配USDT
            base, quote = trading_pair.strip(), 'USDT'
        
        # 标准化计价币
        standard_quote = self.quote_mapping.get(quote.upper(), 'USDT')
        
        # 创建标准格式
        standard_pair = f"{base}/{standard_quote}"
        
        # 生成API调用用的名称
        api_base = base
        api_quote = quote  # 保持原始API格式
        
        result = NormalizedPair(
            base=base,
            quote=standard_quote,
            standard=standard_pair,
            api_base=api_base,
            api_quote=api_quote
        )
        
        self.logger.debug(f"🔄 [交易对标准化] {trading_pair} -> {standard_pair}")
        return result
    
    def parse_config_format(self, config_value: str) -> Optional[NormalizedPair]:
        """
        解析配置文件中的交易对格式
        支持: BTC/USDT, NINE/SEPUSDT, 二哈/USDT 等
        """
        return self.normalize(config_value)
    
    def to_api_format(self, normalized: NormalizedPair, api_type: str = 'nine_cex') -> Dict[str, str]:
        """
        转换为特定API所需的格式
        
        Args:
            normalized: 标准化交易对
            api_type: API类型 ('nine_cex', 'binance', etc.)
            
        Returns:
            dict: API调用参数
        """
        if api_type == 'nine_cex':
            return {
                'trade_pair_name': f"{normalized.api_base}/{normalized.api_quote}",
                'base_name': normalized.api_base,
                'quote_name': normalized.api_quote
            }
        elif api_type == 'nine_cex_order_book':
            # 特殊处理：订单簿API只需要基础币名
            return {
                'trade_pair_name': normalized.api_base,  # 只要基础币名
                'base_name': normalized.api_base,
                'quote_name': normalized.api_quote
            }
        else:
            # 默认格式
            return {
                'trade_pair_name': normalized.standard,
                'base_name': normalized.base,
                'quote_name': normalized.quote
            }
    
    def validate_format(self, trading_pair: str) -> Tuple[bool, str]:
        """
        验证交易对格式是否符合标准
        
        Returns:
            (is_valid, message)
        """
        normalized = self.normalize(trading_pair)
        if not normalized:
            return False, "无法解析交易对格式"
        
        # 检查是否符合推荐格式
        if normalized.quote != 'USDT':
            return True, f"建议使用USDT计价币，当前: {normalized.quote}"
        
        return True, "格式正确"
    
    def get_supported_formats(self) -> Dict[str, str]:
        """返回支持的格式示例"""
        return {
            "标准格式": "BTC/USDT",
            "Nine CEX格式": "NINE/SEPUSDT", 
            "中文代币": "二哈/USDT",
            "单币种": "BTC (默认/USDT)"
        }