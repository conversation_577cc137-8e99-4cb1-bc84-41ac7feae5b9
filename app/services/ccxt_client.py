"""
CCXT API客户端
用于获取中心化交易所的价格数据
"""

import ccxt
import logging
import statistics
from typing import Optional, Dict, Any, List
from decimal import Decimal


class CCXTClient:
    """CEX 价格客户端"""
    
    def __init__(self, config: Optional[Dict] = None, logger: Optional[logging.Logger] = None):
        """
        初始化CCXT客户端
        
        Args:
            config: 配置字典
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.config = config or {}
        
        # 从配置获取交易所列表
        exchanges_str = self.config.get('CPB_EXCHANGES', 'binance,okx')
        self.exchange_ids = [e.strip() for e in exchanges_str.split(',')]
        
        # 价格聚合方式
        self.aggregation = self.config.get('CPB_PRICE_AGGREGATION', 'median')
        
        # 初始化交易所实例
        self.exchanges = {}
        for exchange_id in self.exchange_ids:
            try:
                exchange_class = getattr(ccxt, exchange_id)
                
                # 配置交易所参数
                exchange_config = {
                    'enableRateLimit': True,
                    'timeout': 10000,
                    'options': {'defaultType': 'spot'},
                    'sandbox': False,  # 使用正式环境
                }
                
                # 检查环境变量中的代理设置
                import os
                http_proxy = os.environ.get('http_proxy') or os.environ.get('HTTP_PROXY')
                https_proxy = os.environ.get('https_proxy') or os.environ.get('HTTPS_PROXY')
                
                # 如果有代理设置，添加到配置中
                proxies = {}
                if http_proxy:
                    proxies['http'] = http_proxy
                if https_proxy:
                    proxies['https'] = https_proxy
                
                if proxies:
                    exchange_config['proxies'] = proxies
                    self.logger.debug(f"🌐 [{exchange_id}] 使用代理: {list(proxies.values())}")
                
                self.exchanges[exchange_id] = exchange_class(exchange_config)
                self.logger.info(f"📊 [CEX] {exchange_id} 客户端初始化完成")
            except AttributeError:
                self.logger.error(f"❌ [CEX] 不支持的交易所: {exchange_id}")
            except Exception as e:
                self.logger.warning(f"⚠️ [CEX] {exchange_id} 初始化失败: {e}")
        
        if not self.exchanges:
            self.logger.error("❌ [CEX] 没有可用的交易所客户端")
            raise ValueError("没有可用的交易所客户端")
            
        self.logger.info(f"🌊 [CEX] 客户端初始化完成，支持交易所: {list(self.exchanges.keys())}")
    
    def get_token_price(self, token_input: str, quote_symbol: str = "USDT") -> Optional[Decimal]:
        """
        获取代币价格（保持与 RaydiumClient 相同的接口）
        
        Args:
            token_input: 代币符号（如 BTC, ETH, SEP）
            quote_symbol: 计价币种（默认USDT）
            
        Returns:
            Decimal: 代币价格，获取失败返回None
        """
        self.logger.info(f"🌐 [CEX] 开始获取价格: {token_input}/{quote_symbol}")
        
        # 构建 CCXT 符号格式
        symbol = f"{token_input}/{quote_symbol}"
        prices = []
        success_exchanges = []
        
        # 从各交易所获取价格
        for exchange_id, exchange in self.exchanges.items():
            try:
                # 设置较短的超时时间，避免长时间等待
                exchange.timeout = 5000  # 5秒超时
                
                # 获取ticker数据
                ticker = exchange.fetch_ticker(symbol)
                
                if ticker and ticker.get('last') and ticker['last'] > 0:
                    price = Decimal(str(ticker['last']))
                    prices.append(price)
                    success_exchanges.append(exchange_id)
                    self.logger.debug(f"✅ [{exchange_id}] {symbol} = ${price:.8f}")
                else:
                    self.logger.debug(f"⚠️ [{exchange_id}] 无效的价格数据: {ticker}")
                    
            except ccxt.RequestTimeout as e:
                self.logger.debug(f"⏰ [{exchange_id}] 请求超时: {str(e)[:100]}...")
            except ccxt.NetworkError as e:
                self.logger.debug(f"🌐 [{exchange_id}] 网络错误: {str(e)[:100]}...")
            except ccxt.ExchangeError as e:
                self.logger.debug(f"📊 [{exchange_id}] 交易所错误: {str(e)[:100]}...")
            except Exception as e:
                self.logger.debug(f"⚠️ [{exchange_id}] 获取失败: {str(e)[:100]}...")
        
        # 聚合价格
        if not prices:
            self.logger.warning(f"⚠️ [CEX] 无法从任何交易所获取 {symbol} 价格")
            self.logger.warning(f"💡 [CEX] 请检查网络连接和代理设置")
            return None
        
        # 执行价格聚合
        final_price = self._aggregate_prices(prices)
        
        self.logger.info(f"✅ [CEX] 聚合价格({self.aggregation}): ${final_price:.8f} (来源: {success_exchanges})")
        return final_price
    
    def _aggregate_prices(self, prices: List[Decimal]) -> Decimal:
        """
        聚合价格数据
        
        Args:
            prices: 价格列表
            
        Returns:
            Decimal: 聚合后的价格
        """
        if not prices:
            raise ValueError("价格列表为空")
            
        if len(prices) == 1:
            return prices[0]
        
        if self.aggregation == 'median':
            return Decimal(str(statistics.median(prices)))
        elif self.aggregation == 'average':
            return sum(prices) / len(prices)
        elif self.aggregation == 'max':
            return max(prices)
        elif self.aggregation == 'min':
            return min(prices)
        elif self.aggregation == 'first':
            return prices[0]
        else:
            # 默认使用中位数
            return Decimal(str(statistics.median(prices)))
    
    def get_pool_info(self, token_symbol: str, quote_symbol: str = "USDT") -> Optional[Dict[str, Any]]:
        """
        获取流动性信息（兼容接口，CEX场景下返回基本信息）
        
        Args:
            token_symbol: 代币符号
            quote_symbol: 计价币种
            
        Returns:
            dict: 基本的市场信息
        """
        try:
            symbol = f"{token_symbol}/{quote_symbol}"
            
            # 从第一个可用交易所获取详细信息
            for exchange_id, exchange in self.exchanges.items():
                try:
                    ticker = exchange.fetch_ticker(symbol)
                    
                    if ticker:
                        return {
                            "price": Decimal(str(ticker.get('last', 0))),
                            "volume_24h": Decimal(str(ticker.get('quoteVolume', 0))),
                            "liquidity": Decimal(str(ticker.get('quoteVolume', 0))),  # 使用成交量作为流动性指标
                            "base_volume": Decimal(str(ticker.get('baseVolume', 0))),
                            "exchange": exchange_id,
                            "high_24h": Decimal(str(ticker.get('high', 0))),
                            "low_24h": Decimal(str(ticker.get('low', 0))),
                            "change_24h": Decimal(str(ticker.get('change', 0) or 0))
                        }
                except Exception as e:
                    self.logger.debug(f"从 {exchange_id} 获取详细信息失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ [CEX] 获取市场信息异常: {e}")
            return None
    
    def test_connection(self) -> Dict[str, bool]:
        """
        测试各交易所连接状态
        
        Returns:
            dict: 各交易所的连接状态
        """
        results = {}
        
        for exchange_id, exchange in self.exchanges.items():
            try:
                # 尝试获取BTC/USDT价格作为连接测试
                ticker = exchange.fetch_ticker('BTC/USDT')
                results[exchange_id] = ticker is not None and ticker.get('last') is not None
                
                if results[exchange_id]:
                    self.logger.info(f"✅ [{exchange_id}] 连接正常")
                else:
                    self.logger.warning(f"⚠️ [{exchange_id}] 连接异常：无有效数据")
                    
            except Exception as e:
                results[exchange_id] = False
                self.logger.warning(f"⚠️ [{exchange_id}] 连接失败: {e}")
        
        return results