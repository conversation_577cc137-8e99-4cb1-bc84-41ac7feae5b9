#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易对管理器 - 处理交易对名称和ID之间的映射

主要功能:
1. 从Nine CEX API获取交易对列表和ID映射
2. 缓存交易对信息，减少API调用
3. 提供名称到ID的转换服务
4. 支持配置文件中使用交易对名称的向后兼容
"""

import time
import json
import logging
from typing import Dict, Optional, List, Any
from threading import Lock
from .trading_pair_normalizer import TradingPairNormalizer, NormalizedPair

class TradingPairManager:
    """交易对管理器类"""
    
    def __init__(self, nine_client, logger: Optional[logging.Logger] = None):
        """
        初始化交易对管理器
        
        Args:
            nine_client: Nine CEX 客户端实例
            logger: 日志记录器
        """
        self.nine_client = nine_client
        self.logger = logger or logging.getLogger(__name__)
        
        # 交易对格式标准化器 (KISS原则)
        self.normalizer = TradingPairNormalizer(logger)
        
        # 缓存数据
        self._name_to_id_cache: Dict[str, str] = {}
        self._id_to_info_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 缓存5分钟
        self._lock = Lock()

        # 解析去重机制：记录最近解析的交易对，避免重复日志
        self._recent_resolutions: Dict[str, float] = {}  # pair_name -> timestamp
        self._resolution_log_ttl = 60  # 60秒内不重复记录解析日志

        # 执行会话缓存：避免单次执行中多次刷新
        self._session_cache_refreshed = False
        self._session_start_time = 0
        self._session_ttl = 30  # 30秒内视为同一执行会话
        
        # 默认API凭证 - 从配置中获取
        self._default_api_key = None
        self._default_api_secret = None
    
    def set_default_credentials(self, api_key: str, api_secret: str):
        """设置默认API凭证"""
        self._default_api_key = api_key
        self._default_api_secret = api_secret
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        return time.time() - self._cache_timestamp < self._cache_ttl

    def _log_resolution_success(self, pair_name: str, pair_id: str):
        """智能记录交易对解析成功日志，避免短时间内重复记录"""
        current_time = time.time()

        # 检查是否在短时间内已经记录过相同的解析
        if pair_name in self._recent_resolutions:
            last_log_time = self._recent_resolutions[pair_name]
            if current_time - last_log_time < self._resolution_log_ttl:
                # 短时间内已记录过，跳过日志
                return

        # 记录解析成功日志
        self.logger.info(f"交易对解析: {pair_name} -> {pair_id}")
        self._recent_resolutions[pair_name] = current_time

        # 清理过期的记录，避免内存泄漏
        expired_pairs = [
            name for name, timestamp in self._recent_resolutions.items()
            if current_time - timestamp > self._resolution_log_ttl * 2
        ]
        for name in expired_pairs:
            del self._recent_resolutions[name]

    def _is_same_session(self) -> bool:
        """检查是否在同一执行会话中"""
        current_time = time.time()
        return (self._session_cache_refreshed and
                current_time - self._session_start_time < self._session_ttl)

    def _start_new_session(self):
        """开始新的执行会话"""
        self._session_start_time = time.time()
        self._session_cache_refreshed = False
    
    def _refresh_cache(self, api_key: Optional[str] = None, api_secret: Optional[str] = None) -> bool:
        """
        刷新交易对缓存
        
        Args:
            api_key: API密钥，如果不提供则使用默认值
            api_secret: API密钥，如果不提供则使用默认值
            
        Returns:
            bool: 刷新是否成功
        """
        with self._lock:
            # 使用提供的凭证或默认凭证
            use_api_key = api_key or self._default_api_key
            use_api_secret = api_secret or self._default_api_secret
            
            if not use_api_key or not use_api_secret:
                self.logger.error("❌ [交易对管理器] 缺少API凭证，无法获取交易对信息")
                return False
            
            try:
                # 清空缓存
                self._name_to_id_cache.clear()
                self._id_to_info_cache.clear()
                
                self.logger.debug("🔄 [交易对管理器] 正在刷新交易对缓存...")
                
                # 查询多个常见的稳定币组（stable coin groups）
                stable_coin_groups = ["SEPUSDT", "USDT"]  
                total_pairs_loaded = 0
                
                for stable_coin in stable_coin_groups:
                    self.logger.debug(f"🔍 [交易对管理器] 查询稳定币组: {stable_coin}")
                    
                    try:
                        group_pairs_loaded = 0
                        
                        # 查询所有类型的交易对：内盘(0)和外盘(1)
                        for trade_pair_type in [0, 1]:
                            type_name = "内盘" if trade_pair_type == 0 else "外盘"
                            self.logger.debug(f"🔍 [交易对管理器] 查询 {stable_coin} 组的{type_name}交易对")
                            
                            # 支持分页获取所有交易对数据
                            page_num = 1
                            
                            while True:
                                # 获取以该稳定币为计价货币的交易对
                                data = self.nine_client.get_outer_pool_info_by_group(
                                    user_api_key=use_api_key,
                                    user_api_secret=use_api_secret,
                                    group_name=stable_coin,  # 稳定币组名
                                    trade_pair_type=trade_pair_type,  # 内盘(0)或外盘(1)
                                    page_num=page_num,
                                    page_size=100            # 修复: 使用标准的 pageSize=100 (符合 need.md 规范)
                                )

                                if not data or 'list' not in data or not data['list']:
                                    if page_num == 1:
                                        self.logger.debug(f"🔍 [交易对管理器] {stable_coin} 组的{type_name}交易对第 {page_num} 页无数据")
                                    break

                                # 构建交易对映射（内盘和外盘）
                                for item in data['list']:
                                    if 's' in item and 'id' in item:
                                        base_token = item['s']        # 基础代币名称，如 "Fufufaf", "Party", "NINE"
                                        pair_id = item['id']          # 交易对ID，如 "20250617000029"
                                        quote_currency = stable_coin  # 计价货币，如 "SEPUSDT", "USDT"

                                        # 构建标准的交易对名称：基础代币/计价货币
                                        standard_pair_name = f"{base_token}/{quote_currency}"  # 如 "Fufufaf/SEPUSDT"

                                        # 建立多种格式的映射关系，支持不同的配置格式
                                        mappings_to_add = [
                                            base_token,              # "Fufufaf" -> ID (单独代币名)
                                            standard_pair_name,      # "Fufufaf/SEPUSDT" -> ID (标准格式)
                                        ]

                                        # 如果是 SEPUSDT 组，也支持 /USDT 的简写格式
                                        if quote_currency == "SEPUSDT":
                                            mappings_to_add.append(f"{base_token}/USDT")  # "Fufufaf/USDT" -> ID

                                        # 存储所有映射关系
                                        for mapping_name in mappings_to_add:
                                            self._name_to_id_cache[mapping_name] = pair_id
                                            self.logger.debug(f"📋 [交易对管理器] 添加映射: {mapping_name} -> {pair_id}")

                                        # 存储完整的交易对信息（使用ID作为key）
                                        self._id_to_info_cache[pair_id] = {
                                            **item,
                                            'standard_name': standard_pair_name,  # 标准交易对名称
                                            'base_token': base_token,             # 基础代币名称
                                            'quote_currency': quote_currency,     # 计价货币
                                            'stable_coin_group': stable_coin,     # 稳定币组
                                            'trade_pair_type': trade_pair_type    # 交易对类型
                                        }

                                current_page_count = len(data['list'])
                                group_pairs_loaded += current_page_count

                                self.logger.debug(f"📄 [交易对管理器] {stable_coin} 组{type_name}第 {page_num} 页: {current_page_count} 个交易对")

                                # 检查是否还有更多页面
                                if 'pages' in data and page_num >= data['pages']:
                                    break
                                elif current_page_count < 100:  # 如果当前页数据少于 pageSize，说明是最后一页
                                    break
                                else:
                                    page_num += 1

                        if group_pairs_loaded > 0:
                            total_pairs_loaded += group_pairs_loaded
                            self.logger.debug(f"✅ [交易对管理器] 稳定币组 {stable_coin} 加载了 {group_pairs_loaded} 个外盘交易对")
                    
                    except Exception as e:
                        self.logger.warning(f"⚠️ [交易对管理器] 查询稳定币组 {stable_coin} 失败: {e}")
                        continue
                
                self._cache_timestamp = time.time()
                self.logger.info(f"交易对缓存加载: {len(self._name_to_id_cache)} 个")

                # 输出映射统计信息（调试级别）
                if self._name_to_id_cache:
                    sample_mappings = list(self._name_to_id_cache.items())[:5]
                    self.logger.debug(f"📋 [交易对管理器] 映射示例:")
                    for name, id in sample_mappings:
                        self.logger.debug(f"    {name} -> {id}")
                
                return total_pairs_loaded > 0
                
            except Exception as e:
                self.logger.error(f"❌ [交易对管理器] 刷新缓存失败: {e}")
                return False
    
    def get_pair_id(self, pair_name: str, api_key: Optional[str] = None, api_secret: Optional[str] = None) -> Optional[str]:
        """
        根据交易对名称获取交易对ID

        Args:
            pair_name: 交易对名称，如 "SEPBTC/SEPUSDT"、"Fufufaf/SEPUSDT" 或 "Party/USDT"
            api_key: API密钥，如果不提供则使用默认值
            api_secret: API密钥，如果不提供则使用默认值

        Returns:
            str: 交易对ID，如 "20250617000029"，如果未找到返回None

        Raises:
            ValueError: 当必须获取ID但API调用失败时
        """
        # 智能缓存策略：考虑缓存有效期和执行会话
        need_refresh = (not self._is_cache_valid() or
                       not self._name_to_id_cache or
                       (not self._is_same_session() and not self._name_to_id_cache))

        if need_refresh and not self._is_same_session():
            # 开始新会话或缓存已过期，需要刷新
            self._start_new_session()

            max_retries = 3
            for retry in range(max_retries):
                try:
                    if self._refresh_cache(api_key, api_secret):
                        self.logger.debug(f"缓存刷新成功 (尝试 {retry + 1}/{max_retries})")
                        self._session_cache_refreshed = True
                        break
                    else:
                        self.logger.warning(f"缓存刷新失败 (尝试 {retry + 1}/{max_retries})")
                        if retry < max_retries - 1:
                            import time
                            time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    self.logger.error(f"❌ [交易对管理器] 缓存刷新异常 (尝试 {retry + 1}/{max_retries}): {e}")
                    if retry < max_retries - 1:
                        import time
                        time.sleep(2)

            # 如果所有重试都失败，抛出异常
            if not self._name_to_id_cache:
                error_msg = f"无法从getOuterPoolInfo获取交易对映射，API可能不可用。请检查网络连接和API服务器状态"
                self.logger.error(f"❌ [交易对管理器] {error_msg}")
                raise ValueError(error_msg)
        
        # 尝试多种格式查找
        # 使用normalizer获取基础币名称（避免重复）
        normalized = self.normalizer.normalize(pair_name)
        base_name = normalized.base if normalized else pair_name
        
        search_names = [
            pair_name,  # 原始名称，如 "Fufufaf/SEPUSDT"
            base_name,  # 基础币种名称，如 "Fufufaf"
        ]
        
        # 如果原始名称不包含"/"，尝试添加常见的计价货币
        if '/' not in pair_name:
            search_names.extend([
                f"{pair_name}/SEPUSDT",
                f"{pair_name}/USDT"
            ])
        
        # 尝试精确匹配
        for name in search_names:
            if name in self._name_to_id_cache:
                pair_id = self._name_to_id_cache[name]
                self._log_resolution_success(pair_name, pair_id)
                return pair_id
        
        # 尝试模糊匹配：在所有缓存的键中查找包含基础代币名称的
        base_token = base_name  # 使用前面已经标准化的base_name
        for cached_name in self._name_to_id_cache.keys():
            # 检查是否为基础代币名称的完全匹配
            cached_normalized = self.normalizer.normalize(cached_name)
            cached_base = cached_normalized.base if cached_normalized else cached_name
            if cached_base.upper() == base_token.upper():
                pair_id = self._name_to_id_cache[cached_name]
                self._log_resolution_success(pair_name, pair_id)
                # 将这个映射添加到缓存中，方便下次查找
                self._name_to_id_cache[pair_name] = pair_id
                return pair_id
        
        # 输出错误信息
        self.logger.error(f"❌ [交易对管理器] 未找到交易对: {pair_name}")
        self.logger.debug(f"❌ [交易对管理器] 查找的名称格式: {search_names}")
        self.logger.debug(f"❌ [交易对管理器] 当前缓存包含 {len(self._name_to_id_cache)} 个交易对映射")

        # 输出当前缓存的所有交易对用于调试（调试级别）
        if self._name_to_id_cache:
            all_pairs = list(self._name_to_id_cache.keys())
            self.logger.debug(f"❌ [交易对管理器] 所有可用交易对: {all_pairs}")
        
        return None
    
    def get_pair_info(self, pair_id: str) -> Optional[Dict[str, Any]]:
        """
        根据交易对ID获取完整的交易对信息
        
        Args:
            pair_id: 交易对ID
            
        Returns:
            dict: 交易对完整信息，如果未找到返回None
        """
        if not self._is_cache_valid():
            self._refresh_cache()
        
        return self._id_to_info_cache.get(pair_id)
    
    def list_all_pairs(self) -> Dict[str, str]:
        """
        获取所有交易对的名称到ID映射
        
        Returns:
            dict: 名称到ID的映射字典
        """
        if not self._is_cache_valid():
            self._refresh_cache()
        
        return self._name_to_id_cache.copy()
    
    def is_pair_id(self, value: str) -> bool:
        """
        判断给定的字符串是否是交易对ID格式
        
        Args:
            value: 要判断的字符串
            
        Returns:
            bool: 是否是交易对ID格式
        """
        # 交易对ID格式通常是 "20250617000029" (14位数字)
        return value.isdigit() and len(value) >= 12
    
    def resolve_pair_identifier(self, identifier: str, api_key: Optional[str] = None, api_secret: Optional[str] = None) -> str:
        """
        解析交易对标识符，自动判断是名称还是ID，并返回ID
        
        Args:
            identifier: 交易对标识符（名称或ID）
            api_key: API密钥
            api_secret: API密钥
            
        Returns:
            str: 交易对ID
            
        Raises:
            ValueError: 当无法获取正确的交易对ID时
        """
        # 如果已经是ID格式，直接返回
        if self.is_pair_id(identifier):
            self.logger.debug(f"🆔 [交易对管理器] 输入已是ID格式: {identifier}")
            return identifier
        
        # 否则必须从API获取ID
        self.logger.debug(f"🔍 [交易对管理器] 开始解析交易对名称: {identifier}")
        pair_id = self.get_pair_id(identifier, api_key, api_secret)
        
        if not pair_id:
            error_msg = f"无法从getOuterPoolInfo获取交易对 {identifier} 的正确ID。请确保交易对名称正确且已在Nine CEX上线"
            self.logger.error(f"❌ [交易对管理器] {error_msg}")
            raise ValueError(error_msg)

        # 使用智能日志记录，避免重复
        self._log_resolution_success(identifier, pair_id)
        return pair_id
    
    def normalize_and_resolve(self, trading_pair: str, api_key: Optional[str] = None, api_secret: Optional[str] = None) -> Optional[NormalizedPair]:
        """
        统一入口：标准化交易对格式并解析ID (KISS原则)
        
        这是推荐的统一接口，所有新策略都应使用此方法
        
        Args:
            trading_pair: 交易对字符串 (如 "BTC/USDT", "NINE/SEPUSDT", "二哈/USDT")
            api_key: API密钥
            api_secret: API密钥
            
        Returns:
            NormalizedPair: 包含标准格式和ID的完整交易对信息
        """
        # 1. 标准化格式
        normalized = self.normalizer.normalize(trading_pair)
        if not normalized:
            self.logger.error(f"❌ [交易对管理器] 无法标准化交易对: {trading_pair}")
            return None
        
        try:
            # 2. 解析交易对ID
            pair_id = self.get_pair_id(normalized.standard, api_key, api_secret)
            if not pair_id:
                # 尝试原格式
                pair_id = self.get_pair_id(trading_pair, api_key, api_secret)
            
            if pair_id:
                normalized.pair_id = pair_id
                self.logger.debug(f"✅ [交易对管理器] 统一解析成功: {trading_pair} -> {normalized.standard} (ID: {pair_id})")
                return normalized
            else:
                self.logger.error(f"❌ [交易对管理器] 未找到交易对ID: {trading_pair}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ [交易对管理器] 解析失败: {e}")
            return None