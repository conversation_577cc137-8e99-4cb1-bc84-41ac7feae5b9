import requests
import json
import os # 用于备选的从环境变量读取
from flask import current_app, g # 确保导入 current_app 和 g
import time # 用于可能的签名
import hmac # 用于可能的签名
import hashlib # 用于可能的签名
from typing import Optional, List, Dict

class BinanceClient:
    # BASE_URL 将从配置中读取
    # # 币安官方 API 的基础 URL (可以根据需要选择主站或备用)
    # BASE_URL = "https://api.binance.com" 
    # # 如果是测试网，可以使用: "https://testnet.binance.vision"

    def __init__(self, base_url=None, api_key=None, api_secret=None):
        """
        币安服务客户端初始化。

        Args:
            base_url (str, optional): 币安 API 的基础 URL。
            api_key (str, optional): 币安 API Key。
            api_secret (str, optional): 币安 API Secret。
        """
        if base_url:
            self.base_url = base_url
        elif hasattr(g, 'binance_base_url'): # 尝试从 Flask g 对象获取 (如果已设置)
            self.base_url = g.binance_base_url
        else: # 作为最后的备选，从 current_app.config 获取 (如果应用上下文可用)
            try:
                self.base_url = current_app.config.get("BINANCE_API_URL", "https://api.binance.com")
            except RuntimeError: # 如果没有应用上下文 (例如，直接运行脚本进行单元测试)
                self.base_url = os.environ.get("BINANCE_API_URL", "https://api.binance.com")
        
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        # 只有在提供 API Key 时才添加认证头（用于私有端点）
        if self.api_key:
            self.session.headers.update({'X-MBX-APIKEY': self.api_key})

        # --- 显式代理控制 ---
        proxies = {}
        http_proxy_env = os.environ.get('HTTP_PROXY') or os.environ.get('http_proxy')
        https_proxy_env = os.environ.get('HTTPS_PROXY') or os.environ.get('https_proxy')

        if http_proxy_env:
            proxies['http'] = http_proxy_env
        if https_proxy_env:
            proxies['https'] = https_proxy_env
        
        # 移除代理配置的详细日志输出

    def _create_signature(self, params):
        """
        为需要认证的端点创建签名。
        对于公开端点，不需要签名，直接返回原参数。
        """
        if not self.api_secret:
            # 没有 API Secret，适用于公开端点，直接返回参数
            return params 
        
        # 为私有端点创建 HMAC SHA256 签名
        # 使用 urllib.parse.urlencode 来确保正确的 URL 编码
        from urllib.parse import urlencode
        query_string = urlencode(sorted(params.items()))
        signature = hmac.new(self.api_secret.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
        params['signature'] = signature
        return params

    def _request(self, endpoint: str, params: dict = None, method: str = "GET", require_signature: bool = None) -> dict:
        """
        内部方法，用于发送 HTTP 请求到币安 API。

        Args:
            endpoint (str): API 端点路径 (例如 '/api/v3/depth')。
            params (dict, optional): 请求参数。
            method (str, optional): HTTP 方法。默认为 "GET"。
            require_signature (bool, optional): 是否需要签名。如果为 None，会根据端点自动判断。

        Returns:
            dict: 解析后的 JSON 响应数据。

        Raises:
            ConnectionError: 如果 HTTP 请求失败或币安 API 返回错误。
            ValueError: 如果 JSON 响应解码失败。
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        actual_params = params.copy() if params else {}

        # 定义公开端点（不需要签名）
        public_endpoints = [
            "/api/v3/depth",           # 订单簿
            "/api/v3/ticker/price",    # 价格信息
            "/api/v3/ticker/24hr",     # 24小时统计
            "/api/v3/klines",          # K线数据
            "/api/v3/exchangeInfo",    # 交易规则和交易对信息
            "/api/v3/time",            # 服务器时间
        ]
        
        # 如果没有明确指定，根据端点判断是否需要签名
        if require_signature is None:
            require_signature = endpoint not in public_endpoints

        headers = {}
        if require_signature:
            if not self.api_key or not self.api_secret:
                raise ValueError(f"访问私有端点 {endpoint} 需要API key和secret，但未提供。")
            
            # 添加 API Key 到请求头
            headers['X-MBX-APIKEY'] = self.api_key
            
            # 为 POST 请求添加 Content-Type
            if method == "POST":
                headers['Content-Type'] = 'application/x-www-form-urlencoded'
            
            # 添加时间戳
            actual_params['timestamp'] = int(time.time() * 1000)
            
            if method == "GET":
                actual_params = self._create_signature(actual_params)
            elif method == "POST":
                actual_params = self._create_signature(actual_params)

        try:
            if method == "POST" and require_signature:
                # 对于 POST 请求，参数应该在请求体中，而不是查询字符串中
                # 必须使用与签名计算相同的编码方式
                from urllib.parse import urlencode
                body_data = urlencode(sorted(actual_params.items()))
                response = self.session.request(method, url, data=body_data, headers=headers, timeout=10)
            else:
                # 对于 GET 请求或公开端点，参数在查询字符串中
                response = self.session.request(method, url, params=actual_params, headers=headers, timeout=10)
            response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则引发 HTTPError
            
            if not response.content:
                # Empty response received; this might be unexpected for some endpoints (e.g., order book).
                raise ValueError(f"从 {method} {url} 收到空响应，状态码: {response.status_code}")

            return response.json()
        except requests.exceptions.HTTPError as http_err:
            # 尝试解析币安的错误响应体
            error_details = ""
            try:
                error_data = response.json()
                error_details = f" (Code: {error_data.get('code')}, Message: {error_data.get('msg')})"
            except json.JSONDecodeError:
                error_details = f" (无法解析的错误响应体: {response.text[:200]}...)"
            raise ConnectionError(f"请求到 {url} 时发生HTTP错误: {response.status_code}, 响应: {error_details}") from http_err
        except requests.exceptions.RequestException as req_err:
            raise ConnectionError(f"请求到 {url} 时发生错误: {req_err}") from req_err
        except json.JSONDecodeError as json_err:
            raise ValueError(f"从 {url} 解码 JSON 响应失败: {json_err}. 响应内容: {response.text[:200]}...")

    def get_order_book(self, symbol: str, limit: int = 100) -> dict:
        """
        获取指定交易对的订单薄。
        API 端点: /api/v3/depth

        Args:
            symbol (str): 交易对标识，例如 "BTCUSDT"。
            limit (int, optional): 返回的深度档位数。默认 100。
                                   有效值: [5, 10, 20, 50, 100, 500, 1000, 5000]。
                                   权重会根据 limit 增加。

        Returns:
            dict: 包含订单薄买单 (bids) 和卖单 (asks) 的字典。
                  例如: {'lastUpdateId': ..., 'bids': [[price, qty], ...], 'asks': [[price, qty], ...]}
        """
        if not symbol:
            raise ValueError("交易对 (symbol) 不能为空。")
        
        valid_limits = [5, 10, 20, 50, 100, 500, 1000, 5000]
        if limit not in valid_limits:
            raise ValueError(f"无效的 limit 参数: {limit}。有效值是: {valid_limits}")

        params = {
            "symbol": symbol.upper(), # 币安交易对通常是大写
            "limit": limit
        }
        return self._request("/api/v3/depth", params=params)

    def create_order(self, symbol: str, side: str, order_type: str, quantity: float, price: Optional[float] = None, time_in_force: Optional[str] = "GTC") -> dict:
        """
        创建新订单。
        API 端点: POST /api/v3/order (HMAC SHA256)

        Args:
            symbol (str): 交易对，例如 "BTCUSDT"。
            side (str): "BUY" 或 "SELL"。
            order_type (str): 订单类型，例如 "LIMIT", "MARKET", "STOP_LOSS_LIMIT"等。
                               对于限价单，使用 "LIMIT"。
            quantity (float): 订单数量。
            price (float, optional): 订单价格。对于LIMIT订单是必需的。
            time_in_force (str, optional): 有效期机制。 "GTC" (Good Til Cancelled), 
                                           "IOC" (Immediate Or Cancel), "FOK" (Fill Or Kill)。
                                           对于LIMIT订单，通常是 "GTC"。

        Returns:
            dict: API 的响应，包含订单创建结果。
        
        Raises:
            ValueError: 如果参数无效或缺少API Key/Secret。
        """
        if not self.api_key or not self.api_secret:
            raise ValueError("创建订单需要API key和secret，但未提供。")
        if not symbol:
            raise ValueError("交易对 (symbol) 不能为空。")
        if side.upper() not in ["BUY", "SELL"]:
            raise ValueError("无效的订单方向 (side)，必须是 'BUY' 或 'SELL'。")
        if order_type.upper() not in ["LIMIT", "MARKET", "STOP_LOSS", "STOP_LOSS_LIMIT", "TAKE_PROFIT", "TAKE_PROFIT_LIMIT", "LIMIT_MAKER"]: # 根据币安API文档添加更多类型
            raise ValueError(f"不支持的订单类型: {order_type}。常见的有 'LIMIT', 'MARKET'。")

        params = {
            "symbol": symbol.upper(),
            "side": side.upper(),
            "type": order_type.upper(),
            "quantity": str(quantity),  # 数量也需要是字符串
        }

        if order_type.upper() == "LIMIT":
            if price is None:
                raise ValueError("对于LIMIT订单，价格 (price) 是必需的。")
            params["price"] = str(price) # 价格需要是字符串
            params["timeInForce"] = time_in_force.upper() if time_in_force else "GTC"
        
        # 其他订单类型可能需要不同或额外的参数 (例如市价单不需要price, 止损单需要stopPrice)
        # 此处主要关注限价单 (LIMIT)

        # _request 方法会自动处理签名和时间戳
        return self._request(endpoint="/api/v3/order", params=params, method="POST")

    def get_order_info(self, symbol: str, order_id: Optional[str] = None, orig_client_order_id: Optional[str] = None) -> dict:
        """
        查询订单状态。
        API 端点: GET /api/v3/order (HMAC SHA256)
        至少需要 order_id 或 orig_client_order_id 中的一个。

        Args:
            symbol (str): 交易对，例如 "BTCUSDT"。
            order_id (str, optional): 交易所分配的订单ID。
            orig_client_order_id (str, optional): 用户自定义的订单ID。

        Returns:
            dict: API 的响应，包含订单的详细信息。
        
        Raises:
            ValueError: 如果参数无效或缺少API Key/Secret。
        """
        if not self.api_key or not self.api_secret:
            raise ValueError("查询订单状态需要API key和secret，但未提供。")
        if not symbol:
            raise ValueError("交易对 (symbol) 不能为空。")
        if not order_id and not orig_client_order_id:
            raise ValueError("必须提供 order_id 或 orig_client_order_id 中的至少一个。")

        params = {
            "symbol": symbol.upper()
        }
        if order_id:
            params["orderId"] = order_id
        if orig_client_order_id:
            params["origClientOrderId"] = orig_client_order_id
        
        # _request 方法会自动处理签名和时间戳
        return self._request(endpoint="/api/v3/order", params=params, method="GET")

    def get_account_balance(self) -> List[Dict[str, str]]:
        """
        获取账户所有资产的余额信息。
        API 端点: GET /api/v3/account (HMAC SHA256)

        Returns:
            List[Dict[str, str]]: 包含账户各资产余额信息的列表。
            每个字典通常包含 "asset", "free", "locked" 键。
            例如: [{"asset": "BTC", "free": "0.5", "locked": "0.1"}, ...]

        Raises:
            ValueError: 如果缺少API Key/Secret。
        """
        if not self.api_key or not self.api_secret:
            raise ValueError("获取账户余额需要API key和secret，但未提供。")

        # _request 方法会自动处理签名和时间戳
        response_data = self._request(endpoint="/api/v3/account", params={}, method="GET")
        
        # API成功响应的结构中，余额信息通常在 'balances' 键下
        if response_data and isinstance(response_data, dict) and "balances" in response_data:
            if isinstance(response_data["balances"], list):
                return response_data["balances"]
            else:
                # self.logger.warning if logger is available
                print(f"BinanceClient get_account_balance: 'balances' field is not a list: {response_data['balances']}")
                return [] # 或者抛出错误，取决于严格程度
        else:
            # self.logger.error if logger is available
            print(f"BinanceClient get_account_balance: Unexpected response structure or missing 'balances' key. Response: {response_data}")
            return [] # 或者抛出错误

# --- 示例用法 (用于测试) ---
if __name__ == '__main__':
    # 测试时需要手动设置或从环境变量读取 URL, Key, Secret
    # 默认情况下，订单薄是公开数据，key/secret 不是必需的
    test_binance_api_url = os.environ.get("BINANCE_API_URL", "https://api.binance.com")
    test_binance_api_key = os.environ.get("BINANCE_API_KEY") # 可能为 None
    test_binance_api_secret = os.environ.get("BINANCE_API_SECRET") # 可能为 None

    print(f"测试 BinanceClient，使用 URL: {test_binance_api_url}")
    if test_binance_api_key:
        print("  API Key: 已配置 (隐藏)")
    else:
        print("  API Key: 未配置 (将仅访问公开端点)")

    binance_client = BinanceClient(
        base_url=test_binance_api_url, 
        api_key=test_binance_api_key, 
        api_secret=test_binance_api_secret
    )

    # 测试获取 BTCUSDT 的订单薄
    try:
        print("正在获取 BTCUSDT 订单薄 (最近5档)...")
        # 确保您的IP没有被币安API频率限制或屏蔽
        order_book_btcusdt = binance_client.get_order_book(symbol="BTCUSDT", limit=5)
        print("BTCUSDT 订单薄:")
        print(f"  最后更新ID: {order_book_btcusdt.get('lastUpdateId')}")
        print(f"  卖单 (Asks) - 前 {len(order_book_btcusdt.get('asks', []))} 档:")
        for ask in order_book_btcusdt.get('asks', [])[:5]: # 只打印前5条卖单
            print(f"    价格: {ask[0]}, 数量: {ask[1]}")
        print(f"  买单 (Bids) - 前 {len(order_book_btcusdt.get('bids', []))} 档:")
        for bid in order_book_btcusdt.get('bids', [])[:5]: # 只打印前5条买单
            print(f"    价格: {bid[0]}, 数量: {bid[1]}")

    except ValueError as ve:
        print(f"参数错误: {ve}")
    except ConnectionError as ce:
        print(f"连接或API错误: {ce}")
    except Exception as e:
        print(f"发生未知错误: {e}")

    # 测试获取 ETHBTC 的订单薄
    try:
        print("\n正在获取 ETHBTC 订单薄 (最近10档)...")
        order_book_ethbtc = binance_client.get_order_book(symbol="ETHBTC", limit=10)
        print("ETHBTC 订单薄 (部分显示):")
        print(f"  卖单数量: {len(order_book_ethbtc.get('asks', []))}, 买单数量: {len(order_book_ethbtc.get('bids', []))}")
        # print(json.dumps(order_book_ethbtc, indent=2))

    except Exception as e:
        print(f"获取 ETHBTC 订单薄时出错: {e}") 