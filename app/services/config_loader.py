"""
分层配置加载器
支持基础配置 + 按策略动态加载配置
"""

import os
from typing import Dict, Optional
from dotenv import load_dotenv
import logging


class ConfigLoader:
    """分层配置加载器"""
    
    def __init__(self, base_dir: str = None):
        self.base_dir = base_dir or os.getcwd()
        self.base_env_path = os.path.join(self.base_dir, '.env')
        self.strategy_config_dir = os.path.join(self.base_dir, 'config', 'strategies')
        
    def load_base_config(self) -> Dict[str, str]:
        """加载基础配置"""
        # 先加载基础.env文件
        if os.path.exists(self.base_env_path):
            load_dotenv(self.base_env_path)
        
        return dict(os.environ)
    
    def load_strategy_config(self, strategy_name: str) -> Dict[str, str]:
        """
        根据策略名加载对应的配置文件
        
        Args:
            strategy_name: 策略名称，如 'mirror_binance', 'self_managed_kline'
            
        Returns:
            包含策略配置的字典
        """
        # 策略名称映射到配置文件名
        strategy_file_map = {
            'mirror_binance': 'mirror_binance.env',
            'liquidity_provider': 'liquidity_provider.env',
            'enhanced_liquidity_provider': 'enhanced_liquidity_provider.env',
            'volume_kline': 'volume_kline.env',
            'cumulative_depth': 'cumulative_depth.env',
            'cross_exchange_arbitrage': 'cross_exchange_arbitrage.env',
            'gradient_liquidity': 'gradient_liquidity.env',
            'adaptive_kline': 'adaptive_kline.env',  # 自适应K线操盘策略
            'self_managed_kline': 'self_managed_kline.env',  # 独立配置文件
            'raydium_price_balance': 'raydium_price_balance.env',
            'cex_price_balance': 'cex_price_balance.env',
            'raydium_simple': 'raydium_simple.env',
            'mirror_price': 'mirror_price.env'
        }
        
        config_file = strategy_file_map.get(strategy_name)
        if not config_file:
            # 策略配置已在基础配置中，或者策略不需要额外配置
            return {}
        
        strategy_config_path = os.path.join(self.strategy_config_dir, config_file)
        
        if not os.path.exists(strategy_config_path):
            logging.warning(f"策略配置文件不存在: {strategy_config_path}")
            return {}
        
        # 加载策略特定配置
        load_dotenv(strategy_config_path, override=True)
        
        return dict(os.environ)
    
    def load_complete_config(self, strategy_name: Optional[str] = None) -> Dict[str, str]:
        """
        加载完整配置：基础配置 + 策略配置
        
        Args:
            strategy_name: 策略名称，如果为None则只加载基础配置
            
        Returns:
            完整的配置字典
        """
        # 加载基础配置
        config = self.load_base_config()
        
        # 如果指定了策略，则加载策略特定配置
        if strategy_name:
            strategy_config = self.load_strategy_config(strategy_name)
            config.update(strategy_config)
            logging.info(f"已加载策略配置: {strategy_name}")
        
        return config
    
    def get_available_strategies(self) -> list:
        """获取所有可用的策略列表"""
        strategies = ['self_managed_kline']  # 基础配置中的策略
        
        if os.path.exists(self.strategy_config_dir):
            for filename in os.listdir(self.strategy_config_dir):
                if filename.endswith('.env'):
                    strategy_name = filename[:-4]  # 移除.env扩展名
                    strategies.append(strategy_name)
        
        return strategies
    
    def create_strategy_config_from_old_env(self, old_env_path: str):
        """
        从旧的大型env文件创建分层配置文件
        这是一个迁移工具，帮助将现有配置分解
        """
        if not os.path.exists(old_env_path):
            logging.error(f"旧配置文件不存在: {old_env_path}")
            return
        
        # 确保策略配置目录存在
        os.makedirs(self.strategy_config_dir, exist_ok=True)
        
        # TODO: 实现配置文件分解逻辑
        # 这里可以添加解析旧env文件并生成新配置文件的逻辑
        logging.info("配置文件分解功能待实现")


def load_config_for_strategy(strategy_name: str = None, base_dir: str = None) -> Dict[str, str]:
    """
    便捷函数：为指定策略加载配置
    
    Args:
        strategy_name: 策略名称
        base_dir: 项目基础目录
        
    Returns:
        完整配置字典
    """
    loader = ConfigLoader(base_dir)
    return loader.load_complete_config(strategy_name)