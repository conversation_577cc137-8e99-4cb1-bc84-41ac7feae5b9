"""
Raydium API客户端
用于获取Solana链上Raydium DEX的价格数据
"""

import requests
import logging
from typing import Optional, Dict, Any
from decimal import Decimal


class RaydiumClient:
    """Raydium DEX价格客户端"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Raydium客户端
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Raydium API endpoints
        self.api_base = "https://api.raydium.io"
        self.price_endpoint = f"{self.api_base}/v2/main/price"
        self.pool_endpoint = f"{self.api_base}/v2/main/pool"
        
        # DexScreener API（主要价格源）
        self.dexscreener_api = "https://api.dexscreener.com/latest/dex/tokens"
        
        # 备用：Jupiter聚合器API（包含Raydium价格）
        self.jupiter_price_api = "https://price.jup.ag/v4/price"
        
        # 设置请求头
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'NineTradeMaker/1.0'
        })
        
        self.logger.info("🌊 [Raydium] 客户端初始化完成")
    
    def get_token_price(self, token_input: str, quote_symbol: str = "USDT") -> Optional[Decimal]:
        """
        获取代币价格
        
        Args:
            token_input: 代币符号（如 BTC, ETH）或合约地址
            quote_symbol: 计价币种（默认USDT）
            
        Returns:
            Decimal: 代币价格，获取失败返回None
        """
        self.logger.info(f"🌐 [Raydium] 开始获取价格: {token_input}/{quote_symbol}")
        try:
            # 检测输入是否为合约地址（Solana地址通常是32-44个字符的Base58字符串）
            # Base58字符集：**********************************************************
            is_contract_address = len(token_input) >= 32 and all(c in '**********************************************************' for c in token_input)
            
            if is_contract_address:
                # 直接使用合约地址获取价格
                self.logger.debug(f"🔍 [Raydium] 使用合约地址获取价格: {token_input}")
                
                # 1. 从DexScreener获取（支持合约地址）
                price = self._get_dexscreener_price_by_address(token_input)
                if price:
                    return price
                
                # 2. 从Jupiter获取（支持合约地址）
                price = self._get_jupiter_price_by_address(token_input)
                if price:
                    return price
            else:
                # 使用代币符号获取价格（保持向后兼容）
                self.logger.debug(f"🔍 [Raydium] 使用代币符号获取价格: {token_input}")
                
                # 1. 尝试从Raydium API获取（最快）
                self.logger.debug(f"🔍 [Raydium] 尝试从Raydium API获取...")
                pair_symbol = f"{token_input}-{quote_symbol}"
                price = self._get_raydium_price(pair_symbol)
                if price:
                    self.logger.info(f"✅ [Raydium] 从Raydium API获取成功: ${price:.8f}")
                    return price
                
                # 2. 尝试从Jupiter获取（备用）
                self.logger.debug(f"🔍 [Raydium] 尝试从Jupiter获取...")
                price = self._get_jupiter_price(token_input, quote_symbol)
                if price:
                    self.logger.info(f"✅ [Raydium] 从Jupiter获取成功: ${price:.8f}")
                    return price
                
                # 3. 尝试从DexScreener获取（可能较慢）
                self.logger.debug(f"🔍 [Raydium] 尝试从DexScreener获取...")
                price = self._get_dexscreener_price(token_input, quote_symbol)
                if price:
                    self.logger.info(f"✅ [Raydium] 从DexScreener获取成功: ${price:.8f}")
                    return price
            
            self.logger.warning(f"⚠️ [Raydium] 无法获取 {token_input}/{quote_symbol} 价格")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ [Raydium] 获取价格异常: {e}")
            return None
    
    def _get_raydium_price(self, pair_symbol: str) -> Optional[Decimal]:
        """从Raydium API获取价格"""
        try:
            response = self.session.get(
                self.price_endpoint,
                params={"pair": pair_symbol},
                timeout=3  # 减少超时时间
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("data"):
                    price_str = data["data"].get("price")
                    if price_str:
                        return Decimal(str(price_str))
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Raydium API调用失败: {e}")
            return None
    
    def _get_jupiter_price(self, token_symbol: str, quote_symbol: str) -> Optional[Decimal]:
        """从Jupiter聚合器获取价格（备用）"""
        try:
            # 映射常见代币到Solana地址
            token_addresses = {
                "BTC": "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E",  # Wrapped BTC
                "ETH": "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk",  # Wrapped ETH
                "SOL": "So11111111111111111111111111111111111111112",  # Native SOL
                "USDT": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
                "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            }
            
            token_address = token_addresses.get(token_symbol.upper())
            if not token_address:
                return None
            
            response = self.session.get(
                self.jupiter_price_api,
                params={"ids": token_address},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and token_address in data["data"]:
                    price_info = data["data"][token_address]
                    price = price_info.get("price")
                    if price:
                        return Decimal(str(price))
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Jupiter API调用失败: {e}")
            return None
    
    def _get_dexscreener_price_by_address(self, contract_address: str) -> Optional[Decimal]:
        """从DexScreener根据合约地址获取价格"""
        try:
            url = f"{self.dexscreener_api}/{contract_address}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                pairs = data.get("pairs", [])
                
                if pairs:
                    # 选择流动性最高的交易对
                    best_pair = max(pairs, key=lambda x: float(x.get("liquidity", {}).get("usd", 0)))
                    price_usd = best_pair.get("priceUsd")
                    
                    if price_usd:
                        price = Decimal(str(price_usd))
                        self.logger.debug(f"📊 [DexScreener] 合约地址 {contract_address[:8]}... 价格: ${price}")
                        return price
            
            return None
            
        except Exception as e:
            self.logger.debug(f"DexScreener合约地址查询失败: {e}")
            return None
    
    def _get_jupiter_price_by_address(self, contract_address: str) -> Optional[Decimal]:
        """从Jupiter根据合约地址获取价格"""
        try:
            response = self.session.get(
                self.jupiter_price_api,
                params={"ids": contract_address},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and contract_address in data["data"]:
                    price_info = data["data"][contract_address]
                    price = price_info.get("price")
                    if price:
                        price_decimal = Decimal(str(price))
                        self.logger.debug(f"📊 [Jupiter] 合约地址 {contract_address[:8]}... 价格: ${price_decimal}")
                        return price_decimal
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Jupiter合约地址查询失败: {e}")
            return None
    
    def _get_dexscreener_price(self, token_symbol: str, quote_symbol: str) -> Optional[Decimal]:
        """从DexScreener获取价格"""
        try:
            # 映射代币符号到Solana地址
            token_addresses = {
                "SOL": "So11111111111111111111111111111111111111112",
                "BTC": "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E",
                "ETH": "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk",
                "USDT": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
                "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "BONK": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
            }
            
            token_address = token_addresses.get(token_symbol.upper())
            if not token_address:
                return None
            
            url = f"{self.dexscreener_api}/{token_address}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if "pairs" in data and data["pairs"]:
                    # 寻找USDT交易对，优先选择Raydium
                    target_pairs = []
                    for pair in data["pairs"]:
                        quote_token = pair.get("quoteToken", {})
                        if quote_token.get("symbol", "").upper() == quote_symbol.upper():
                            target_pairs.append(pair)
                    
                    if target_pairs:
                        # 优先选择Raydium，其次选择流动性最高的
                        selected_pair = None
                        for pair in target_pairs:
                            if pair.get("dexId") == "raydium":
                                selected_pair = pair
                                break
                        
                        if not selected_pair:
                            # 选择流动性最高的
                            selected_pair = max(target_pairs, 
                                              key=lambda p: float(p.get("liquidity", {}).get("usd", 0)))
                        
                        price_usd = selected_pair.get("priceUsd")
                        if price_usd:
                            self.logger.debug(f"DexScreener获取价格: {token_symbol}/${price_usd} (DEX: {selected_pair.get('dexId')})")
                            return Decimal(str(price_usd))
            
            return None
            
        except Exception as e:
            self.logger.debug(f"DexScreener API调用失败: {e}")
            return None
    
    def get_pool_info(self, token_symbol: str, quote_symbol: str = "USDT") -> Optional[Dict[str, Any]]:
        """
        获取流动性池信息
        
        Args:
            token_symbol: 代币符号
            quote_symbol: 计价币种
            
        Returns:
            dict: 池子信息，包含流动性、成交量等
        """
        try:
            pair_symbol = f"{token_symbol}-{quote_symbol}"
            
            response = self.session.get(
                self.pool_endpoint,
                params={"pair": pair_symbol},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("data"):
                    pool_data = data["data"]
                    return {
                        "liquidity": Decimal(str(pool_data.get("liquidity", 0))),
                        "volume_24h": Decimal(str(pool_data.get("volume24h", 0))),
                        "price": Decimal(str(pool_data.get("price", 0))),
                        "base_reserve": Decimal(str(pool_data.get("baseReserve", 0))),
                        "quote_reserve": Decimal(str(pool_data.get("quoteReserve", 0)))
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ [Raydium] 获取池子信息异常: {e}")
            return None