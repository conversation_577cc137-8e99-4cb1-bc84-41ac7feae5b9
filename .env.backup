# --- 应用通用配置 ---
# Flask 应用密钥，用于会话安全等
SECRET_KEY=your_very_secret_flask_key_here
# Nine CEX API 的基础 URL (正确的API地址)
# NINE_API_URL=https://openapi.binineex.com
NINE_API_URL=http://3.148.112.106:9999
# 默认运行的交易策略 (可选项: mirror_binance, volume_kline, cumulative_depth)
TRADING_STRATEGY=mirror_binance
# 是否启用 Binance 客户端 (MirrorBinanceStrategy 依赖此项为 True)
ENABLE_BINANCE_CLIENT=True

# --- Binance API (如果需要 MirrorBinanceStrategy 或其他直接与 Binance 交互的策略) ---
# Binance API 基础 URL
BINANCE_API_URL=https://api.binance.com
# 您的 Binance API Key (如果策略需要访问私有端点)
BINANCE_API_KEY=W5ek3LFzvcCJGuvu72OiBgCknoXCiP7gBnzjdLz1gmTTRRW1LwAZAWXIVb3lSUjZ
# 您的 Binance API Secret (如果策略需要访问私有端点)
BINANCE_API_SECRET=eDUzP1PrwN6xST3GSoTvXE4PiLLRB25LXRIqiVF9LY7pbwrAg1A4mFdDvlNSYcYT

# --- 通用 Nine CEX 订单参数 (所有策略共用这些作为下单基础参数) ---
# Nine CEX 账户类型 (例如: 1 代表币币账户)
NINE_ACCOUNT_TYPE=1
# Nine CEX 限价单的订单类型代码
NINE_ORDER_TYPE_LIMIT=1
# Nine CEX 买单方向代码
NINE_ORDER_DIR_BUY=1
# Nine CEX 卖单方向代码
NINE_ORDER_DIR_SELL=2

# --- MirrorBinanceStrategy (MM) 特定配置 ---
# 镜像币安策略：从币安获取订单簿，按一定规则在 Nine CEX 上创建对应订单
# [必需] MirrorBinanceStrategy 使用的 Nine CEX API Key (正确的凭证)
MM_NINE_API_KEY=5073ff6ed6c6401fba34c9626d8337b6
# [必需] MirrorBinanceStrategy 使用的 Nine CEX API Secret (正确的凭证)
MM_NINE_API_SECRET=b38f4cfbe7c841ac82f1c6df074ef72a
# 要在 Nine CEX 上交易的交易对
MM_NINE_CEX_SYMBOL=ETH/SEPUSDT
# 要从 Binance 获取数据的交易对
MM_BINANCE_SYMBOL=PEPEUSDT
# (示例: 0.001 代表 0.1%) Nine CEX 订单价格相对于币安价格的价差百分比 (调整为更保守的价差)
MM_SPREAD_PERCENTAGE=0.01
# (示例: 1.0) Nine CEX 订单数量相对于币安数量的系数
MM_QUANTITY_COEFFICIENT=0.1
# [必需] MirrorBinanceStrategy 在 Nine CEX 下单时使用的价格精度 (小数点后位数)
MM_PRICE_PRECISION=8
# [必需] MirrorBinanceStrategy 在 Nine CEX 下单时使用的数量精度 (小数点后位数)
MM_QTY_PRECISION=0
# 从 Binance 获取订单簿的档位数 (减少到2以避免超过挂单限制)
MM_BINANCE_DEPTH_LEVELS=5
# MirrorBinanceStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
MM_UPDATE_INTERVAL=60

# --- MirrorBinanceStrategy 优化配置 (新增) ---
# 成交详情缓存时间 (秒) - 减少API调用频率，提升性能
MM_TRADE_DETAIL_CACHE_TTL=30
# 撤单策略选择 - true=只撤销完全未成交订单，false=也撤销部分成交订单
MM_CANCEL_UNFILLED_ONLY=true
# 部分成交撤单阈值 - 成交比例低于此值时撤单 (仅当MM_CANCEL_UNFILLED_ONLY=false时生效)
MM_CANCEL_PARTIAL_THRESHOLD=0.1
# 订单超时时间 (秒) - 超过此时间未成交的订单将被撤销
MM_ORDER_TIMEOUT_SECONDS=300
# 价格偏离阈值 - 当订单价格与当前市场价格偏离超过此比例时撤单
MM_PRICE_DEVIATION_THRESHOLD=0.005

# --- VolumeKlineStrategy (VK) 特定配置 ---
# 交易量K线策略：根据Nine CEX资金池数据在最佳买卖价位放置固定数量的订单
# v2.1优化：简化为真正的定时定量刷量策略，只获取盘口第一档数据
# [必需] VolumeKlineStrategy 使用的 Nine CEX API Key
VK_NINE_API_KEY=5073ff6ed6c6401fba34c9626d8337b6
# [必需] VolumeKlineStrategy 使用的 Nine CEX API Secret
VK_NINE_API_SECRET=b38f4cfbe7c841ac82f1c6df074ef72a
# 要操作的交易对
VK_TRADING_PAIR=ETH/USDT
# VolumeKlineStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
VK_INTERVAL_SECONDS=120
# [必需] 每次下单的基础币种数量 (例如 SEPBTC 的数量)
VK_ORDER_AMOUNT=100000
# [必需] 最小允许的交易数量 (基础币种单位)
VK_MIN_TRADE_QTY_ASSET=10000
# [必需] VolumeKlineStrategy 在 Nine CEX 下单时使用的价格精度
VK_PRICE_PRECISION=8
# [必需] VolumeKlineStrategy 在 Nine CEX 下单时使用的数量精度
VK_QTY_PRECISION=0
# 启用定期成交检测 - 在每轮策略执行前检查之前订单的成交状态
VK_ENABLE_PERIODIC_TRADE_CHECK=true

# --- CumulativeDepthStrategy (CDS) 特定配置 ---
# 累积深度策略：根据资金池的累积深度来确定买卖价格，并下固定数量的订单
# [必需] CumulativeDepthStrategy 使用的 Nine CEX API Key
CDS_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] CumulativeDepthStrategy 使用的 Nine CEX API Secret
CDS_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# 要操作的交易对
CDS_TRADING_PAIR=SEPBTC/USDT
# CumulativeDepthStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
CDS_INTERVAL_SECONDS=2
# [必需] 买卖订单的基础币种数量 (例如 BTC 的数量)
CDS_ORDER_AMOUNT_BASE=0.00001
# [必需] 计算买单价格时，目标累积买盘数量 (基础币种单位)
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=8
# [必需] 计算卖单价格时，目标累积卖盘数量 (基础币种单位)
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=8
# (示例: 0.01 USDT) 对计算出的原始买价进行的调整 (正数为向上调整价格)
CDS_PRICE_ADJUSTMENT_BUY=0.01
# (示例: -0.01 USDT) 对计算出的原始卖价进行的调整 (负数为向下调整价格)
CDS_PRICE_ADJUSTMENT_SELL=-0.01
# [必需] 目标买卖价之间的最小价差 (计价货币单位)
CDS_MIN_SPREAD_PRICE=1.5
# (示例: -0.05 USDT) 当价差过小时，对买价的调整 (通常为负以扩大价差)
CDS_SPREAD_ADJUSTMENT_BUY=-10
# (示例: 0.05 USDT) 当价差过小时，对卖价的调整 (通常为正以扩大价差)
CDS_SPREAD_ADJUSTMENT_SELL=10
# [必需] 允许的最小下单数量 (基础币种单位)
CDS_MIN_ORDER_QTY_BASE=0.00001
# [必需] CumulativeDepthStrategy 在 Nine CEX 下单时使用的价格精度
CDS_PRICE_PRECISION=2
# [必需] CumulativeDepthStrategy 在 Nine CEX 下单时使用的数量精度
CDS_QTY_PRECISION=5
# 从资金池获取数据时，扫描的最大档位数
CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN=20


# --- Binance 测试网配置 (添加到 Binance API 部分之后) ---
# 是否使用币安测试网 (True/False)
USE_BINANCE_TESTNET=true
# 币安测试网 API URL
BINANCE_TESTNET_API_URL=https://testnet.binance.vision
# 币安测试网 API Key
BINANCE_TESTNET_API_KEY=XPmwV7pQ1gE0mDahUVnvGe4Tk6XVm2Yo6Q4ggG1pGxA5zNltQX0Tmt2RsRBO73Gz
# 币安测试网 API Secret
BINANCE_TESTNET_API_SECRET=sDvF71dvPcVDYn5ylTCdDSqz3DakCu2rEHbDRO7vcV0GVPjGtBJVHs7jvPcv4KRO

# --- CrossExchangeArbitrageStrategy (ARB) 特定配置 (完全新增) ---
# 跨交易所套利策略：在 Nine CEX 和 Binance 之间寻找价格差异进行套利
# [必需] CrossExchangeArbitrageStrategy 使用的 Nine CEX API Key
ARB_NINE_API_KEY=3126564304c54725907a074b7cdd00c1
# [必需] CrossExchangeArbitrageStrategy 使用的 Nine CEX API Secret  
ARB_NINE_API_SECRET=00201dece2be48bd9149a1c233279bbc
# Nine CEX 上的交易对
ARB_NINE_SYMBOL=SEPETH/SEPUSDT
# Binance 上的交易对
ARB_BINANCE_SYMBOL=ETHUSDT
# Nine CEX 手续费率 (例如: 0.001 = 0.1%)
ARB_NINE_FEE_RATE=0.001
# Binance 手续费率 (例如: 0.001 = 0.1%)
ARB_BINANCE_FEE_RATE=0.001
# 最小利润率阈值 (例如: 0.002 = 0.2%)
ARB_MIN_PROFIT_PCT=0.002
# 滑点保护因子 (例如: 0.0005 = 0.05%)
ARB_SLIPPAGE_FACTOR=0.0005

# --- 动态数量计算配置 ---
# 数量计算模式: fixed=固定数量, percentage=按盘口百分比, adaptive=自适应
ARB_ORDER_AMOUNT_MODE=adaptive
# 基础数量 (fixed模式使用，adaptive模式作为基准)
ARB_ORDER_AMOUNT_BASE=0.001
# 盘口数量百分比 (percentage模式使用，例如: 0.1 = 使用盘口10%的数量)
ARB_ORDER_AMOUNT_PERCENTAGE=0.1
# 最大单次交易数量限制
ARB_ORDER_AMOUNT_MAX=0.01
# 最小单次交易数量限制
ARB_ORDER_AMOUNT_MIN=0.0001

# --- 其他配置 ---
# 最大待处理订单数
ARB_MAX_PENDING_ORDERS=5
# 订单超时时间 (秒)
ARB_ORDER_TIMEOUT_SECONDS=300
# Binance IOC 订单检查超时 (秒)
ARB_BINANCE_IOC_CHECK_TIMEOUT=5
# Binance 价格精度
ARB_BINANCE_PRICE_PRECISION=8
# Binance 数量精度
ARB_BINANCE_QTY_PRECISION=6
# Binance 最小交易数量
ARB_BINANCE_MIN_BASE_QTY=0.0001
# Nine CEX 订单簿精度参数
ARB_NINE_POOL_PRECISION_API_PARAM=0.01

# --- LiquidityProviderStrategy (LP) 特定配置 (新增) ---
# 流动性提供策略：为新上线的交易对自动提供初始流动性，创建买卖订单网格
# [必需] LiquidityProviderStrategy 使用的 Nine CEX API Key
LP_NINE_API_KEY=d9fde074120b4f739fd47be2ccbd9b87
# [必需] LiquidityProviderStrategy 使用的 Nine CEX API Secret
LP_NINE_API_SECRET=a0e9ce6b980b4b26b36c4db15c426bd8
# 初始 USDT 资金量 - 用于为新交易对提供流动性的初始资金
LP_INITIAL_USDT=1000
# 代币卖出比例 - 当监控到新交易对时，将代币余额的多少比例用于卖单 (0.15 = 15%)
LP_COIN_SELL_PERCENTAGE=0.15

# --- 深度优化配置 (多层挂单) ---
# 挂单层数 - 在买卖两侧各挂多少层订单
LP_DEPTH_LEVELS=5
# 基础价差百分比 - 第一层订单相对于中间价的价差 (0.002 = 0.2%)
LP_SPREAD_BASE=0.002
# 价差递增幅度 - 每增加一层，价差增加的幅度 (0.001 = 0.1%)
LP_SPREAD_INCREMENT=0.001
# 价差随机性 - 每层价差的随机调整范围 ±(0.0003 = ±0.03%)
LP_SPREAD_RANDOMNESS=0.0003

# --- 订单大小配置 ---
# 单个订单大小百分比 - 每个订单金额占总可用资金的比例 (0.1 = 10%)
LP_ORDER_SIZE_PERCENTAGE=0.1
# 订单大小模式 - normal=传统递减模式, reverse=大单托底模式
LP_ORDER_SIZE_MODE=reverse
# 订单大小变化系数 - normal模式时为衰减系数, reverse模式时为增长系数 (0.8 = 每层变化20%)
LP_ORDER_SIZE_FACTOR=0.8
# 最小订单金额 - 单个订单的最小USDT金额，低于此值不下单
LP_MIN_ORDER_AMOUNT=10

# --- 时间和行为配置 ---
# 卖单频率 - 每隔多少秒执行一次定期卖单操作 (300秒 = 5分钟)
LP_SELL_FREQUENCY=300
# 定期卖出比例 - 每次定期卖出时，卖出代币余额的百分比 (0.01 = 1%)
LP_SELL_AMOUNT_PERCENTAGE=0.01
# LiquidityProviderStrategy 策略执行间隔 (秒) - TradingBotService使用此配置控制该策略的执行频率
LP_UPDATE_INTERVAL=60

# --- 随机化优化配置 (拟人化行为) ---
# 时间间隔随机化范围 - 对监控和出货间隔添加随机性 (±30%)
LP_TIME_RANDOMNESS_FACTOR=0.3
# 订单数量随机化范围 - 对订单数量添加微调随机性 (±5%)
LP_QUANTITY_RANDOMNESS_FACTOR=0.05
# 部分撤单概率 - 每次检查时随机撤销部分订单的概率 (0.1 = 10%)
LP_PARTIAL_CANCEL_PROBABILITY=0.1
# 撤单重挂延迟范围 - 撤单后重新挂单的延迟时间范围 (秒)
LP_REORDER_DELAY_MIN=5
LP_REORDER_DELAY_MAX=30
# 行为模式随机化 - 是否启用随机行为模式
LP_ENABLE_BEHAVIOR_RANDOMIZATION=true
# 订单生命周期随机化 - 订单存活时间的随机范围 (秒)
LP_ORDER_LIFETIME_MIN=300
LP_ORDER_LIFETIME_MAX=1800

# --- 技术配置 ---
# [必需] Nine CEX 下单时使用的价格精度 (小数点后位数)
LP_PRICE_PRECISION=5
# [必需] Nine CEX 下单时使用的数量精度 (小数点后位数)
LP_QTY_PRECISION=0


# --- EnhancedLiquidityProviderStrategy (ELP) 特定配置 (新增) ---
# 增强型流动性提供策略：专门针对AMM代币的改进版流动性做市策略
# [必需] EnhancedLiquidityProviderStrategy 使用的 Nine CEX API Key
ELP_NINE_API_KEY=d9fde074120b4f739fd47be2ccbd9b87
# [必需] EnhancedLiquidityProviderStrategy 使用的 Nine CEX API Secret
ELP_NINE_API_SECRET=a0e9ce6b980b4b26b36c4db15c426bd8

# --- 运行模式配置 ---
# 运行模式 - manual=手动指定交易对, auto=自动监控新交易对
ELP_OPERATING_MODE=manual
# 手动模式下指定的交易对 (逗号分隔)
ELP_MANUAL_PAIRS=Party/USDT

# --- 资金配置 ---
# 总可用 USDT 资金量
ELP_TOTAL_USDT=100

# --- 价格策略配置 ---
# AMM 代币基础价格 (固定价格 0.0000161)
ELP_BASE_PRICE=0.0000161
# 价格倍数配置 (逗号分隔，如 0.5,1.0,2.0 表示 0.5倍、1倍、2倍价格)
ELP_PRICE_MULTIPLIERS=0.5,1.0,2.0

# --- 订单配置 ---
# 每个价格等级的订单层数
ELP_ORDERS_PER_LEVEL=3

# --- 卖出机制配置 ---
# 定期卖出频率 (秒) - 每隔多久执行一次自动卖出
ELP_SELL_FREQUENCY=300
# 每次卖出比例 - 每次卖出代币余额的百分比 (0.01 = 1%)
ELP_SELL_PERCENTAGE=0.01
# 最小卖出金额 - 低于此值不执行卖出操作
ELP_MIN_SELL_AMOUNT=100

# --- 精度配置 ---
# [必需] 价格精度 (小数点后位数)
ELP_PRICE_PRECISION=5
# [必需] 数量精度 (小数点后位数)
ELP_QTY_PRECISION=0

# --- 基础配置补充 ---
# Flask应用配置
FLASK_APP=app
FLASK_ENV=development
FLASK_DEBUG=True

# 通用交易对配置
MM_TRADING_PAIR=Fufufaf/USDT
MM_TRADING_PAIR_ID=20250617000029

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# --- RaydiumPriceBalanceStrategy (RPB) 配置 ---
# Raydium链上价格平衡策略：获取Raydium DEX价格并将CEX价格与之对齐
# [必需] RaydiumPriceBalanceStrategy 使用的 Nine CEX API Key
RPB_NINE_API_KEY=6f9f3d2fb9404acaa4d836b8e624745b
# [必需] RaydiumPriceBalanceStrategy 使用的 Nine CEX API Secret
RPB_NINE_API_SECRET=b4cf2b0a5ceb429cbcedadbd8e57b7af

# 交易对配置
RPB_TRADING_PAIR=二哈/USDT
# 链上合约地址 - 优先使用合约地址而非代币符号（Solana地址）
RPB_CONTRACT_ADDRESS=ASRNzwDpYWseimGQh3GootWLphpuMe3cUnpoH4mFbonk

# ===== 核心策略参数 =====
# CEX目标价格偏差（相对于DEX价格） 🎯
# -0.05 = CEX比DEX低5%（吸引从CEX买入，适合新币拉升期）
# 0 = CEX与DEX价格一致（标准套利平衡）
# 0.05 = CEX比DEX高5%（吸引向CEX卖出，适合稳定期）
RPB_TARGET_PRICE_OFFSET=0

# 价格限制因子（CEX的300%限制）
RPB_PRICE_LIMIT_FACTOR=2.9

# ===== 订单金额配置 =====
# 基础订单金额提高到10 USDT，确保满足最小10000个币的要求
# 按当前二哈价格约0.001 USDT计算，10 USDT = 10000个币
RPB_BASE_ORDER_AMOUNT=2
RPB_MIN_ORDER_AMOUNT=1
RPB_MAX_ORDER_AMOUNT=5

# 小额交易金额（用于吃单和建立成交价）
# 调整为满足最小数量要求
RPB_SMALL_TRADE_AMOUNT=1
RPB_MEDIUM_TRADE_AMOUNT=3

# ===== 流动性配置 =====
# 价差级别（逗号分隔，相对于目标价格的比例）
RPB_INITIAL_SPREADS=0.001,0.002,0.005,0.01,0.02

# 每层订单金额倍数（逗号分隔）
RPB_LEVEL_MULTIPLIERS=1.0,1.2,1.5,2.0,3.0

# 渐进式拉盘参数
RPB_PROGRESSIVE_PUMP_THRESHOLD=0.5
RPB_PUMP_STEP_FACTORS=1.5,2.0,2.3,2.6,2.9

# 流动性深度参数
RPB_LIQUIDITY_LEVELS=5

# ===== 精度配置 =====
RPB_PRICE_PRECISION=8
RPB_QTY_PRECISION=2

# 策略更新间隔 (秒)
RPB_UPDATE_INTERVAL=30

