# Nine Trade Maker

一个基于 Flask 的数字货币交易机器人框架，支持多种交易策略和跨交易所操作。

## 功能特性

- **多交易策略支持**：支持9种不同的交易策略
- **🌟 自管理策略**：极简4参数配置，智能自适应优化
- **跨交易所操作**：支持 Nine CEX、Binance 和 Raydium DEX
- **统一服务架构**：通过 `TradingBotService` 统一管理所有策略
- **专用API密钥管理**：每个策略使用独立的API密钥
- **灵活配置系统**：基于环境变量的配置管理
- **统一交易对格式**：自动标准化交易对格式，支持BASE/USDT标准
- **实时订单管理**：支持订单下达、取消和状态跟踪
- **Web API接口**：提供 RESTful API 接口

## 支持的交易策略

### 1. MirrorBinanceStrategy (币安镜像做市策略) 🚀
- **功能**：镜像 Binance 订单簿到 Nine CEX 进行做市
- **原理**：获取 Binance 最佳买卖价格，在 Nine CEX 上以配置的价差下单
- **适用场景**：为 Nine CEX 提供流动性，基于 Binance 价格进行做市
- **特点**：使用 Binance 公开 API，无需 Binance API 密钥
- **🚀 v2.1优化**：智能缓存机制减少80-90%API调用，4种撤单策略提升资金效率
- **📖 详细文档**: [docs/strategies/mirror-binance-strategy.md](docs/strategies/mirror-binance-strategy.md)

### 2. LiquidityProviderStrategy (流动性提供策略) 🔥
- **功能**：在 Nine CEX 上提供深度流动性，支持多层订单和智能撤单
- **原理**：基于当前市价以配置的价差建立多层买卖单，提供全方位流动性支持
- **适用场景**：为交易对提供深度流动性，稳定价格波动，增强市场质量
- **🔥 核心特性**：
  - 多层订单支持（1-10层可配置）
  - 4种订单金额分配模式（平均、递增、递减、固定）
  - 8种随机化拟人功能提升安全性
  - 智能订单生命周期管理
  - 部分撤单重挂机制
- **📖 详细文档**: [README_LIQUIDITY_PROVIDER.md](README_LIQUIDITY_PROVIDER.md)

### 3. VolumeKlineStrategy (成交量K线策略)
- **功能**：基于 Nine CEX 自身订单簿进行交易
- **原理**：在最佳买卖价格上同时下买单和卖单
- **适用场景**：增加交易对成交量
- **🚀 v2.1优化**：简化为真正的定时定量刷量策略，修复API参数兼容性问题
- **📖 详细文档**: [docs/strategies/volume-kline-strategy.md](docs/strategies/volume-kline-strategy.md)

### 4. CumulativeDepthStrategy (累积深度策略)
- **功能**：基于累积深度计算目标价格
- **原理**：计算达到指定金额所需的累积深度价格
- **适用场景**：深度流动性提供
- **📖 详细文档**: [docs/strategies/cumulative-depth-strategy.md](docs/strategies/cumulative-depth-strategy.md)

### 5. CrossExchangeArbitrageStrategy (跨交易所套利策略)
- **功能**：在 Nine CEX 和 Binance 间寻找套利机会
- **原理**：比较两个交易所价格差异，执行套利交易
- **适用场景**：价格差异套利
- **📖 详细文档**: [docs/strategies/cross-exchange-arbitrage-strategy.md](docs/strategies/cross-exchange-arbitrage-strategy.md)

### 6. EnhancedLiquidityProviderStrategy (增强型流动性提供策略) ⚡
- **功能**：针对AMM token提供高级流动性管理，支持拉盘出货和智能深度控制
- **原理**：使用固定基础价格进行多层上下挂单，具备拉盘出货功能和持续出货机制
- **适用场景**：专门针对AMM代币的流动性提供，支持价格操控和利润实现
- **⚡ 核心特性**：
  - 支持手动指定交易对和自动发现新交易对两种模式
  - 固定价格模式，适合AMM代币的价格控制
  - 拉盘出货功能，支持5x目标倍数的智能拉盘
  - 分阶段拉盘策略，优化拉盘效率
  - 持续出货机制，实现利润最大化
- **📖 详细文档**: [docs/strategies/enhanced_liquidity_provider_strategy.md](docs/strategies/enhanced_liquidity_provider_strategy.md)

### 7. NaturalKlineStrategy (自然K线策略) 📊
- **功能**：通过控制买卖订单生成自然的K线图形态
- **原理**：基于预设模式生成价格轨迹，包括上涨、下跌、横盘、突破等形态
- **适用场景**：K线形态控制，技术分析图表优化
- **📊 核心特性**：
  - 支持10+种K线形态（上涨、下跌、横盘、突破、假突破等）
  - 自动生成上下影线，增加K线自然度
  - 自适应市场干扰，智能切换形态
  - 基于成交量的动态调整
- **⚠️ 注意**：配置复杂，需要20+参数，适合高级用户

### 8. 🌟 SelfManagedNaturalKlineStrategy (自管理K线策略) - 推荐 ⭐
- **功能**：极简版K线控制策略，自动参数优化
- **原理**：智能分析账户和市场状况，自动计算所有策略参数
- **适用场景**：新手用户，希望简单使用K线控制功能
- **🌟 核心优势**：
  - **极简配置**：仅需4个参数（API密钥、交易对、风险等级）
  - **智能自适应**：自动根据账户余额调整订单规模
  - **实时优化**：根据市场流动性和历史表现持续优化
  - **零门槛**：1000 USDT起步，支持各种资金规模
  - **自我学习**：越用越聪明的算法
- **💰 资金需求**：
  - 保守型：1,000+ USDT（订单规模0.5%，2分钟间隔）
  - 平衡型：5,000+ USDT（订单规模1%，1分钟间隔）
  - 激进型：50,000+ USDT（订单规模2%，30秒间隔）

### 9. RaydiumPriceBalanceStrategy (Raydium链上价格平衡策略) 🌊
- **功能**：获取Raydium DEX链上价格，自动平衡CEX价格
- **原理**：实时监控链上价格，通过吃单/砸盘将CEX价格调整至链上水平
- **适用场景**：DEX-CEX价格套利，跨平台流动性提供
- **🌊 核心特性**：
  - 实时获取Raydium链上价格
  - 智能判断价格偏差并执行平衡
  - 价格偏低时吃卖盘提价
  - 价格偏高时砸买盘降价
  - 价格对齐后自动提供流动性
- **📖 详细文档**: [docs/strategies/raydium-price-balance-strategy.md](docs/strategies/raydium-price-balance-strategy.md)

> 📚 **完整策略文档**: 查看 [docs/strategies/](docs/strategies/) 目录获取每个策略的详细说明、配置示例和最佳实践。

## 系统架构

```
Nine Trade Maker
├── app/
│   ├── __init__.py           # 应用工厂和配置管理
│   ├── services/
│   │   ├── market_maker_service.py  # TradingBotService (统一交易服务)
│   │   ├── nine_client.py           # Nine CEX API 客户端
│   │   ├── binance_client.py        # Binance API 客户端
│   │   ├── raydium_client.py        # Raydium DEX API 客户端
│   │   ├── trading_pair_manager.py  # 交易对管理器
│   │   └── trading_pair_normalizer.py # 交易对格式统一化
│   ├── strategies/
│   │   ├── base_strategy.py         # 策略基类
│   │   ├── mirror_binance_strategy.py
│   │   ├── liquidity_provider_strategy.py
│   │   ├── enhanced_liquidity_provider_strategy.py
│   │   ├── volume_kline_strategy.py
│   │   ├── cumulative_depth_strategy.py
│   │   ├── cross_exchange_arbitrage_strategy.py
│   │   └── raydium_price_balance_strategy.py
│   └── api/                         # Web API 路由
└── cli.py                           # 命令行工具
```

## 安装和配置

### 1. 环境要求
- Python 3.8+ (建议 Python 3.9 或以上)
- pip
- venv (虚拟环境，推荐使用)

### 2. 创建虚拟环境

#### Ubuntu/Debian 系统
```bash
# 安装 Python 和 pip（如果尚未安装）
sudo apt update
sudo apt install python3 python3-pip python3-venv

# 创建虚拟环境
python3 -m venv nine-trade-env

# 激活虚拟环境
source nine-trade-env/bin/activate
```

#### macOS 系统
```bash
# 使用 Homebrew 安装 Python（推荐）
brew install python

# 或使用系统自带的 Python3
# 创建虚拟环境
python3 -m venv nine-trade-env

# 激活虚拟环境
source nine-trade-env/bin/activate
```

#### Windows 系统
```bash
# 创建虚拟环境
python -m venv nine-trade-env

# 激活虚拟环境
nine-trade-env\Scripts\activate
```

### 3. 安装依赖
```bash
# 确保在激活的虚拟环境中
# 升级 pip 到最新版本
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

### 4. 配置环境变量

#### 🆕 新版分层配置 (推荐)
```bash
# 确保在激活的虚拟环境和项目目录中
cd nine-trade-maker

# 如果是首次使用或从旧版迁移
python -c "from app.services.config_migration import migrate_config; print(migrate_config('.'))"

# 编辑主配置文件 (仅66行，14个参数)
vim .env
# 或使用其他编辑器：nano .env 或 code .env
```

#### 📁 配置文件结构
```
nine-trade-maker/
├── .env                              # 主配置 (66行，14参数)
├── config/strategies/                # 策略配置目录 (8个策略)
│   ├── mirror_binance.env                # 镜像币安策略 (15参数)
│   ├── liquidity_provider.env            # 流动性提供策略 (25参数)  
│   ├── enhanced_liquidity_provider.env   # 增强流动性策略 (47参数)
│   ├── volume_kline.env                  # 成交量策略 (9参数)
│   ├── cumulative_depth.env              # 累积深度策略 (16参数)
│   ├── cross_exchange_arbitrage.env      # 跨交易所套利 (20参数)
│   ├── natural_kline.env                 # 自然K线策略 (22参数)
│   └── raydium_price_balance.env         # Raydium平衡策略 (24参数)
└── env.example                       # 旧版大型配置 (已废弃)
```

#### 🔄 从旧版配置迁移
如果你正在使用旧版大型配置文件(591行)，强烈建议迁移：
```bash
# 自动迁移到新版配置
python -c "from app.services.config_migration import migrate_config; result = migrate_config('.'); print('迁移结果:', result['message'])"

# 查看备份文件
ls -la .env.backup
```

### 5. 关键配置项

#### 🌟 新手推荐：自管理K线策略 (仅需4个参数)
```bash
# 1. 选择策略
TRADING_STRATEGY=self_managed_kline

# 2. Nine CEX API 凭证
SMK_NINE_API_KEY=your_api_key_here
SMK_NINE_API_SECRET=your_api_secret_here

# 3. 交易对
SMK_TRADING_PAIR=ETH/SEPUSDT

# 4. 风险等级 (conservative/balanced/aggressive)
SMK_RISK_LEVEL=balanced

# 🎉 完成！其他参数全部自动计算
```

#### 传统配置方式
```bash
# Nine CEX API
NINE_API_URL=https://api.nine.com

# 策略选择（可选，命令行参数优先级更高）  
TRADING_STRATEGY=mirror_binance  # 可选: mirror_binance, liquidity_provider, volume_kline, cumulative_depth, cross_exchange_arbitrage, enhanced_liquidity_provider, raydium_price_balance, natural_kline, self_managed_kline

# Binance API 配置
ENABLE_BINANCE_CLIENT=true

# 使用正式网络 (公开 API，无需密钥)
BINANCE_API_URL=https://api.binance.com

# 或使用测试网络 (需要测试网 API 密钥)
USE_BINANCE_TESTNET=true
BINANCE_TESTNET_API_URL=https://testnet.binance.vision
BINANCE_TESTNET_API_KEY=your_testnet_api_key
BINANCE_TESTNET_API_SECRET=your_testnet_api_secret
```

#### 策略专用API密钥
每个策略需要配置独立的 Nine CEX API 密钥：
```bash
# MirrorBinanceStrategy
MM_NINE_API_KEY=your_mm_api_key
MM_NINE_API_SECRET=your_mm_api_secret

# LiquidityProviderStrategy
LP_NINE_API_KEY=your_lp_api_key
LP_NINE_API_SECRET=your_lp_api_secret

# VolumeKlineStrategy  
VK_NINE_API_KEY=your_vk_api_key
VK_NINE_API_SECRET=your_vk_api_secret

# CumulativeDepthStrategy
CDS_NINE_API_KEY=your_cds_api_key
CDS_NINE_API_SECRET=your_cds_api_secret

# CrossExchangeArbitrageStrategy
ARB_NINE_API_KEY=your_arb_api_key
ARB_NINE_API_SECRET=your_arb_api_secret

# EnhancedLiquidityProviderStrategy
ELP_NINE_API_KEY=your_elp_api_key
ELP_NINE_API_SECRET=your_elp_api_secret

# RaydiumPriceBalanceStrategy
RPB_NINE_API_KEY=your_rpb_api_key
RPB_NINE_API_SECRET=your_rpb_api_secret
```

## 使用方法

> **重要提醒**：所有操作都需要在激活的虚拟环境中进行。如果虚拟环境未激活，请先执行激活命令：
> - Linux/macOS: `source nine-trade-env/bin/activate`
> - Windows: `nine-trade-env\Scripts\activate`

### 命令行操作

#### 1. 启动交易机器人（后台运行）
```bash
# 确保在激活的虚拟环境中
# 使用默认策略（从环境变量 TRADING_STRATEGY 读取）
flask start-trading-bot

# 指定特定策略启动（推荐方式）
# 🌟 新手推荐：自管理K线策略（仅需4个参数）
flask start-trading-bot --strategy self_managed_kline

# 其他策略
flask start-trading-bot --strategy mirror_binance
flask start-trading-bot --strategy liquidity_provider
flask start-trading-bot --strategy enhanced_liquidity_provider
flask start-trading-bot --strategy volume_kline
flask start-trading-bot --strategy cumulative_depth
flask start-trading-bot --strategy cross_exchange_arbitrage
flask start-trading-bot --strategy natural_kline
flask start-trading-bot --strategy raydium_price_balance

# 模拟运行模式（不实际下单）
flask start-trading-bot --strategy self_managed_kline --dry-run
flask start-trading-bot --strategy mirror_binance --dry-run
```

> **注意**：命令行参数 `--strategy` 会覆盖环境变量 `TRADING_STRATEGY` 的设置。推荐使用命令行参数指定策略，这样更明确且避免配置冲突。

#### 2. 停止交易机器人
```bash
flask stop-trading-bot
```

#### 3. 执行单次策略运行
```bash
# 使用默认策略（实际下单）
flask run-bot-once

# 指定特定策略（实际下单）
# 🌟 新手推荐
flask run-bot-once --strategy self_managed_kline

# 其他策略
flask run-bot-once --strategy mirror_binance
flask run-bot-once --strategy liquidity_provider
flask run-bot-once --strategy enhanced_liquidity_provider
flask run-bot-once --strategy volume_kline
flask run-bot-once --strategy cumulative_depth
flask run-bot-once --strategy cross_exchange_arbitrage
flask run-bot-once --strategy natural_kline
flask run-bot-once --strategy raydium_price_balance

# 模拟运行（不实际下单，仅测试逻辑）
flask run-bot-once --strategy self_managed_kline --dry-run
flask run-bot-once --strategy mirror_binance --dry-run
flask run-bot-once --strategy liquidity_provider --dry-run
flask run-bot-once --strategy enhanced_liquidity_provider --dry-run
flask run-bot-once --strategy volume_kline --dry-run
flask run-bot-once --strategy cumulative_depth --dry-run
flask run-bot-once --strategy cross_exchange_arbitrage --dry-run
flask run-bot-once --strategy natural_kline --dry-run
flask run-bot-once --strategy raydium_price_balance --dry-run
```

### Web API 接口

#### 启动 Flask 应用
```bash
# 确保在激活的虚拟环境中
flask run --host=0.0.0.0 --port=5000
```

#### 主要 API 端点
- `GET /health` - 健康检查
- `POST /api/market/orders` - 获取市场订单
- `POST /api/trade/place` - 下单
- `POST /api/trade/cancel` - 撤单
- `GET /api/binance/orderbook` - 获取币安订单簿

## 策略配置详解

### MirrorBinanceStrategy 配置
```bash
# === 基础配置 ===
MM_BINANCE_SYMBOL=BTCUSDT           # 币安交易对
MM_NINE_CEX_SYMBOL=BTC/USDT         # Nine CEX 交易对
MM_SPREAD_PERCENTAGE=0.002          # 价差百分比 (0.2%)
MM_UPDATE_INTERVAL=60               # 更新间隔(秒)
MM_PRICE_PRECISION=2                # 价格精度
MM_QTY_PRECISION=6                  # 数量精度

# === v2.1优化配置 ===
MM_TRADE_DETAIL_CACHE_TTL=30        # 成交详情缓存时间（秒）
MM_CANCEL_UNFILLED_ONLY=true        # 只撤销完全未成交订单
MM_CANCEL_PARTIAL_THRESHOLD=0.1     # 部分成交撤单阈值（10%）
MM_ORDER_TIMEOUT_SECONDS=300        # 订单超时时间（5分钟）
MM_PRICE_DEVIATION_THRESHOLD=0.005  # 价格偏离阈值（0.5%）
```

### LiquidityProviderStrategy 配置
```bash
# === 基础配置 ===
LP_TRADING_PAIR=BTC/USDT             # 交易对
LP_ORDER_LAYERS=5                    # 订单层数（1-10层）
LP_SPREAD_PERCENTAGE=0.002           # 价差百分比 (0.2%)
LP_UPDATE_INTERVAL=60                # 更新间隔(秒)
LP_PRICE_PRECISION=2                 # 价格精度
LP_QTY_PRECISION=6                   # 数量精度

# === 订单金额配置 ===
LP_ORDER_AMOUNT_BASE=30              # 基础订单金额（USDT）
LP_ORDER_SIZE_MODE=reverse           # 订单大小模式: average/increment/decrement/reverse
LP_ORDER_SIZE_FACTOR=1.25            # 订单大小递增/递减因子

# === 随机化拟人功能（8项） ===
LP_ENABLE_BEHAVIOR_RANDOMIZATION=true    # 启用行为随机化
LP_TIME_RANDOMNESS_FACTOR=0.3             # 时间间隔随机化因子（±30%）
LP_QUANTITY_RANDOMNESS_FACTOR=0.05        # 订单数量随机化因子（±5%）
LP_PARTIAL_CANCEL_PROBABILITY=0.1         # 部分撤单概率（10%）
LP_REORDER_DELAY_MIN=5                    # 重新下单最小延迟（秒）
LP_REORDER_DELAY_MAX=30                   # 重新下单最大延迟（秒）
LP_ORDER_LIFETIME_MIN=300                 # 订单生命周期最小值（5分钟）
LP_ORDER_LIFETIME_MAX=1800                # 订单生命周期最大值（30分钟）

# === 出货功能配置 ===
LP_ENABLE_SELL_OFF=true              # 启用出货功能
LP_SELL_OFF_AMOUNT=0.001             # 出货数量
LP_SELL_OFF_INTERVAL=120             # 出货间隔（秒）
LP_SELL_OFF_RANDOMNESS=0.3           # 出货时间随机化因子（±30%）

# 定期卖出比例 - 每次定期卖出时，卖出代币余额的百分比 (0.01 = 1%)
LP_SELL_AMOUNT_PERCENTAGE=0.01
# LiquidityProvider策略专用更新间隔(秒) - TradingBotService使用此配置控制该策略的执行频率
LP_UPDATE_INTERVAL=60
```

### EnhancedLiquidityProviderStrategy 配置
```bash
# === 基础配置 ===
ELP_NINE_API_KEY=your_elp_api_key           # 增强型策略专用API密钥
ELP_NINE_API_SECRET=your_elp_api_secret     # 增强型策略专用API密钥

# === 运行模式配置 ===
ELP_OPERATING_MODE=manual                    # 运行模式: manual(指定交易对) / auto(自动发现)
ELP_MANUAL_PAIRS=TOKENXYZ/USDT,TOKENABC/USDT # 手动模式指定的交易对（逗号分隔）

# === 资金和价格配置 ===
ELP_TOTAL_USDT=3000                         # 总资金池（USDT）
ELP_BASE_PRICE=0.0000161                    # 固定基础价格
ELP_PRICE_MULTIPLIERS=0.5,1.0,2.0          # 价格倍数配置（用于多层挂单）
ELP_ORDERS_PER_LEVEL=3                      # 每个价格层级的订单数量

# === 卖出机制配置 ===
ELP_SELL_FREQUENCY=300                      # 定期卖出间隔（秒）
ELP_SELL_PERCENTAGE=0.01                    # 每次卖出的代币百分比（1%）
ELP_MIN_SELL_AMOUNT=100                     # 最小卖出数量

# === 价格模式配置 ===
ELP_PRICE_MODE=fixed                        # 价格模式: fixed(固定价格) / market(市场价格)

# === 拉盘出货配置 ===
ELP_ENABLE_PUMP_DUMP=true                   # 启用拉盘出货功能
ELP_PUMP_TARGET_RATIO=5.0                   # 拉盘目标倍数（5x）
ELP_PUMP_TRIGGER_PRICE=0.0000161            # 拉盘触发价格

# === 深度控制配置 ===
ELP_PUMP_SELL_WALL_REMOVAL=0.8             # 拉盘时移除的卖单比例
ELP_PUMP_BUY_WALL_BOOST=1.5                # 拉盘时买单深度增强倍数
ELP_DUMP_SELL_WALL_RESTORE=2.0             # 出货时恢复卖单的倍数

# === 拉盘节奏配置 ===
ELP_PUMP_PHASE_DURATION=300                # 每个拉盘阶段持续时间（秒）
ELP_DUMP_BATCH_SIZE=0.1                    # 每批出货的比例（10%）
ELP_PUMP_BUY_MODE=natural                  # 买入模式: natural/aggressive/balanced

# === 智能拉盘配置 ===
ELP_PUMP_MAX_INVESTMENT=2000               # 拉盘最大投入金额（USDT）
ELP_PUMP_BUY_INTERVAL=30                   # 主动买入间隔（秒）
ELP_PUMP_BUY_STEP_RATIO=0.05               # 每次买入推高幅度（5%）
ELP_PUMP_EFFICIENCY_THRESHOLD=0.6          # 拉盘效率阈值

# === 分阶段拉盘配置 ===
ELP_PUMP_STAGE_TARGETS=1.5,2.5,4.0,5.5    # 分阶段目标倍数
ELP_PUMP_STAGE_PAUSE=60                    # 每阶段间隔时间（秒）
ELP_PUMP_FOMO_MULTIPLIER=1.5               # FOMO阶段资金倍数

# === 精度配置 ===
ELP_PRICE_PRECISION=8                      # 价格精度
ELP_QTY_PRECISION=6                        # 数量精度

# === 更新间隔和检测配置 ===
ELP_UPDATE_INTERVAL=60                     # 策略更新间隔（秒）
ELP_ENABLE_PERIODIC_TRADE_CHECK=false     # 启用定期成交检测
```

### VolumeKlineStrategy 配置
```bash
# === v2.1优化版本 - 简化刷量策略 ===
VK_TRADING_PAIR=BTC/USDT            # 交易对
VK_INTERVAL_SECONDS=60              # VolumeKline策略专用执行间隔(秒)
VK_ORDER_AMOUNT=0.001               # 下单数量
VK_MIN_TRADE_QTY_ASSET=0.0001       # 最小交易数量

# === 成交检测优化 (新增) ===
VK_ENABLE_PERIODIC_TRADE_CHECK=true # 启用定期成交检测，提升成交状态监控的及时性
# 注意：v2.1已优化API参数格式，移除了 VK_ORDER_BOOK_DEPTH_TO_FETCH 和 VK_ORDER_PRICE_BUFFER_PERCENTAGE
```

### CumulativeDepthStrategy 配置
```bash
CDS_TRADING_PAIR=BTC/USDT                    # 交易对
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=1000         # 买单累积深度金额
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=1000        # 卖单累积深度金额
CDS_ORDER_AMOUNT_BASE=0.001                  # 下单数量
```

### CrossExchangeArbitrageStrategy 配置
```bash
ARB_NINE_CEX_SYMBOL=BTC/USDT         # Nine CEX 交易对
ARB_BINANCE_SYMBOL=BTCUSDT           # Binance 交易对
ARB_MIN_PROFIT_PERCENTAGE=0.002      # 最小利润阈值 (0.2%)
ARB_ORDER_AMOUNT_BASE=0.001          # 下单数量
ARB_UPDATE_INTERVAL=5                # 更新间隔(秒)
```

### RaydiumPriceBalanceStrategy 配置
```bash
# === 基础配置 ===
RPB_TRADING_PAIR=二哈/USDT                    # CEX交易对
RPB_CONTRACT_ADDRESS=ASRNzwDpYWse...         # Solana合约地址（优先使用）

# === 价格容差配置 ===
RPB_PRICE_TOLERANCE=0.002                    # 价格容差 (0.2%)
RPB_AGGRESSIVE_THRESHOLD=0.01                # 激进策略阈值 (1%)

# === 交易配置 ===
RPB_BASE_ORDER_AMOUNT=100                    # 基础订单金额（USDT）
RPB_MAX_ORDER_AMOUNT=1000                    # 最大单笔订单金额
RPB_EAT_ORDER_MULTIPLIER=2                   # 吃单倍数

# === 流动性配置 ===
RPB_LIQUIDITY_SPREAD=0.001                   # 流动性价差 (0.1%)
RPB_LIQUIDITY_LEVELS=5                       # 流动性层数
RPB_LIQUIDITY_AMOUNT_PER_LEVEL=50           # 每层流动性金额

# === 精度配置 ===
RPB_PRICE_PRECISION=8                        # 价格精度（支持小额代币）
RPB_QTY_PRECISION=2                          # 数量精度（符合CEX限制）

# === 更新间隔 ===
RPB_UPDATE_INTERVAL=30                       # 策略更新间隔（秒）
```
> **注意**：RaydiumPriceBalanceStrategy需要网络代理访问DexScreener和Jupiter API。
> 使用前请设置：`export https_proxy=http://127.0.0.1:7890`

## 开发和调试

### 虚拟环境管理
```bash
# 激活虚拟环境
source nine-trade-env/bin/activate  # Linux/macOS
# 或
nine-trade-env\Scripts\activate     # Windows

# 退出虚拟环境
deactivate

# 删除虚拟环境（如果需要）
rm -rf nine-trade-env              # Linux/macOS
# 或
rmdir /s nine-trade-env             # Windows
```

### 日志级别
应用使用 Python 的 logging 模块，默认级别为 INFO。

### 错误处理
- 所有策略都有完整的异常处理
- API 调用失败会记录详细错误信息
- 网络超时和连接错误会自动重试

### 测试
```bash
# 单次运行测试（不会实际下单，仅测试逻辑）
flask run-bot-once --strategy volume_kline --dry-run

# 或者测试其他策略
flask run-bot-once --strategy mirror_binance --dry-run
flask run-bot-once --strategy cumulative_depth --dry-run
flask run-bot-once --strategy cross_exchange_arbitrage --dry-run
```

## 注意事项

1. **API 密钥安全**：确保 `.env` 文件不被提交到版本控制系统
2. **资金风险**：在生产环境使用前，请在测试环境充分测试
3. **网络稳定性**：确保服务器网络连接稳定，避免API调用超时
4. **监控和告警**：建议配置日志监控和异常告警
5. **策略参数**：根据市场情况和风险承受能力调整策略参数

## 架构优化

### 最新改进 (v2.1)
- **服务层统一**：将 `MarketMakerService` 重构为 `TradingBotService`，支持所有策略类型
- **API密钥专用化**：每个策略使用独立的API密钥，提高安全性和可管理性
- **订单簿共享**：策略可复用 `TradingBotService` 获取的订单簿数据，减少API调用
- **配置标准化**：统一订单参数配置，减少重复配置项
- **错误处理增强**：改进异常处理和日志记录
- **日志中文化**：所有用户可见的日志信息统一使用中文，提升可读性
- **启动逻辑优化**：命令行参数优先级高于环境变量，避免策略选择冲突
- **初始化精简**：应用启动时不预创建交易服务，只在CLI命令执行时按需创建
- **🚀 MirrorBinanceStrategy优化**：智能缓存机制减少80-90%API调用，4种撤单策略提升资金效率
- **🔥 LiquidityProviderStrategy新增**：多层流动性做市策略，支持8项随机化拟人功能，4种金额分配模式
