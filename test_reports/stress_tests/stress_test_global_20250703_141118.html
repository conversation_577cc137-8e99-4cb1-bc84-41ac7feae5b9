
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试报告 - stress_test_global</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 14px; color: #666; margin-top: 5px; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>性能测试报告</h1>
        <h2>stress_test_global</h2>
        <p><strong>测试时间:</strong> 2025-07-03T14:11:18.129922</p>

        <div class="summary">
            <div class="metric-card">
                <div class="metric-value danger">0.0%</div>
                <div class="metric-label">成功率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3000</div>
                <div class="metric-label">总请求数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.000s</div>
                <div class="metric-label">平均响应时间</div>
            </div>
        </div>

        <h3>详细指标</h3>
        <table>
            <tr><th>指标</th><th>值</th></tr>
            <tr><td>总请求数</td><td>3000</td></tr>
            <tr><td>成功请求数</td><td>0</td></tr>
            <tr><td>失败请求数</td><td>3000</td></tr>
            <tr><td>成功率</td><td>0.00%</td></tr>
            <tr><td>平均响应时间</td><td>0.000s</td></tr>
        </table>

        <h3>错误统计</h3>
        <table>
            <tr><th>错误类型</th><th>次数</th></tr>
        <tr><td>stress_test_error</td><td>3000</td></tr>
        </table>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
            <p>Nine Trade Maker 性能测试报告 - 生成时间: 2025-07-03 14:11:18</p>
        </div>
    </div>
</body>
</html>
        