#!/usr/bin/env python3
"""
Nine Trade Maker 测试运行脚本

提供便捷的测试执行接口，支持不同类型的测试和配置选项
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path
from dotenv import load_dotenv


class TestRunner:
    """测试运行器"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests"

        # 加载环境变量
        env_file = self.project_root / ".env"
        if env_file.exists():
            load_dotenv(env_file)
        
    def run_unit_tests(self, verbose=False, coverage=False):
        """运行单元测试"""
        print("🧪 运行单元测试...")
        
        cmd = ["python", "-m", "pytest", "tests/unit/"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
        
        cmd.extend(["-m", "unit"])
        
        return self._run_command(cmd)
    
    def run_integration_tests(self, verbose=False):
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = ["python", "-m", "pytest", "tests/integration/"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.extend(["-m", "integration"])
        
        return self._run_command(cmd)
    
    def run_performance_tests(self, verbose=False, test_type="all"):
        """运行性能测试"""
        print("⚡ 运行性能测试...")
        
        cmd = ["python", "-m", "pytest", "tests/performance/"]
        
        if verbose:
            cmd.append("-v")
        
        if test_type == "concurrent":
            cmd.extend(["-m", "concurrent"])
        elif test_type == "stress":
            cmd.extend(["-m", "stress"])
        elif test_type == "load":
            cmd.extend(["-m", "slow"])
        else:
            cmd.extend(["-m", "performance"])
        
        return self._run_command(cmd)
    
    def run_batch_tests(self, verbose=False):
        """运行批量订单测试"""
        print("📦 运行批量订单测试...")
        
        cmd = ["python", "-m", "pytest", "tests/integration/test_batch_orders.py"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.extend(["-m", "batch"])
        
        return self._run_command(cmd)
    
    def run_smoke_tests(self, verbose=False):
        """运行冒烟测试"""
        print("💨 运行冒烟测试...")
        
        cmd = ["python", "-m", "pytest"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.extend(["-m", "smoke", "--tb=short"])
        
        return self._run_command(cmd)
    
    def run_all_tests(self, verbose=False, coverage=False):
        """运行所有测试"""
        print("🚀 运行完整测试套件...")
        
        results = []
        
        # 单元测试
        print("\n" + "="*50)
        result = self.run_unit_tests(verbose, coverage)
        results.append(("单元测试", result))
        
        # 集成测试
        print("\n" + "="*50)
        result = self.run_integration_tests(verbose)
        results.append(("集成测试", result))
        
        # 性能测试（排除慢速测试）
        print("\n" + "="*50)
        result = self.run_performance_tests(verbose, "concurrent")
        results.append(("性能测试", result))
        
        # 输出总结
        self._print_test_summary(results)
        
        return all(result == 0 for _, result in results)
    
    def run_quick_tests(self, verbose=False):
        """运行快速测试（排除慢速测试）"""
        print("⚡ 运行快速测试...")
        
        cmd = ["python", "-m", "pytest"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.extend(["-m", "not slow", "--tb=short"])
        
        return self._run_command(cmd)
    
    def run_custom_tests(self, test_path, markers=None, verbose=False):
        """运行自定义测试"""
        print(f"🎯 运行自定义测试: {test_path}")
        
        cmd = ["python", "-m", "pytest", test_path]
        
        if verbose:
            cmd.append("-v")
        
        if markers:
            for marker in markers:
                cmd.extend(["-m", marker])
        
        return self._run_command(cmd)
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 检查必要的环境变量
        required_env_vars = [
            "MM_NINE_API_KEY",
            "MM_NINE_API_SECRET",
            "NINE_API_URL"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
            print("请在 .env 文件中配置这些变量")
            return False
        
        # 创建测试报告目录
        os.makedirs("test_reports", exist_ok=True)
        os.makedirs("test_reports/coverage", exist_ok=True)
        os.makedirs("test_reports/performance", exist_ok=True)
        os.makedirs("test_reports/load_tests", exist_ok=True)
        os.makedirs("test_reports/stress_tests", exist_ok=True)
        
        print("✅ 测试环境设置完成")
        return True
    
    def generate_test_report(self):
        """生成测试报告"""
        print("📊 生成测试报告...")
        
        # 运行带覆盖率的完整测试
        cmd = [
            "python", "-m", "pytest",
            "--cov=app",
            "--cov-report=html:test_reports/coverage",
            "--cov-report=term",
            "--html=test_reports/test_report.html",
            "--self-contained-html",
            "-v"
        ]
        
        result = self._run_command(cmd)
        
        if result == 0:
            print("✅ 测试报告生成完成")
            print("📁 覆盖率报告: test_reports/coverage/index.html")
            print("📁 测试报告: test_reports/test_report.html")
        else:
            print("❌ 测试报告生成失败")
        
        return result
    
    def _run_command(self, cmd):
        """运行命令"""
        try:
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, check=False)
            return result.returncode
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return 1
    
    def _print_test_summary(self, results):
        """打印测试总结"""
        print("\n" + "="*60)
        print("📋 测试总结")
        print("="*60)
        
        for test_name, result in results:
            status = "✅ 通过" if result == 0 else "❌ 失败"
            print(f"{test_name}: {status}")
        
        total_passed = sum(1 for _, result in results if result == 0)
        total_tests = len(results)
        
        print(f"\n总计: {total_passed}/{total_tests} 测试套件通过")
        
        if total_passed == total_tests:
            print("🎉 所有测试都通过了！")
        else:
            print("⚠️  有测试失败，请检查输出")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Nine Trade Maker 测试运行器")
    
    parser.add_argument(
        "test_type",
        choices=[
            "unit", "integration", "performance", "batch", "smoke",
            "all", "quick", "custom", "setup", "report"
        ],
        help="测试类型"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--path",
        help="自定义测试路径（用于 custom 类型）"
    )
    
    parser.add_argument(
        "--markers", "-m",
        nargs="*",
        help="测试标记（用于 custom 类型）"
    )
    
    parser.add_argument(
        "--perf-type",
        choices=["all", "concurrent", "stress", "load"],
        default="all",
        help="性能测试类型"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # 设置测试环境
    if not runner.setup_test_environment():
        sys.exit(1)
    
    # 执行测试
    start_time = time.time()
    
    if args.test_type == "unit":
        result = runner.run_unit_tests(args.verbose, args.coverage)
    elif args.test_type == "integration":
        result = runner.run_integration_tests(args.verbose)
    elif args.test_type == "performance":
        result = runner.run_performance_tests(args.verbose, args.perf_type)
    elif args.test_type == "batch":
        result = runner.run_batch_tests(args.verbose)
    elif args.test_type == "smoke":
        result = runner.run_smoke_tests(args.verbose)
    elif args.test_type == "all":
        result = 0 if runner.run_all_tests(args.verbose, args.coverage) else 1
    elif args.test_type == "quick":
        result = runner.run_quick_tests(args.verbose)
    elif args.test_type == "custom":
        if not args.path:
            print("❌ 自定义测试需要指定 --path 参数")
            sys.exit(1)
        result = runner.run_custom_tests(args.path, args.markers, args.verbose)
    elif args.test_type == "setup":
        result = 0 if runner.setup_test_environment() else 1
    elif args.test_type == "report":
        result = runner.generate_test_report()
    else:
        print(f"❌ 未知的测试类型: {args.test_type}")
        sys.exit(1)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️  测试执行时间: {duration:.2f} 秒")
    
    if result == 0:
        print("✅ 测试执行成功")
    else:
        print("❌ 测试执行失败")
    
    sys.exit(result)


if __name__ == "__main__":
    main()
