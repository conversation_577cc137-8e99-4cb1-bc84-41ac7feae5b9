# =============================================================================
# AdaptiveKlineStrategy (AK) 配置 - 自适应K线操盘策略
# =============================================================================
# 🎯 核心功能：支持拉盘、砸盘、横盘三种模式的自然交易策略
# 📊 工作原理：通过随机化时间、数量、价格，模拟真实交易者行为
# 🔄 动态特性：根据选定模式执行有方向性的交易，影响价格走势
# =============================================================================

# --- 基础配置 ---
# 交易对
AK_TRADING_PAIR=TST/USDT

# 策略模式选择
# bull: 拉盘模式 (70%买单推高价格)
# bear: 砸盘模式 (70%卖单压低价格)  
# sideways: 横盘模式 (50%买卖平衡震荡)
AK_MODE=bear

# --- 订单金额配置 ---
# 基础订单金额 (USDT价值)
AK_ORDER_VALUE=0.5

# 最小交易金额阈值 (USDT价值)
AK_MIN_TRADE_VALUE=0.1

# --- 随机化配置 ---
# 时间随机性 - 执行间隔的随机波动范围 (±30%)
AK_TIME_RANDOMNESS=0.3

# 数量随机性 - 订单数量的随机波动范围 (±40%) 
AK_QUANTITY_RANDOMNESS=0.4

# 价格偏移最大值 - 相对当前价格的最大偏移 (0.5%)
AK_PRICE_OFFSET_MAX=0.005

# --- 模式特定配置 ---
# 拉盘模式：买单比例 (70%)
AK_BULL_BUY_RATIO=0.7

# 砸盘模式：卖单比例 (70%) 
AK_BEAR_SELL_RATIO=0.7

# 横盘模式：买卖平衡点 (50%)
AK_SIDEWAYS_BALANCE=0.5

# --- 自然交易模拟 ---
# 休息概率 - 跳过执行的概率 (10%)
AK_REST_PROBABILITY=0.1

# 爆发概率 - 连续多次执行的概率 (5%)
AK_BURST_PROBABILITY=0.05

# 爆发次数范围 - 爆发时的连续执行次数
AK_BURST_COUNT_MIN=2
AK_BURST_COUNT_MAX=4

# 大订单比例 - 大订单占总订单的比例 (30%)
AK_LARGE_ORDER_RATIO=0.3

# 大订单规模倍数 - 大订单相对基础订单的倍数范围
AK_LARGE_ORDER_MULTIPLIER_MIN=1.5
AK_LARGE_ORDER_MULTIPLIER_MAX=2.5

# --- 精度配置 ---
# 价格精度 (小数位数，系统规定)
AK_PRICE_PRECISION=8

# 数量精度 (小数位数，系统规定)
# 注意：SDOG/USDT要求订单数量必须是整数（0位小数）
AK_QTY_PRECISION=0

# --- API配置 ---
# Nine API凭证 (使用通用配置 NINE_API_KEY 和 NINE_API_SECRET)
# 如果需要独立的API凭证，可以设置：
# AK_NINE_API_KEY=your_dedicated_api_key
# AK_NINE_API_SECRET=your_dedicated_api_secret

# 订单簿获取精度参数
AK_NINE_POOL_PRECISION_API_PARAM=0.01

# --- 执行时间配置 ---
# 基础执行间隔 (秒) - 会叠加随机性
AK_BASE_INTERVAL=1

# 最小间隔限制 (秒) - 防止执行过于频繁
AK_MIN_INTERVAL=5

# 最大间隔限制 (秒) - 防止执行间隔过长
AK_MAX_INTERVAL=30

# --- 风险控制配置 ---
# 单次最大订单价值 (USDT) - 防止单笔订单过大
AK_MAX_SINGLE_ORDER_VALUE=100

# 每小时最大交易次数 - 防止过度交易
AK_MAX_TRADES_PER_HOUR=1000

# 价格偏差告警阈值 (5%) - 价格异常时暂停
AK_PRICE_DEVIATION_ALERT=0.05

# --- 订单管理配置 ---
# 撤单策略控制 - 是否每次都撤销所有订单
AK_CANCEL_ALL_ORDERS=false

# 订单最大存活时间 (秒) - 超过此时间的订单将被撤销
AK_ORDER_MAX_AGE=300

# 价格容忍度 - 订单价格偏离当前策略目标的最大容忍比例
AK_PRICE_TOLERANCE=0.03


# =============================================================================
# 💡 策略详细说明
# =============================================================================

# 🎯 拉盘模式 (bull) 详解:
# 
# 交易比例: 70%买单 + 30%卖单
# 买单策略: 在当前价格或略高价格挂单，持续推动价格上涨
# 卖单策略: 在更高价格挂单，模拟获利了结，避免涨幅过于生硬
# 
# 示例执行流程:
# 1. 70%概率: 在买一价或略高0.1-0.3%价格下买单
# 2. 30%概率: 在当前价格+1-2%价格下卖单
# 3. 买单数量相对较大(120-180%)，卖单数量相对较小(60-90%)

# 🎯 砸盘模式 (bear) 详解:
#
# 交易比例: 30%买单 + 70%卖单  
# 卖单策略: 在当前价格或略低价格挂单，持续推动价格下跌
# 买单策略: 在较低价格挂单，模拟抄底行为
#
# 示例执行流程:
# 1. 70%概率: 在卖一价或略低0.1-0.3%价格下卖单
# 2. 30%概率: 在当前价格-1-2%价格下买单
# 3. 卖单数量相对较大(120-180%)，买单数量相对较小(60-90%)

# 🎯 横盘模式 (sideways) 详解:
#
# 交易比例: 50%买单 + 50%卖单，动态平衡
# 挂单策略: 在当前价格上下小范围(±0.2-0.8%)均匀分布订单
# 维持目标: 通过平衡的买卖压力保持价格在震荡区间
#
# 示例执行流程:
# 1. 50%概率: 在当前价格-0.2-0.8%范围下买单
# 2. 50%概率: 在当前价格+0.2-0.8%范围下卖单  
# 3. 买卖单数量相对平衡(80-120%)

# 🎲 自然交易模拟机制:
#
# 时间随机化:
# - 基础间隔45秒 ± 30%随机波动 (约31-58秒)
# - 10%概率"休息"跳过本次执行
# - 5%概率"爆发"连续执行2-4次
#
# 数量随机化:
# - 基础数量 ± 40%随机波动
# - 70%概率生成小订单，30%概率生成大订单
# - 大订单规模为基础订单的1.5-2.5倍
#
# 价格策略:
# - 不总是使用最优价格，添加±0.5%的价格偏移
# - 部分订单立即成交，部分订单等待成交
# - 根据订单簿深度动态调整挂单策略

# =============================================================================
# 🎮 使用建议
# =============================================================================

# 🔰 测试配置 (小资金测试):
# AK_ORDER_VALUE=0.2
# AK_MAX_SINGLE_ORDER_VALUE=5
# AK_BASE_INTERVAL=60

# ⚖️ 标准配置 (推荐):
# AK_ORDER_VALUE=0.5  
# AK_MAX_SINGLE_ORDER_VALUE=10
# AK_BASE_INTERVAL=45

# 🚀 激进配置 (大资金运作):
# AK_ORDER_VALUE=2.0
# AK_MAX_SINGLE_ORDER_VALUE=50
# AK_BASE_INTERVAL=30

# 🎯 订单管理模式说明:
#
# AK_CANCEL_ALL_ORDERS=true (传统模式):
# - 每次执行都撤销所有现有订单
# - 确保策略纯净性，所有订单都符合当前模式
# - 可能造成短暂的流动性真空
#
# AK_CANCEL_ALL_ORDERS=false (智能模式):  
# - 只撤销不符合当前策略的订单
# - 保留仍然有效的订单，提供持续流动性
# - 基于订单存活时间和价格容忍度进行智能管理

# =============================================================================
# ⚠️ 风险提示
# =============================================================================
# - 拉盘模式需要充足的USDT余额
# - 砸盘模式需要充足的基础代币余额
# - 建议先小资金测试各种模式的效果
# - 注意市场深度，避免对薄弱市场造成过大冲击
# - 合理设置风险控制参数，避免异常情况下的损失

# =============================================================================