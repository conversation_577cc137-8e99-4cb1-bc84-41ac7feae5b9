# =============================================================================
# 跨交易所套利策略配置 (CrossExchangeArbitrageStrategy)
# =============================================================================
# 功能：在Nine CEX和Binance间寻找价格差异进行套利
# 适用：价格差异套利，跨交易所流动性平衡
# 特色：动态数量计算 + 滑点保护 + IOC订单检查

# --- API配置 ---
ARB_NINE_API_KEY=your_nine_api_key_here
ARB_NINE_API_SECRET=your_nine_api_secret_here
# Binance API将使用全局配置中的BINANCE_API_KEY和BINANCE_API_SECRET

# --- 交易对配置 ---
ARB_NINE_SYMBOL=SEPETH                   # Nine CEX交易对
ARB_BINANCE_SYMBOL=SEPETH                # Binance交易对

# --- 费率配置 ---
ARB_NINE_FEE_RATE=0.001                  # Nine CEX手续费率(0.1%)
ARB_BINANCE_FEE_RATE=0.001               # Binance手续费率(0.1%)

# --- 套利阈值配置 ---
ARB_MIN_PROFIT_PCT=0.002                 # 最小利润率阈值(0.2%)
ARB_SLIPPAGE_FACTOR=0.0005               # 滑点保护因子(0.05%)

# --- 动态数量计算配置 ---
ARB_ORDER_AMOUNT_MODE=adaptive           # fixed=固定数量, percentage=按盘口百分比, adaptive=自适应
ARB_ORDER_AMOUNT_BASE=0.001              # 基础数量(fixed模式使用，adaptive模式作为基准)
ARB_ORDER_AMOUNT_PERCENTAGE=0.1          # 盘口数量百分比(percentage模式使用，10%盘口数量)
ARB_ORDER_AMOUNT_MAX=0.01                # 最大单次交易数量限制
ARB_ORDER_AMOUNT_MIN=0.0001              # 最小单次交易数量限制

# --- 风险控制配置 ---
ARB_MAX_PENDING_ORDERS=5                 # 最大待处理订单数
ARB_ORDER_TIMEOUT_SECONDS=300            # 订单超时时间(秒)
ARB_BINANCE_IOC_CHECK_TIMEOUT=5          # Binance IOC订单检查超时(秒)

# --- Binance精度配置 ---
ARB_BINANCE_PRICE_PRECISION=8            # Binance价格精度
ARB_BINANCE_QTY_PRECISION=6              # Binance数量精度
ARB_BINANCE_MIN_BASE_QTY=0.0001          # Binance最小交易数量

# --- Nine CEX配置 ---
ARB_NINE_POOL_PRECISION_API_PARAM=0.01   # Nine CEX订单簿精度参数

# 启动：flask start-trading-bot --strategy cross_exchange_arbitrage
# 注意：需要同时配置Nine CEX和Binance的API密钥