# =============================================================================
# GradientLiquidityStrategy (GL) 配置 - 梯度流动性支撑策略
# =============================================================================
# 🎯 核心功能：为指定交易对提供分层流动性支撑
# 📊 工作原理：在当前价格周围分层挂单，成交后动态补充并推动价格
# 🔄 动态特性：订单成交后往后推移价格，而不是简单的盘口插入
# =============================================================================

# --- 基础配置 ---
# 交易对（与AK策略分离，避免冲突）
GL_TRADING_PAIR=SDOG/USDT

# 最大总资金使用 (USDT) - 所有挂单的总金额不超过此值
GL_MAX_TOTAL_FUND=500

# --- 梯度配置 ---
# 梯度等级数量 - 在价格两侧分几层挂单
GL_GRADIENT_LEVELS=5

# 基础价差比例 - 第一层订单距离中心价格的比例
GL_BASE_SPREAD_RATIO=0.01

# 价差递增倍数 - 每层价差的递增倍数
# 例如：第1层1%，第2层2%，第3层4%
GL_SPREAD_MULTIPLIER=1.5

# --- 订单数量配置 ---
# 基础订单金额 (USDT) - 每个订单的基本金额
GL_BASE_ORDER_VALUE=0.5

# 每层最大订单数 - 每个梯度等级最多挂几个订单
GL_MAX_ORDERS_PER_LEVEL=1

# 数量随机性 - 订单数量的随机波动范围 (±30%)
GL_QUANTITY_RANDOMNESS=0.3

# --- 价格推动配置 ---
# 启用价格推动 - 所有订单成交后推动目标价格
GL_PRICE_PUSH_ENABLED=true

# 推动步长比例 - 每次推动价格的比例
GL_PUSH_STEP_RATIO=0.005

# --- 精度配置 ---
# 价格精度 (小数位数，系统规定)
GL_PRICE_PRECISION=8

# 数量精度 (小数位数，系统规定)
# 注意：SDOG/USDT要求订单数量必须是整数（0位小数）
GL_QTY_PRECISION=0

# --- 执行时间配置 ---
# 策略更新间隔 (秒) - 梯度流动性策略专用
# 建议值：
# - 5秒: 高频活跃模式，API安全频率（每分钟最多12次调用）
# - 10秒: 高频模式，适合活跃交易和快速响应
# - 15秒: 平衡模式，兼顾响应速度和系统负载 (推荐)
# - 30秒: 保守模式，适合稳定市场
GL_UPDATE_INTERVAL=3

# --- 高频活跃模式配置 ---
# 启用高频活跃模式 - 让订单簿一直"动起来"
# true: 每5秒刷新部分订单，创造高活跃度（API安全频率）
# false: 传统梯度模式，订单静态挂单
GL_ACTIVE_MODE=true

# 每次刷新的订单数量 - 每个周期取消并重新创建多少个订单
GL_REFRESH_PER_CYCLE=5

# 订单最大存活时间(秒) - 超过此时间的订单将被强制刷新
GL_MAX_ORDER_AGE=30

# 价格抖动幅度 - 订单价格的随机波动范围(±0.1%)
GL_PRICE_JITTER=0.001

# 活跃模式最大订单数 - 高频模式下同时维持的最大订单数
GL_MAX_ACTIVE_ORDERS=10

# 价格波动频率 - 正弦波动的频率系数，越大波动越快
GL_WAVE_FREQUENCY=0.1

# --- 价格安全检查配置 ---
# 最大价格偏离度 - 订单价格与市场价格的最大偏离比例 (默认50%)
GL_MAX_PRICE_DEVIATION=0.5

# 买单价格上限比例 - 买单价格不能超过市场价的比例 (默认110%)
GL_BUY_LIMIT_RATIO=1.1

# 卖单价格下限比例 - 卖单价格不能低于市场价的比例 (默认90%)
GL_SELL_LIMIT_RATIO=0.9

# 最小价格限制 - 订单价格的绝对最小值 (防止价格过低)
GL_MIN_PRICE_LIMIT=0.000001

# 波动幅度减小系数 - 实际波动相对于配置波动的比例 (默认20%)
GL_JITTER_REDUCTION_FACTOR=0.2

# 最大波动范围 - 单次波动的最大偏移比例 (默认0.5%)
GL_MAX_WAVE_RANGE=0.005

# =============================================================================
# 💡 策略详细说明
# =============================================================================

# 🎯 梯度布局示例（假设当前价格 0.00003000）:
# 
# 第3层买单: 0.00002832 (-5.6%)  |  第3层卖单: 0.00003169 (+5.6%)
# 第2层买单: 0.00002888 (-3.7%)  |  第2层卖单: 0.00003113 (+3.7%)  
# 第1层买单: 0.00002925 (-2.5%)  |  第1层卖单: 0.00003075 (+2.5%)
#                    当前价格: 0.00003000
#
# 每层最多2个订单，数量随机变化±30%

# 🔄 动态补充机制:
# 1. 监控所有挂单状态
# 2. 订单完全成交后，在相同等级重新挂单
# 3. 所有订单都成交时，推动目标价格（价格引导）
# 4. 不在盘口插入，而是维持梯度结构

# ⏱️ 执行节奏控制:
# - 每15秒检查一次订单状态和价格变化
# - 比默认60秒更频繁，适合流动性策略的快速响应需求
# - 根据市场活跃度可调整：活跃市场用10秒，冷市场用30秒

# 📊 资金分配策略:
# - 远离价格的等级使用更大订单（提供深度）
# - 每层权重递增：第1层0.3，第2层0.5，第3层0.7
# - 总资金使用不超过设定限制

# 🎲 随机性设计:
# - 订单数量：基础数量 ± 30% 随机变化
# - 订单价格：基础价格 ± 0.1% 微调
# - 避免机械化特征，模拟真实交易行为

# =============================================================================
# ⚙️ 配置建议
# =============================================================================

# 🔰 保守配置 (适合小资金):
# GL_MAX_TOTAL_FUND=200
# GL_GRADIENT_LEVELS=2  
# GL_BASE_ORDER_VALUE=30
# GL_MAX_ORDERS_PER_LEVEL=2

# ⚖️ 平衡配置 (推荐):
# GL_MAX_TOTAL_FUND=500
# GL_GRADIENT_LEVELS=5
# GL_BASE_ORDER_VALUE=50  
# GL_MAX_ORDERS_PER_LEVEL=2

# 🚀 激进配置 (适合大资金):
# GL_MAX_TOTAL_FUND=1000
# GL_GRADIENT_LEVELS=5
# GL_BASE_ORDER_VALUE=80
# GL_MAX_ORDERS_PER_LEVEL=3

# =============================================================================
# 🎯 适用场景
# =============================================================================
# ✅ 项目方做市：为自己的代币提供流动性支撑
# ✅ 价格引导：通过订单成交推动价格向指定方向移动  
# ✅ 深度提供：在价格周围提供充足的买卖深度
# ✅ 市场稳定：减少价格剧烈波动，提供缓冲

# 🚨 注意事项:
# - 这是做市策略，不是投机策略
# - 需要持有足够的基础代币和USDT
# - 适合在相对稳定的价格区间运行
# - 建议先小资金测试，熟悉后再加大资金

# =============================================================================
# 🚀 高频活跃模式详细说明
# =============================================================================

# 🎯 活跃模式效果:
# - 订单簿持续有买卖活动，每2秒都有变化
# - 价格和数量动态波动，模拟真实交易者
# - 创造"繁忙市场"的视觉效果

# ⚙️ 工作原理:
# 1. 每2秒执行一次策略
# 2. 每次取消2个最老的订单
# 3. 立即生成2个新订单（价格微调±0.1%）
# 4. 30秒内所有订单完全轮换一遍

# 🔧 活跃模式配置示例:
# GL_ACTIVE_MODE=true       # 启用活跃模式
# GL_UPDATE_INTERVAL=2      # 2秒执行一次
# GL_REFRESH_PER_CYCLE=2    # 每次刷新2个订单
# GL_MAX_ORDER_AGE=30       # 订单最大寿命30秒
# GL_PRICE_JITTER=0.001     # 价格抖动±0.1%
# GL_MAX_ACTIVE_ORDERS=20   # 维持20个订单

# 📊 性能影响:
# - API调用控制在720次/小时以内（安全频率）
# - 内置API频率保护机制，每分钟最多30次调用
# - 最小1秒调用间隔，防止突发请求
# - 可能产生少量额外手续费

# ⚠️ 使用建议:
# - 适合需要"表演活跃度"的场景
# - 测试时先用小金额
# - 内置API频率保护，自动限制调用频率
# - 确保有足够余额支持高频操作
# - 如需更高频率，建议先联系平台确认API限制

# 🛡️ 安全保护:
# - 模式切换时自动清理旧数据，避免冲突
# - API频率限制：每分钟最多30次，每次间隔至少1秒
# - 过载保护：达到限制时自动跳过执行

# =============================================================================