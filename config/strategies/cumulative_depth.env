# =============================================================================
# 累积深度策略配置 (CumulativeDepthStrategy)
# =============================================================================
# 功能：基于累积深度计算目标价格，提供深度流动性
# 适用：深度流动性提供，基于资金池累积深度进行智能下单
# 特色：累积深度分析 + 价格调整机制 + 最小价差控制

# --- API配置 ---
CDS_NINE_API_KEY=your_nine_api_key_here
CDS_NINE_API_SECRET=your_nine_api_secret_here

# --- 基础配置 ---
CDS_TRADING_PAIR=SEPBTC/USDT             # 要操作的交易对
CDS_INTERVAL_SECONDS=2                   # 策略执行间隔(秒)

# --- 订单金额配置 ---
CDS_ORDER_AMOUNT_BASE=0.00001            # 买卖订单的基础币种数量(例如BTC数量)
CDS_MIN_ORDER_QTY_BASE=0.00001           # 允许的最小下单数量(基础币种单位)

# --- 累积深度配置 ---
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=8        # 计算买单价格时，目标累积买盘数量(基础币种)
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=8       # 计算卖单价格时，目标累积卖盘数量(基础币种)

# --- 价格调整配置 ---
CDS_PRICE_ADJUSTMENT_BUY=0.01            # 买价调整(正数向上调整价格)
CDS_PRICE_ADJUSTMENT_SELL=-0.01          # 卖价调整(负数向下调整价格)

# --- 价差控制配置 ---
CDS_MIN_SPREAD_PRICE=1.5                 # 目标买卖价之间的最小价差(计价货币单位)
CDS_SPREAD_ADJUSTMENT_BUY=-10            # 价差过小时对买价的调整(通常为负以扩大价差)
CDS_SPREAD_ADJUSTMENT_SELL=10            # 价差过小时对卖价的调整(通常为正以扩大价差)

# --- 精度配置 ---
CDS_PRICE_PRECISION=2                    # 价格精度(小数点后位数)
CDS_QTY_PRECISION=5                      # 数量精度(小数点后位数)

# --- 深度分析配置 ---
CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN=20     # 从资金池获取数据时扫描的最大档位数

# 启动：flask start-trading-bot --strategy cumulative_depth
# 适用场景：需要基于订单簿深度进行智能流动性提供的情况