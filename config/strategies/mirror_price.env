# =============================================================================
# 价格搬平策略配置 (MirrorPriceStrategy)
# =============================================================================
# 功能：从任意价格源（CEX/DEX）同步价格到Nine CEX
# 适用：新币价格引导、套利平衡、价格维护

# --- API配置 ---
# 注意：优先使用根目录 .env 中的通用 NINE_API_KEY，如果没有则使用下面的专用配置
# MP_NINE_API_KEY=5bbbd9e3417842e6b378300cb5ae56b3
# MP_NINE_API_SECRET=c1fa08418f4441f4839d3d561031dd35

# --- 价格源配置 ---
MP_PRICE_SOURCE=raydium                    # 价格源: binance/raydium
MP_SOURCE_SYMBOL=二哈/USDT                    # 源交易对 (Binance格式)
MP_TARGET_SYMBOL=二哈/USDT             # Nine CEX目标交易对
MP_RAYDIUM_CONTRACT=ASRNzwDpYWseimGQh3GootWLphpuMe3cUnpoH4mFbonk                       # Raydium合约地址 (可选，raydium源时使用)

# --- 同步参数 ---
MP_PRICE_OFFSET=0.005                          # 目标价格偏移 (-0.01表示比源价格低1%)
MP_SYNC_AMOUNT=10                          # 推动订单金额 (USDT)
MP_UPDATE_INTERVAL=30                      # 策略执行间隔 (秒)

# --- 精度配置 ---
MP_PRICE_PRECISION=4                       # 价格精度 (小数点后位数)
MP_QTY_PRECISION=2                         # 数量精度 (小数点后位数)

# --- 使用示例 ---
# 1. 跟随Binance BTC价格:
#    MP_PRICE_SOURCE=binance, MP_SOURCE_SYMBOL=BTCUSDT, MP_PRICE_OFFSET=0
#
# 2. 比Binance价格低1%:
#    MP_PRICE_SOURCE=binance, MP_SOURCE_SYMBOL=BTCUSDT, MP_PRICE_OFFSET=-0.01
#
# 3. 跟随Raydium DEX价格:
#    MP_PRICE_SOURCE=raydium, MP_RAYDIUM_CONTRACT=xxx, MP_PRICE_OFFSET=0

# 启动：flask start-trading-bot --strategy mirror_price