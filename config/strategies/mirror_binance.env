# =============================================================================
# 镜像币安策略配置 (MirrorBinanceStrategy)
# =============================================================================
# 功能：镜像Binance订单簿到Nine CEX进行做市
# 适用：为Nine CEX提供流动性，基于Binance价格做市

# --- API配置 ---
# 注意：优先使用根目录 .env 中的通用 NINE_API_KEY，如果没有则使用下面的专用配置
MM_NINE_API_KEY=5bbbd9e3417842e6b378300cb5ae56b3
MM_NINE_API_SECRET=c1fa08418f4441f4839d3d561031dd35

# --- 交易对配置 ---
MM_NINE_CEX_SYMBOL=SEPBTC/SEPUSDT        # Nine CEX交易对
MM_BINANCE_SYMBOL=BTCUSDT                # Binance参考交易对

# --- 核心参数 ---
MM_SPREAD_PERCENTAGE=0.0001              # 价差百分比 (0.01% = 万分之一)
MM_QUANTITY_COEFFICIENT=0.2              # 数量系数 (20%的币安数量)
MM_UPDATE_INTERVAL=60                    # 更新间隔 (秒)

# --- 精度配置 ---
MM_PRICE_PRECISION=2                     # 价格精度 (小数点后位数)
MM_QTY_PRECISION=5                       # 数量精度 (小数点后位数)
MM_BINANCE_DEPTH_LEVELS=5               # 订单簿档位数

# --- 优化配置 ---
MM_TRADE_DETAIL_CACHE_TTL=30            # 成交详情缓存时间(秒)
MM_CANCEL_UNFILLED_ONLY=true            # 只撤销未成交订单
MM_CANCEL_PARTIAL_THRESHOLD=0.1         # 部分成交撤单阈值
MM_ORDER_TIMEOUT_SECONDS=300            # 订单超时时间(秒)
MM_PRICE_DEVIATION_THRESHOLD=0.005      # 价格偏离阈值(0.5%)

# 启动：flask start-trading-bot --strategy mirror_binance