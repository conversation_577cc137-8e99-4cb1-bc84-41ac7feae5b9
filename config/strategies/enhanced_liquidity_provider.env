# =============================================================================
# 增强型流动性提供策略配置 (EnhancedLiquidityProviderStrategy) 
# =============================================================================
# 功能：专为meme币设计的高级做市+拉盘出货一体化策略
# 适用：控制大部分筹码的新代币，实现从基础价拉升至5x以上目标
# 特色：三层架构(底部托单+流动性网格+拉盘通道) + 四阶段智能降温

# --- API配置 ---
ELP_NINE_API_KEY=your_nine_api_key_here
ELP_NINE_API_SECRET=your_nine_api_secret_here

# --- 运行模式配置 ---
ELP_OPERATING_MODE=manual                # manual=手动指定交易对, auto=自动监控新交易对
ELP_MANUAL_PAIRS=NINE/SEPUSDT,SEPBTC/SEPUSDT  # 手动模式下的交易对(逗号分隔)

# --- 资金配置 (三层架构) ---
ELP_TOTAL_USDT=3000                      # 总可用USDT资金量

# --- 价格策略配置 ---
ELP_BASE_PRICE=0.0000161                 # AMM代币基础价格(固定)
ELP_PRICE_MULTIPLIERS=1.5,2.0,3.0,4.0,5.0  # 价格倍数配置
ELP_PRICE_MODE=fixed                     # fixed=固定价格模式, market=市场模式

# --- 订单配置 ---
ELP_ORDERS_PER_LEVEL=3                   # 每个价格等级的订单层数
ELP_PRICE_PRECISION=8                    # 价格精度
ELP_QTY_PRECISION=6                      # 数量精度

# --- 卖出机制配置 ---
ELP_SELL_FREQUENCY=300                   # 定期卖出频率(秒)
ELP_SELL_PERCENTAGE=0.01                 # 每次卖出代币余额百分比(1%)
ELP_MIN_SELL_AMOUNT=100                  # 最小卖出金额

# --- 🚀 拉盘出货配置 (核心功能) ---
ELP_ENABLE_PUMP_DUMP=true                # 启用拉盘出货功能
ELP_PUMP_TARGET_RATIO=5.0                # 拉盘目标倍数(5x = 500%)
ELP_PUMP_TRIGGER_PRICE=0.0000161         # 拉盘触发价格

# --- 🎯 智能拉盘配置 ---
ELP_PUMP_MAX_INVESTMENT=2000             # 拉盘最大投入(USDT)
ELP_PUMP_BUY_INTERVAL=30                 # 主动买入间隔(秒)
ELP_PUMP_BUY_STEP_RATIO=0.05             # 每次买入推高幅度(5%)
ELP_PUMP_EFFICIENCY_THRESHOLD=0.6        # 拉盘效率阈值(60%)

# --- 🎨 买入策略模式 ---
ELP_PUMP_BUY_MODE=balanced               # natural=自然化, balanced=平衡, aggressive=激进

# --- 🎨 订单薄深度控制 (核心创新) ---
# 阶段1: 超薄深度，100-200u轻松突破
ELP_DEPTH_STAGE1_TOKENS=50,100,200,150
ELP_DEPTH_STAGE1_RATIOS=1.1,1.2,1.3,1.4

# 阶段2: 正常深度，需要努力但可控  
ELP_DEPTH_STAGE2_TOKENS=500,800,600
ELP_DEPTH_STAGE2_RATIOS=1.8,2.0,2.2

# 阶段3: 出货深度，平衡出货不砸盘
ELP_DEPTH_STAGE3_TOKENS=1000,1200,1000
ELP_DEPTH_STAGE3_RATIOS=2.8,3.2,3.6

# 阶段4: 防护深度，防止过度拉升
ELP_DEPTH_STAGE4_TOKENS=2000,3000,4000
ELP_DEPTH_STAGE4_RATIOS=4.5,5.0,5.5

# --- 🌊 密集流动性网格 (智能做市) ---
ELP_GRID_CORE_BUDGET_RATIO=0.25          # 核心区域预算比例(±0.5%内)
ELP_GRID_TIGHT_BUDGET_RATIO=0.15         # 紧密区域预算比例(0.5-1.5%)
ELP_GRID_STANDARD_BUDGET_RATIO=0.08      # 标准区域预算比例(1.5-3%)
ELP_GRID_OUTER_BUDGET_RATIO=0.05         # 外围区域预算比例(3-5%)

# 各区域价格偏移设置
ELP_CORE_OFFSETS=-0.005,-0.003,-0.001,0.001,0.003,0.005
ELP_TIGHT_OFFSETS=-0.015,-0.01,-0.008,0.008,0.01,0.015  
ELP_STANDARD_OFFSETS=-0.03,-0.025,-0.02,0.02,0.025,0.03
ELP_OUTER_OFFSETS=-0.05,-0.04,0.04,0.05

# --- 💰 动态出货配置 (核心盈利机制) ---
ELP_DUMP_PRICE_RATIOS=1.3,1.8,2.5,3.5,4.5      # 出货价格倍数
ELP_DUMP_RATIOS=0.05,0.08,0.12,0.15,0.10        # 出货数量比例
ELP_DUMP_SUPPORT_AMOUNTS=200,300,400,500,300    # 出货支撑资金(USDT)

# --- 🌡️ 四阶段智能降温 (防止利润回吐) ---
ELP_COOLING_BUY_RATIOS=1.2,0.9,0.7,1.0          # 降温期买单比例
ELP_COOLING_SELL_RATIOS=1.5,1.2,1.0,1.0         # 降温期卖单比例  
ELP_COOLING_DURATIONS=60,90,120,0                # 降温阶段持续时间(秒)

# --- 🛡️ 风险管理配置 ---
ELP_EXTERNAL_VOLUME_THRESHOLD=1.5        # 外部买盘检测阈值(150%)
ELP_EMERGENCY_STOP_LOSS=0.2              # 紧急停止损失比例(20%)
ELP_MAX_STRATEGY_DURATION=7200           # 最大运行时间(2小时)

# --- 📊 分阶段拉盘配置 ---
ELP_PUMP_STAGE_TARGETS=1.5,2.5,4.0,5.5  # 分阶段目标倍数
ELP_PUMP_STAGE_PAUSE=60                  # 每阶段间隔时间(秒)
ELP_PUMP_FOMO_MULTIPLIER=1.5             # FOMO阶段资金增强倍数

# --- 🔧 深度控制微调 ---
ELP_PUMP_SELL_WALL_REMOVAL=0.8           # 拉盘时移除卖单比例(80%)
ELP_PUMP_BUY_WALL_BOOST=1.5              # 拉盘时买单增强倍数
ELP_DUMP_SELL_WALL_RESTORE=2.0           # 出货时卖单墙倍数
ELP_PUMP_PHASE_DURATION=300              # 拉盘阶段持续时间(秒)
ELP_DUMP_BATCH_SIZE=0.1                  # 分批出货比例(10%)

# 启动：flask start-trading-bot --strategy enhanced_liquidity_provider
# 警告：此策略功能强大，请谨慎使用，建议先测试模式运行