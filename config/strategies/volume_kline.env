# =============================================================================
# 成交量K线策略配置 (VolumeKlineStrategy)
# =============================================================================
# 功能：定时定量刷量，增加交易对成交量
# 适用：基于Nine CEX自身订单簿进行交易

# --- API配置 ---
VK_NINE_API_KEY=your_nine_api_key_here
VK_NINE_API_SECRET=your_nine_api_secret_here

# --- 基础配置 ---
VK_TRADING_PAIR=NINE/USDT                # 交易对
VK_INTERVAL_SECONDS=60                   # 策略执行间隔 (秒)

# --- 交易配置 ---
VK_ORDER_AMOUNT=0.01                     # 每次下单的基础币种数量
VK_MIN_TRADE_QTY_ASSET=0.01              # 最小允许的交易数量

# --- 精度配置 ---
VK_PRICE_PRECISION=2                     # 价格精度
VK_QTY_PRECISION=2                       # 数量精度

# --- 功能开关 ---
VK_ENABLE_PERIODIC_TRADE_CHECK=true      # 启用定期成交检测

# 启动：flask start-trading-bot --strategy volume_kline