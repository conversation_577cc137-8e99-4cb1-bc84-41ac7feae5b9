# =============================================================================
# 流动性提供策略配置 (LiquidityProviderStrategy) 
# =============================================================================
# 功能：提供深度流动性，支持多层订单和智能撤单
# 适用：为交易对提供流动性，稳定价格波动

# --- API配置 ---
LP_NINE_API_KEY=your_nine_api_key_here
LP_NINE_API_SECRET=your_nine_api_secret_here

# --- 资金配置 ---
LP_INITIAL_USDT=3000                    # 初始USDT资金量
LP_COIN_SELL_PERCENTAGE=0.15            # 代币卖出比例 (15%)

# --- 深度优化配置 ---
LP_DEPTH_LEVELS=5                       # 挂单层数
LP_SPREAD_BASE=0.002                    # 基础价差 (0.2%)
LP_SPREAD_INCREMENT=0.001               # 价差递增幅度 (0.1%)
LP_SPREAD_RANDOMNESS=0.0003             # 价差随机性 (±0.03%)

# --- 订单大小配置 ---
LP_ORDER_SIZE_PERCENTAGE=0.1            # 单订单占资金比例 (10%)
LP_ORDER_SIZE_MODE=reverse              # normal=递减, reverse=大单托底
LP_ORDER_SIZE_FACTOR=0.8                # 大小变化系数
LP_MIN_ORDER_AMOUNT=10                  # 最小订单金额 (USDT)

# --- 时间行为配置 ---
LP_SELL_FREQUENCY=300                   # 卖单频率 (5分钟)
LP_SELL_AMOUNT_PERCENTAGE=0.01          # 定期卖出比例 (1%)
LP_UPDATE_INTERVAL=60                   # 策略执行间隔 (秒)

# --- 随机化配置 (拟人化) ---
LP_TIME_RANDOMNESS_FACTOR=0.3           # 时间间隔随机性 (±30%)
LP_QUANTITY_RANDOMNESS_FACTOR=0.05      # 数量随机性 (±5%)
LP_PARTIAL_CANCEL_PROBABILITY=0.1       # 部分撤单概率 (10%)
LP_REORDER_DELAY_MIN=5                  # 重挂延迟最小值 (秒)
LP_REORDER_DELAY_MAX=30                 # 重挂延迟最大值 (秒)
LP_ENABLE_BEHAVIOR_RANDOMIZATION=true   # 启用行为随机化
LP_ORDER_LIFETIME_MIN=300               # 订单最短生命周期 (秒)
LP_ORDER_LIFETIME_MAX=1800              # 订单最长生命周期 (秒)

# --- 精度配置 ---
LP_PRICE_PRECISION=2                    # 价格精度
LP_QTY_PRECISION=5                      # 数量精度

# 启动：flask start-trading-bot --strategy liquidity_provider