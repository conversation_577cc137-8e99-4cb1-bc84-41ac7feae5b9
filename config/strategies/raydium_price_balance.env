# =============================================================================
# Raydium链上价格平衡策略配置 (RaydiumPriceBalanceStrategy)  
# =============================================================================
# 目标：CEX价格低于DEX价格1%，为新币拉升创造有利环境
# 功能：获取Raydium DEX链上价格，自动调整CEX价格至目标位置
# 注意：API密钥从根目录 .env 文件中统一读取

# --- 交易对配置 ---
RPB_TRADING_PAIR=USELESS/USDT                # CEX交易对
RPB_TRADING_PAIR_ID=20250620000002        # CEX交易对ID (备用)
RPB_CONTRACT_ADDRESS=Dz9mQ9NzkBcCsuGPFJ3r1bS4wgqKMHBPiVuniW8Mbonk  # Solana代币合约地址

# --- 💡 核心策略参数 ---
RPB_TARGET_PRICE_OFFSET=-0.01             # CEX比DEX低1% (新币拉升策略)
RPB_PRICE_TOLERANCE=0.002                 # 价格容差(0.2%，目标价格±0.2%可接受)
RPB_AGGRESSIVE_THRESHOLD=0.01             # 激进阈值(1%，偏差>1%激进调整)

# --- 交易配置 (适配500U资金规模) ---
RPB_BASE_ORDER_AMOUNT=0.2                   # 基础订单金额(USDT)
RPB_MAX_ORDER_AMOUNT=1                   # 最大单笔订单金额(USDT)
RPB_MIN_ORDER_AMOUNT=0.1                  # 最小订单金额(USDT)

# --- 流动性配置 ---
RPB_LIQUIDITY_LEVELS=5                    # 流动性层数
RPB_LIQUIDITY_AMOUNT_PER_LEVEL=0.5          # 每层流动性金额(USDT)
RPB_INITIAL_SPREADS=0.003,0.006,0.012     # 多层价差: 0.3%, 0.6%, 1.2%

# --- 推动交易配置 ---
RPB_SMALL_TRADE_AMOUNT=1                  # 小额推动金额(USDT)
RPB_MEDIUM_TRADE_AMOUNT=3                 # 中等推动金额(USDT)

# --- 精度配置 ---
RPB_PRICE_PRECISION=8                     # 价格精度(小数点后位数)
RPB_QTY_PRECISION=2                       # 数量精度(小数点后位数)

# --- 系统配置 ---
RPB_UPDATE_INTERVAL=20                    # 策略执行间隔(秒)
RPB_PRICE_LIMIT_FACTOR=9.9                # 990%价格限制(Nine系统限制)

# --- 订单管理配置 ---
RPB_MAX_ORDERS=30                         # 触发清理的最大订单数
RPB_KEEP_ORDERS=20                        # 清理时保留的最新订单数

# 启动：flask start-trading-bot --strategy raydium_price_balance
# 注意：需要Solana RPC节点访问权限