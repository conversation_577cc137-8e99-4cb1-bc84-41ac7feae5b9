# 内盘API接口文档

## 概述

内盘API提供了对Nine CEX内盘交易功能的访问，包括获取内盘信息和执行内盘交易。所有内盘相关的接口都在 `/api/inner-pool` 路径下。

## 接口列表

### 1. 获取内盘信息

**接口路径**: `GET /api/inner-pool/info`

**描述**: 批量获取内盘池子信息，包括代币信息、流动性、价格、市值等详细数据。

#### 请求参数

| 参数名 | 类型 | 是否必需 | 默认值 | 描述 |
|-------|------|----------|--------|------|
| apiKey | string | 是 | - | 用户API密钥 |
| secret | string | 是 | - | 用户API密钥 |
| currentPage | integer | 否 | 1 | 当前页码 |
| pageSize | integer | 否 | 20 | 每页数量 |
| chain | string | 否 | - | 链名称，如 "Solana" |

#### 请求示例

```bash
curl -X GET "http://localhost:5001/api/inner-pool/info?apiKey=your_api_key&secret=your_secret&currentPage=1&pageSize=20&chain=Solana"
```

#### 响应示例

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "records": [
      {
        "id": 17,
        "assetId": 1162,
        "chain": "solana",
        "chainImage": "https://nine-community-dev.s3.ap-southeast-1.amazonaws.com/uploads/1744800418219_sepoliachain.png",
        "cover": "https://nine-prod-file.s3.ap-southeast-1.amazonaws.com/uploads/1751099009111_post_image.jpg",
        "tokenSymbol": "ERDAI",
        "stableCoinSymbol": "USDT",
        "tokenAmount": 332358638,
        "stableCoinAmount": 2978.71,
        "poolStatus": "ACTIVE",
        "ticker": "ERDAI",
        "totalSupply": 1000000000,
        "preMc": 1010,
        "liquidity": 1989974.8742132,
        "createdTime": 1751225491000,
        "process": 65.95,
        "change24h": 787.13,
        "price": 8.96E-6,
        "volume24h": 3874.28,
        "marketCap": 8960.0,
        "isExternal": 0,
        "isStop": 0,
        "countPerson": 1021,
        "topTenPercentage": 82,
        "tx24h": 752
      }
    ],
    "total": 12,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 批量内盘交易

**接口路径**: `POST /api/inner-pool/trade`

**描述**: 执行批量内盘交易操作，支持买入(BUY)和卖出(SELL)操作。

#### 请求体格式

请求体应该是一个数组，每个元素包含用户认证信息和交易详情：

```json
[
  {
    "userBean": {
      "apiKey": "your_api_key",
      "secret": "your_secret"
    },
    "innerTradeBean": [
      {
        "poolId": 1,
        "amount": "1.0",
        "tradeType": "BUY"
      },
      {
        "poolId": 1,
        "amount": "2.0",
        "tradeType": "SELL"
      }
    ]
  }
]
```

#### 参数说明

- **userBean**: 用户认证信息
  - `apiKey`: 用户API密钥
  - `secret`: 用户API密钥
  
- **innerTradeBean**: 交易详情数组
  - `poolId`: 内盘池子ID（数字）
  - `amount`: 交易数量（字符串格式）
  - `tradeType`: 交易类型，"BUY" 或 "SELL"

#### 请求示例

```bash
curl -X POST "http://localhost:5001/api/inner-pool/trade" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "userBean": {
        "apiKey": "your_api_key",
        "secret": "your_secret"
      },
      "innerTradeBean": [
        {
          "poolId": 1,
          "amount": "1",
          "tradeType": "BUY"
        }
      ]
    }
  ]'
```

#### 响应示例

```json
{
  "code": 200,
  "msg": "成功",
  "data": [
    {
      "email": null,
      "phone": null,
      "password": null,
      "apiKey": "your_api_key",
      "secret": "your_secret",
      "data": {
        "code": 200,
        "data": {
          "poolId": 1,
          "userId": 45,
          "tradeType": "BUY",
          "tokenAmount": 530667.98,
          "stableCoinAmount": 1,
          "price": 1.88E-6,
          "tradeTimestamp": 1746682244921
        }
      }
    }
  ]
}
```

## 错误处理

所有接口都采用统一的错误响应格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | 描述 |
|-------|------|
| 400 | 请求参数错误 |
| 503 | Nine CEX客户端未初始化或服务不可用 |
| 500 | 内部服务器错误 |

## 使用注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要在客户端代码中硬编码。

2. **参数验证**: 系统会对所有请求参数进行严格验证，请确保参数格式正确。

3. **交易类型**: 内盘交易只支持 "BUY" 和 "SELL" 两种类型。

4. **数量格式**: 交易数量必须以字符串格式传递，确保精度不丢失。

5. **池子ID**: 请确保传递的 poolId 是有效的内盘池子ID。

6. **批量操作**: 两个接口都支持批量操作，可以在一次请求中处理多个操作。

## 示例代码

### Python 示例

```python
import requests

# 获取内盘信息
def get_inner_pool_info(api_key, secret, page=1, size=20, chain=None):
    url = "http://localhost:5001/api/inner-pool/info"
    params = {
        "apiKey": api_key,
        "secret": secret,
        "currentPage": page,
        "pageSize": size
    }
    if chain:
        params["chain"] = chain
    
    response = requests.get(url, params=params)
    return response.json()

# 执行内盘交易
def inner_pool_trade(api_key, secret, pool_id, amount, trade_type):
    url = "http://localhost:5001/api/inner-pool/trade"
    data = [
        {
            "userBean": {
                "apiKey": api_key,
                "secret": secret
            },
            "innerTradeBean": [
                {
                    "poolId": pool_id,
                    "amount": str(amount),
                    "tradeType": trade_type
                }
            ]
        }
    ]
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    API_KEY = "your_api_key"
    SECRET = "your_secret"
    
    # 获取内盘信息
    pool_info = get_inner_pool_info(API_KEY, SECRET, chain="Solana")
    print("内盘信息:", pool_info)
    
    # 执行买入交易
    trade_result = inner_pool_trade(API_KEY, SECRET, 1, 1.0, "BUY")
    print("交易结果:", trade_result)
```

### JavaScript 示例

```javascript
// 获取内盘信息
async function getInnerPoolInfo(apiKey, secret, page = 1, size = 20, chain = null) {
    const url = new URL('http://localhost:5001/api/inner-pool/info');
    url.searchParams.append('apiKey', apiKey);
    url.searchParams.append('secret', secret);
    url.searchParams.append('currentPage', page);
    url.searchParams.append('pageSize', size);
    if (chain) {
        url.searchParams.append('chain', chain);
    }
    
    const response = await fetch(url);
    return await response.json();
}

// 执行内盘交易
async function innerPoolTrade(apiKey, secret, poolId, amount, tradeType) {
    const url = 'http://localhost:5001/api/inner-pool/trade';
    const data = [
        {
            userBean: {
                apiKey: apiKey,
                secret: secret
            },
            innerTradeBean: [
                {
                    poolId: poolId,
                    amount: amount.toString(),
                    tradeType: tradeType
                }
            ]
        }
    ];
    
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
    
    return await response.json();
}

// 使用示例
(async () => {
    const API_KEY = 'your_api_key';
    const SECRET = 'your_secret';
    
    try {
        // 获取内盘信息
        const poolInfo = await getInnerPoolInfo(API_KEY, SECRET, 1, 20, 'Solana');
        console.log('内盘信息:', poolInfo);
        
        // 执行买入交易
        const tradeResult = await innerPoolTrade(API_KEY, SECRET, 1, 1.0, 'BUY');
        console.log('交易结果:', tradeResult);
    } catch (error) {
        console.error('请求失败:', error);
    }
})();
``` 