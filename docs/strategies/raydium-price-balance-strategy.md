# Raydium Price Balance Strategy (链上价格平衡策略)

## 概述
RaydiumPriceBalanceStrategy 是一个创新的跨平台价格平衡策略，通过实时监控 Raydium DEX（Solana 链上去中心化交易所）的价格，自动调整 CEX 中心化交易所的价格，确保两个市场价格保持一致。

## 核心功能

### 1. 实时价格监控
- 从 Raydium DEX 获取链上实时价格
- 监控 CEX 订单簿中间价
- 计算价格偏差并触发相应操作

### 2. 智能价格平衡
- **价格偏低**：自动吃掉卖盘，提高 CEX 价格至链上水平
- **价格偏高**：智能砸买盘，降低 CEX 价格至链上水平
- **价格对齐**：在平衡价格两侧提供多层流动性

### 3. 风险控制
- 价格容差机制，避免频繁调整
- 最大订单金额限制，控制单笔风险
- 分层挂单，平滑价格波动

## 工作原理

```
链上价格获取 → 价格比较 → 偏差判断 → 执行策略
    ↓              ↓           ↓            ↓
Raydium API    CEX订单簿   容差检查    吃单/砸盘/做市
```

### 价格平衡逻辑
1. **获取基准价格**：从 Raydium 获取链上价格作为基准
2. **检查CEX状态**：
   - 订单簿为空：初始化模式（创建初始流动性）
   - 有订单存在：计算价格偏差
3. **执行操作**：
   - 订单簿空：初始化模式（按链上价格创建5层深度）
   - 偏差 < -0.2%：提价模式（吃卖盘）
   - 偏差 > 0.2%：降价模式（砸买盘）
   - |偏差| ≤ 0.2%：流动性模式（双边挂单）

## 配置说明

### 必需配置
```bash
# API密钥
RPB_NINE_API_KEY=your_api_key
RPB_NINE_API_SECRET=your_api_secret

# 交易对
RPB_TRADING_PAIR=BTC/USDT
```

### 价格控制参数
```bash
# 价格容差：0.2%以内视为已对齐
RPB_PRICE_TOLERANCE=0.002

# 激进阈值：超过1%采用激进策略
RPB_AGGRESSIVE_THRESHOLD=0.01
```

### 交易参数
```bash
# 基础订单金额：100 USDT
RPB_BASE_ORDER_AMOUNT=100

# 最大单笔订单：1000 USDT
RPB_MAX_ORDER_AMOUNT=1000

# 吃单倍数：2倍基础金额
RPB_EAT_ORDER_MULTIPLIER=2
```

### 流动性参数
```bash
# 流动性价差：0.1%
RPB_LIQUIDITY_SPREAD=0.001

# 流动性层数：5层
RPB_LIQUIDITY_LEVELS=5

# 每层金额：50 USDT
RPB_LIQUIDITY_AMOUNT_PER_LEVEL=50
```

## 使用方法

### 1. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件，设置 RPB 相关参数
vim .env
```

### 2. 启动策略
```bash
# 激活虚拟环境
source nine-trade-env/bin/activate

# 启动策略（实际运行）
flask start-trading-bot --strategy raydium_price_balance

# 模拟运行（不下单）
flask start-trading-bot --strategy raydium_price_balance --dry-run

# 单次执行
flask run-bot-once --strategy raydium_price_balance
```

### 3. 监控日志
策略运行时会输出详细日志：
```
🌊 链上价格: BTC/USDT = 65000.00
📈 链上价格: 65000, CEX价格: 64800, 偏差: -0.31%
⬆️ 提价操作: 吃掉 3 个卖单，总成本 500 USDT
💧 流动性布局: 在 65000 附近挂 10 个订单
```

## 策略优势

### 1. 跨平台套利
- 利用 DEX 和 CEX 价格差异
- 自动捕捉套利机会
- 双向价格平衡

### 2. 流动性提供
- 价格对齐后自动做市
- 多层订单提供深度
- 赚取交易手续费

### 3. 市场初始化
- **解决CEX初期没有挂单问题**
- 自动按链上价格创建初始深度
- 5层递增流动性（0.1%-2.0%）
- 总投入1340 USDT创建完整市场

### 4. 风险可控
- 设置最大订单限制
- 价格容差避免频繁操作
- 分层挂单分散风险

## 注意事项

### 1. 网络要求
- 需要稳定的网络连接
- Raydium API 可能有访问限制
- 建议使用 VPS 部署

### 2. 资金要求
- 需要足够的 USDT 进行价格平衡
- 需要持有交易对的基础币种
- 建议准备 3000+ USDT

### 3. 风险提示
- 链上价格可能剧烈波动
- 大额订单可能造成滑点
- 需要实时监控策略运行

## 性能优化

### 1. 缓存机制
- Raydium 价格缓存 5 秒
- 减少 API 调用频率
- 提高响应速度

### 2. 批量操作
- 批量下单减少延迟
- 批量撤单提高效率
- 优化订单管理

### 3. 智能判断
- 动态调整吃单力度
- 根据深度决定订单大小
- 避免过度影响市场

## 常见问题

### Q1: 无法获取 Raydium 价格？
- 检查网络连接
- 确认交易对是否存在于 Raydium
- 查看 API 是否正常

### Q2: 价格一直无法对齐？
- 调整价格容差参数
- 增加基础订单金额
- 检查是否有外部干扰

### Q3: 策略消耗资金过快？
- 降低最大订单金额
- 增加价格容差
- 减少流动性层数

## 策略收益预期

- **套利收益**：价差 × 成交量
- **做市收益**：交易手续费返佣
- **预期日收益**：2-5%（取决于市场波动）
- **风险等级**：中等

## 更新日志

### v1.0.0 (2024-01)
- 初始版本发布
- 支持 Raydium 价格获取
- 实现价格平衡机制
- 添加流动性提供功能