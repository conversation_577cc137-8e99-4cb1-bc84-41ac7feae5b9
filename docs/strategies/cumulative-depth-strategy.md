# CumulativeDepthStrategy - 累积深度策略

## 📊 策略概述

**CumulativeDepthStrategy（累积深度策略）** 是一个基于累积深度计算的高级做市策略。该策略通过分析Nine CEX订单簿的累积深度，计算达到指定成交金额所需的价格水平，然后在这些关键价位提供流动性支撑。

### 🎯 核心目标
- 基于深度分析提供智能定价
- 为大额交易提供流动性支撑
- 通过深度策略获取更好的价格优势

### ⭐ 复杂度等级
**⭐⭐⭐⭐** (复杂) - 需要深度市场分析能力

## 🔧 工作原理

### 基本流程
```mermaid
graph TD
    A[开始执行] --> B[获取Nine CEX订单簿]
    B --> C[撤销现有订单]
    C --> D[分析买单方向累积深度]
    D --> E[分析卖单方向累积深度]
    E --> F[计算目标买价]
    F --> G[计算目标卖价]
    G --> H[价差检查和调整]
    H --> I[下达买卖订单]
    I --> J[等待下次执行]
    J --> A
```

### 详细逻辑

#### 1. **累积深度分析**
```python
# 买单方向分析
累积数量 = 0
for 价格, 数量 in 订单簿买单:
    累积数量 += 数量
    if 累积数量 >= 目标累积金额:
        目标买价 = 价格 + 买单价格调整
        break
```

#### 2. **价格计算**
- 扫描订单簿直到累积数量达到配置阈值
- 在累积深度价格基础上应用价格调整
- 确保买卖价格不会交叉

#### 3. **价差保护**
```python
if (卖价 - 买价) <= 最小价差:
    买价 += 买单价差调整
    卖价 += 卖单价差调整
```

#### 4. **智能回退**
- 如果无法找到合适的累积深度，使用最佳价格作为回退
- 确保在任何市场条件下都能提供流动性

## ⚙️ 配置参数

### 必需配置

```bash
# === 策略专用API密钥 ===
CDS_NINE_API_KEY=your_cds_api_key         # Nine CEX API密钥
CDS_NINE_API_SECRET=your_cds_api_secret   # Nine CEX API秘钥

# === 交易配置 ===
CDS_TRADING_PAIR=BTC/USDT                 # 目标交易对
CDS_ORDER_AMOUNT_BASE=0.001               # 下单数量（基础资产）

# === 累积深度配置 ===
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=1000      # 买单累积深度金额阈值
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=1000     # 卖单累积深度金额阈值

# === 价格调整配置 ===
CDS_PRICE_ADJUSTMENT_BUY=0.01             # 买单价格调整
CDS_PRICE_ADJUSTMENT_SELL=-0.01           # 卖单价格调整

# === 价差保护配置 ===
CDS_MIN_SPREAD_PRICE=100                  # 最小价差保护
CDS_SPREAD_ADJUSTMENT_BUY=-50             # 买单价差调整
CDS_SPREAD_ADJUSTMENT_SELL=50             # 卖单价差调整

# === 时间和精度配置 ===
CDS_INTERVAL_SECONDS=60                   # 执行间隔（秒）
CDS_PRICE_PRECISION=2                     # 价格精度
CDS_QTY_PRECISION=6                       # 数量精度
```

### 可选配置

```bash
# === 高级配置 ===
CDS_MIN_ORDER_QTY_BASE=0.02               # 最小订单数量
CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN=20      # 最大扫描深度层数
```

## 📈 实际运行示例

### 场景假设
```
Nine CEX BTC/USDT 订单簿:
买单 (bids): 
  [49000, 0.1], [48900, 0.2], [48800, 0.5], [48700, 1.0], [48600, 2.0]

卖单 (asks):
  [50000, 0.1], [50100, 0.3], [50200, 0.8], [50300, 1.5], [50400, 2.0]

配置:
  CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=1000 USDT
  CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=1000 USDT
  CDS_PRICE_ADJUSTMENT_BUY=0.01
  CDS_PRICE_ADJUSTMENT_SELL=-0.01
```

### 执行过程

#### 步骤1: 买单深度分析
```python
累积计算:
49000 * 0.1 = 4.9 USDT      (累积: 4.9)
48900 * 0.2 = 9.78 USDT     (累积: 14.68)
48800 * 0.5 = 24.4 USDT     (累积: 39.08)
48700 * 1.0 = 48.7 USDT     (累积: 87.78)
48600 * 2.0 = 97.2 USDT     (累积: 184.98)
... 继续扫描直到累积 >= 1000 USDT

目标买价 = 深度价格 + 0.01
```

#### 步骤2: 卖单深度分析
```python
累积计算:
50000 * 0.1 = 5.0 USDT      (累积: 5.0)
50100 * 0.3 = 15.03 USDT    (累积: 20.03)
50200 * 0.8 = 40.16 USDT    (累积: 60.19)
... 继续扫描直到累积 >= 1000 USDT

目标卖价 = 深度价格 - 0.01
```

#### 步骤3: 价差检查
```python
if (目标卖价 - 目标买价) <= 100:
    目标买价 += (-50)  # 降低买价
    目标卖价 += 50     # 提高卖价
```

## 💡 策略特点

### ✅ 优势
1. **深度感知**: 基于真实市场深度做决策
2. **大单支撑**: 为大额交易提供合理定价
3. **价格优势**: 在关键支撑/阻力位提供流动性
4. **智能定价**: 考虑市场容量的定价策略

### ⚠️ 风险和注意事项
1. **计算复杂**: 需要扫描和分析多个价格层级
2. **市场依赖**: 严重依赖订单簿的完整性和准确性
3. **参数敏感**: 累积深度阈值设置需要谨慎
4. **延迟风险**: 深度分析可能增加执行延迟

### 📊 适用场景
- **机构做市**: 为机构交易者提供深度流动性
- **大额交易**: 支撑大额订单的成交
- **价格发现**: 基于深度的智能价格发现
- **波动期**: 在市场波动期提供稳定锚点

## 🚀 快速开始

### 1. 理解深度概念
```bash
# 累积深度阈值应该根据交易对的特性设置
# BTC/USDT: 建议 1000-10000 USDT
# ETH/USDT: 建议 500-5000 USDT
# 小币种: 建议 100-1000 USDT
```

### 2. 配置参数
```bash
# 保守设置（大额支撑）
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=5000
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=5000
CDS_ORDER_AMOUNT_BASE=0.1

# 激进设置（频繁交易）
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=1000  
CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=1000
CDS_ORDER_AMOUNT_BASE=0.01
```

### 3. 测试运行
```bash
# 模拟运行观察价格计算
flask run-bot-once --strategy cumulative_depth --dry-run
```

### 4. 正式启动
```bash
flask start-trading-bot --strategy cumulative_depth
```

## 📊 监控和优化

### 关键指标
- **深度覆盖率**: 订单是否在合理的深度位置
- **成交效率**: 订单在深度位置的成交情况
- **价差维护**: 买卖价差是否保持合理
- **计算延迟**: 深度分析的计算时间

### 参数调优

#### 增加保守性
```bash
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=10000    # 更深的深度要求
CDS_MIN_SPREAD_PRICE=200                 # 更大的价差保护
CDS_INTERVAL_SECONDS=120                 # 更长的更新间隔
```

#### 增加敏感性
```bash
CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=500      # 更浅的深度要求
CDS_MIN_SPREAD_PRICE=50                  # 更小的价差保护  
CDS_INTERVAL_SECONDS=30                  # 更短的更新间隔
```

## 🔧 故障排除

### 常见问题

#### 1. 无法找到合适深度
```
⚠️ 无法确定有效的买卖价格
```
**解决方案**: 降低累积深度阈值或增加扫描层数

#### 2. 价格交叉
```
⚠️ 目标价格交叉 买:X ≥ 卖:Y
```
**解决方案**: 调整价格调整参数或增加价差保护

#### 3. 订单被拒绝
```
❌ 调整后价格无效
```
**解决方案**: 检查精度配置和最小订单数量设置

## 📝 最佳实践

### 1. 参数设置策略
- 根据交易对日均成交量设置累积深度阈值
- 价格调整幅度应小于平均价差的50%
- 最小价差应大于交易所手续费的2倍

### 2. 风险管理
- 定期检查深度计算的准确性
- 监控市场异常情况下的表现
- 设置合理的资金上限

### 3. 性能优化
- 选择流动性充足的交易对
- 根据市场活跃度调整扫描深度
- 定期评估策略的深度覆盖效果

---

**⚠️ 风险提示**: 本策略基于复杂的深度分析，建议在充分理解累积深度概念后使用。请在测试环境验证参数设置的合理性。

*文档更新时间: 2025年1月* 