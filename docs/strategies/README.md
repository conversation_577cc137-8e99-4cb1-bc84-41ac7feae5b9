# Nine Trade Maker 交易策略文档

本目录包含了Nine Trade Maker支持的所有交易策略的详细文档。每个策略都有独立的文档文件，包含工作原理、配置说明、使用示例和最佳实践。

## 📚 策略列表

| 策略名称 | 文档文件 | 适用场景 | 复杂度 |
|---------|---------|----------|--------|
| [镜像币安策略](./mirror-binance-strategy.md) | `mirror-binance-strategy.md` | 做市、套价 | ⭐⭐⭐ |
| [流动性提供策略](./liquidity-provider-strategy.md) | `liquidity-provider-strategy.md` | 深度流动性、多层做市 | ⭐⭐⭐⭐ 🔥 |
| [成交量K线策略](./volume-kline-strategy.md) | `volume-kline-strategy.md` | 增加成交量、提供流动性 | ⭐⭐ (v2.1优化) |
| [累积深度策略](./cumulative-depth-strategy.md) | `cumulative-depth-strategy.md` | 深度流动性、大单支撑 | ⭐⭐⭐⭐ |
| [跨交易所套利策略](./cross-exchange-arbitrage-strategy.md) | `cross-exchange-arbitrage-strategy.md` | 价差套利 | ⭐⭐⭐⭐⭐ |

## 🎯 策略选择指南

### 刷量场景
- **成交量K线策略**:  适合定时定量主动成交刷量

### 做市商场景
- **镜像币安策略**: 基于主流交易所价格，提供稳定流动性
- **流动性提供策略**: 🔥 多层订单支持，具备强大的随机化拟人功能，最佳的深度流动性方案
- **累积深度策略**: 基于深度计算，适合大额交易支撑

### 高级用户
- **跨交易所套利策略**: 需要同时操作两个交易所，风险和收益并存

## 📋 通用配置

所有策略都需要配置以下基础参数：

```bash
# Nine CEX 基础配置
NINE_API_URL=https://api.nine.com
NINE_ACCOUNT_TYPE=1
NINE_ORDER_TYPE_LIMIT=1
NINE_ORDER_DIR_BUY=1
NINE_ORDER_DIR_SELL=2

# 币安配置（部分策略需要）
ENABLE_BINANCE_CLIENT=true
USE_BINANCE_TESTNET=true
BINANCE_TESTNET_API_URL=https://testnet.binance.vision
BINANCE_TESTNET_API_KEY=your_testnet_key
BINANCE_TESTNET_API_SECRET=your_testnet_secret
```

## 🚀 快速开始

1. **选择策略**: 根据你的需求选择合适的策略
2. **阅读文档**: 详细阅读对应策略的文档
3. **配置参数**: 根据文档配置相应的环境变量
4. **测试运行**: 使用 `--dry-run` 模式测试
5. **正式运行**: 确认无误后正式启动

```bash
# 测试运行
flask run-bot-once --strategy [策略名] --dry-run

# 正式运行
flask start-trading-bot --strategy [策略名]
```

## 📊 策略对比

| 特性 | 镜像币安 | 流动性提供 | 成交量K线 | 累积深度 | 跨交易所套利 |
|------|----------|------------|-----------|----------|-------------|
| 外部数据依赖 | ✅ 币安API | ❌ 无 | ❌ 无 | ❌ 无 | ✅ 币安API |
| 风险等级 | 中等 | 中低 | 低 | 中等 | 高 |
| 收益潜力 | 中等 | 中高 | 低 | 中等 | 高 |
| 配置复杂度 | 中等 | 中等 | 简单 | 复杂 | 复杂 |
| 资金要求 | 中等 | 中等 | 低 | 高 | 高 |
| 拟人化程度 | 低 | 🔥 极高 | 低 | 低 | 低 |
| 多层订单 | ❌ | ✅ 1-10层 | ❌ | ❌ | ❌ |
| 随机化功能 | ❌ | ✅ 8项 | ❌ | ❌ | ❌ |

## ⚠️ 风险提示

1. **资金安全**: 所有策略都涉及真实资金交易，请在充分理解后使用
2. **测试环境**: 建议先在测试网络充分测试
3. **监控运行**: 生产环境需要持续监控
4. **参数调优**: 根据市场情况调整策略参数
5. **止损机制**: 设置合理的风险控制措施

## 📞 技术支持

- 遇到问题请查看对应策略的详细文档
- 检查日志输出获取错误信息
- 确保API密钥配置正确
- 验证网络连接稳定性

---

*最后更新: 2025年1月* 